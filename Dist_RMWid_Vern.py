class ESUD:
    def __init__(self):
        self.obj_chain = None  # 相当于 pcObj<PERSON><PERSON><PERSON>

    def dist_rm_wid_vern(self, sched):
        """
        将粗轧（RM）的宽度游标（Vernier）分配到粗轧各道次（Drafting Stands）的宽度传感器和机架

        Args:
            sched: 调度对象，包含轧制计划相关信息

        Returns:
            bool: 操作是否成功
        """
        # 局部变量
        iseg = self.obj_chain.body_index()
        ps = 0
        entry_side = True

        # 检查出口反馈 - 适用于FM入口导板的情况
        if sched.wrk.get("wrk_exit_fbk", 0) > 0:
            print(f"{sched.obj_name()} bypass Dist_RMWid_Vern, call is after RMX for F0 Edger FFWD")
            return True

        # 使用RM宽度游标初始化机架和宽度规传感器的宽度偏移
        for ps in range(sched.setup_d.lstpas + 1):
            # 机架
            if sched.sup_pass_d[ps].std_d[iseg] is not None:
                sched.sup_pass_d[ps].std_d[iseg].wid_offset = 0.0

            # 宽度规传感器
            try:
                obj = sched.sup_pass_d[ps].en_pce_d[iseg]
                entry_side = True

                while obj is not None and obj != sched.sup_pass_d[ps].ex_pce_d[iseg]:
                    # 检查是否为宽度传感器对象
                    if obj.get_class_name().lower() == "cwidthsensord":
                        width_sensor = obj
                        
                        if entry_side:
                            if ps <= 1:
                                width_sensor.wid_offset = 0.0
                            else:
                                if (sched.ramp.adapted_state.family_prv != 
                                    sched.rapp.state.family):
                                    width_sensor.wid_offset = (
                                        sched.ramp.state.rmx_wid_vern * 
                                        (1 - sched.rapp.state.rwid_off_mult) +
                                        sched.rapp.state.rmx_wid_off * 
                                        sched.rapp.state.rwid_off_mult
                                    )
                                else:
                                    width_sensor.wid_offset = sched.ramp.state.rmx_wid_vern
                        else:
                            if (sched.ramp.adapted_state.family_prv != 
                                sched.rapp.state.family):
                                width_sensor.wid_offset = (
                                    sched.ramp.state.rmx_wid_vern * 
                                    (1 - sched.rapp.state.rwid_off_mult) +
                                    sched.rapp.state.rmx_wid_off * 
                                    sched.rapp.state.rwid_off_mult
                                )
                            else:
                                width_sensor.wid_offset = sched.ramp.state.rmx_wid_vern
                    
                    # 检查是否为机架对象，用于切换entry_side标志
                    elif obj.get_class_name().lower() == "cstdd":
                        entry_side = False
                    
                    obj = obj.next_obj

            except Exception as e:
                print(f"Exception processing ESUD::Dist_RMWid_Vern(), ps= {ps}")
                return False

        return True
