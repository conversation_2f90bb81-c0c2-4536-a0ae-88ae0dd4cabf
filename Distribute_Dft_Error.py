class ESUD:
    def Distribute_Dft_Error(self, pcSupPassD, fstpas, lstpas, dft_error):
        """
        分配轧制误差到指定的道次。

        参数:
            pcSupPassD (list): 道次列表
            fstpas (int): 起始道次
            lstpas (int): 结束道次
            dft_error (float): 需要分配的轧制误差

        注意:
            1. 轧制量将根据负载值添加到现有的道次轧制量中
            2. 待分配的轧制误差可以是正值或负值
            3. 如果需要增加轧制量，只包含未达到最大轧制限制的道次
            4. 如果需要减少轧制量，只包含未达到最小轧制限制的道次
            5. 如果没有可用的轧制容量，将返回有效状态给调用者。这种情况稍后会被标记为过度轧制或轧制不足

        返回:
            bool: 分配状态
        """
        # 局部变量初始化
        iseg = self.pcObjChain.Body_Index()
        dfterr = dft_error  # 待分配的轧制误差，初始化为输入值
        
        # 迭代分配轧制误差
        for iter in range(21):  # 0-20共21次迭代
            # 计算总负载
            totload = 0.0
            
            # 根据轧制误差的正负和道次的限制情况计算总负载
            for ps in range(fstpas, lstpas + 1):
                pcEdgD = pcSupPassD[ps].Drafting_EdgD(iseg)
                if pcEdgD is not None:
                    if dft_error > 0.0:
                        # 如果轧制误差为正
                        if (pcEdgD.draft_min == pcEdgD.draft and dfterr < 0.0) or \
                           (pcEdgD.draft_max == pcEdgD.draft and dfterr > 0.0):
                            continue
                        elif pcEdgD.draft < pcEdgD.draft_max:
                            totload += pcEdgD.load_act
                    elif pcEdgD.draft > pcEdgD.draft_min:
                        totload += pcEdgD.load_act
                    else:
                        # 设置轧制量为最小值
                        pcEdgD.draft = pcEdgD.draft_min
            
            # 检查总负载
            if totload <= 0.0:
                # 输出警告信息：所有轧制道次都达到限制
                print(f"{pcSupPassD[0].pcExPceD[iseg].pcPce.prod_id} All Edging passes at limits, "
                      f"ESU status= {pcEdgD.Image(self.esu_status)}")
                return True
            
            # 按负载比例分配轧制误差
            ddraft = dfterr
            
            # 遍历所有道次分配轧制误差
            for ps in range(fstpas, lstpas + 1):
                pcEdgD = pcSupPassD[ps].Drafting_EdgD(iseg)
                if pcEdgD is not None and \
                   ((pcEdgD.draft != pcEdgD.draft_max and ddraft > 0.0) or
                    (pcEdgD.draft != pcEdgD.draft_min and ddraft < 0.0)):
                    
                    # 计算当前道次的轧制量
                    dft = ddraft * pcEdgD.load_act / totload
                    
                    # 检查与上一次迭代的残余轧制量
                    if (dft < 0.0 and pcEdgD.res_dft > 0.0) or \
                       (dft > 0.0 and pcEdgD.res_dft < 0.0):
                        print(f"{pcSupPassD[0].pcExPceD[iseg].pcPce.prod_id}, "
                              f"dft= {dft}, res_dft= {pcEdgD.res_dft}, ps= {ps}")
                        
                        if abs(dft) > abs(pcEdgD.res_dft):
                            dft += pcEdgD.res_dft
                            pcEdgD.res_dft = 0.0
                        else:
                            pcEdgD.res_dft += dft
                    
                    # 计算新的轧制量
                    draft = dft + pcEdgD.draft
                    
                    # 检查轧制量是否超出限制
                    if draft > pcEdgD.draft_max:
                        pcEdgD.res_dft = draft - pcEdgD.draft_max
                        dft -= pcEdgD.res_dft
                        draft = pcEdgD.draft_max
                    elif draft < pcEdgD.draft_min:
                        pcEdgD.res_dft = draft - pcEdgD.draft_min
                        dft -= pcEdgD.res_dft
                        draft = pcEdgD.draft_min
                    
                    pcEdgD.draft = draft
                    dfterr = dfterr - dft
            
            print(f"{pcSupPassD[0].pcExPceD[iseg].pcPce.prod_id}, "
                  f"dft_error= {dft_error}, dfterr= {dfterr}, iter= {iter}")
            
            # 检查是否成功分配
            if abs(dfterr) < self.pcESU.accuracy:
                return True
        
        # 迭代失败处理
        for ps in range(fstpas, lstpas + 1):
            pcEdgD = pcSupPassD[ps].Drafting_EdgD(iseg)
            if pcEdgD is not None:
                print(f"{pcSupPassD[0].pcExPceD[iseg].pcPce.prod_id} "
                      f"ps= {ps}, dMin= {pcEdgD.draft_min}, "
                      f"draft= {pcEdgD.draft}, dMax= {pcEdgD.draft_max}, "
                      f"res_dft= {pcEdgD.res_dft}")
        
        # 输出错误信息
        print(f"{pcSupPassD[0].pcExPceD[iseg].pcPce.prod_id} "
              f"ESU::Distribute_Dft_Error: unable to distribute draft")
        print(f"{pcSupPassD[0].pcExPceD[iseg].pcPce.prod_id} "
              f"ESU::Distribute_Dft_Error: dfterr={dfterr}, "
              f"accuracy= {self.pcESU.accuracy}")
        
        return False
