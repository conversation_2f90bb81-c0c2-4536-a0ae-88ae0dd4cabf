//-----------------------------------------------------------------------------
//
// ABSTRACT:
//     This file defines the static and dynamic stand object methods for a
//     basic finishing mill stand
//
//
//     FUNCTION/PROCEDURE/TASK  DESCRIPTION
//     -----------------------  -----------------------------------------------
//   static
//     cStd                     Stand object constructor
//     cStd                     Stand object copy constructor
//     cStd                     Stand object destructor
//     operator=                assignment operator
//     operator==               equivalence operator
//     operator<                less than operator
//     linkObj                  Method for linking stand to other objects
//     dump                     dump member data and/or composed objects
//   dynamic
//     cStdD                    Stand object constructor
//     cStdD                    Stand object copy constructor
//     ~cStdD                   Stand object destructor
//     operator=                assignment operator
//     operator==               equivalence operator
//     operator<                less than operator
//     linkObj                  Method for linking stand to other objects
//     rbheat                   Method for calculating roll temperature change
//                                 from friction power
//
//-----------------------------------------------------------------------------
#include <cstdlib>
#include "objhash.hxx"
#include "std.hxx"
#include "pce.hxx"
#include "mtr.hxx"
#ifdef ALUMINUM_HSM
   #include "physcon.hxx"
   #include "tensiometer.hxx"
   #include "tensionreel.hxx"
#else
   #include "looper.hxx"
#endif

#include "stdrollpr.hxx"
#include "rollbite.hxx"
#include "rbheat.hxx"

#include "sgp.hxx"          // SGP               - for flow stress
#include "fagp.hxx"         // AGP               - for flow stress
#include "fapp.hxx"         // APP               - for flow stress
#include "fprp.hxx"         // PRP               - for tension
#include "pdi.hxx"          // PDI               - for flow stress
#include "pdichem.hxx"      // PDIChem           - for flow stress
#include "flowstress.hxx"
#include "table.hxx"
#include "utility.hxx"
#include "mathuty.hxx"

#include "FPassFbkSC.hxx"
#include "FPassFbkMD.hxx"

#ifdef WIN32
    #ifdef _DEBUG
    #define new DEBUG_NEW
    #endif
#endif

// Data schema for the cStd class.
static cSchema::schema_type cStd_schema[]=
{
    LINK_TO_SCHEMA("cBaseStd","cBaseStd")
    { NULL, NULL, SCHEMA_T( cStd, float, pct_min_ldspd ),                "",  "pu",               "Minimum pct lead speed configured" },
    { NULL, NULL, SCHEMA_T( cStd, float, pct_max_ldspd ),                "",  "pu",               "Maximum pct lead speed configured" },
    { 0 }   // terminate list
};

// Link all the schema's together
cSchema::schema_name_type cStd::sSchema[]=
{
    {
        "cStd",                            // name
        sizeof(cStd),                      // size
        cStd_schema,                       // schema
        false,                              // packed
        true,                               // allow ptr
        false,                              // Read only
        "Static FM stand configuration",    // comment
        0                                   // offset to config data
    },

    { 0 } // terminate list
};

// Data schema for the cStdD class.
static cSchema::schema_type cStdD_schema[]=
{
    LINK_TO_SCHEMA("cBaseStdD","cBaseStdD")

    //---------------------
    // Pointer definitions.
    //---------------------
    { NULL, NULL, SCHEMA_PO( cStdD, cStd, pcStd ),                       "",  "",               "pointer to static Std object" },
    { NULL, NULL, SCHEMA_PO( cStdD, cPceD, pcEnPceD ),                   "",  "",               "pointer to dynamic ENPCE object" },
    { NULL, NULL, SCHEMA_PO( cStdD, cPceD, pcExPceD ),                   "",  "",               "pointer to dynamic EXPCE object" },
#ifdef ALUMINUM_HSM
	{ NULL, NULL, SCHEMA_PO( cStdD, cTensiometerD, pcTensiometerD ),               "",  "",     "pointer to dynamic TENSIOMETER object" },
	{ NULL, NULL, SCHEMA_PO( cStdD, cTensionreelD, pcTensionreelD ),               "",  "",     "pointer to dynamic TENSIOMETER object" },
#else
	{ NULL, NULL, SCHEMA_PO( cStdD, cLooperD, pcLooperD ),               "",  "",               "pointer to dynamic LOOPER object" },
#endif
	//------------------------------------------------
    // Saved pointer to SGP, AGP, APP and PRP
    //------------------------------------------------
    { NULL, NULL, SCHEMA_PO( cStdD, cSGP, pcSGP ),                       "",  "",               "pointer to SGP object" },
    { NULL, NULL, SCHEMA_PO( cStdD, cFAGP, pcFAGP ),                     "",  "",               "pointer to AGP object" },
    { NULL, NULL, SCHEMA_PO( cStdD, cFAPP, pcFAPP ),                     "",  "",               "pointer to APP object" },
    { NULL, NULL, SCHEMA_PO( cStdD, cFPRP, pcFPRP ),                     "",  "",               "pointer to PRP object" },
    { NULL, NULL, SCHEMA_PO( cStdD, cPDI, pcPDI ),                       "",  "",               "pointer to PDI object" },
    { NULL, NULL, SCHEMA_PO( cStdD, cPDIChem, pcPDIChem ),               "",  "",               "pointer to PDIChem object" },

    { NULL, NULL, SCHEMA_PO( cStdD, cFPassFbkSC, pcFPassFbkSC ),         "",  "",               "pointer to SC Data" },
    { NULL, NULL, SCHEMA_PO( cStdD, cFPassFbkMD, pcFPassFbkMD ),         "",  "",               "pointer to MD Data" },
    { NULL, NULL, SCHEMA_T( cStdD, float, op_tens_mult ),               "",  "",               "operator tension multiplier" },
    { NULL, NULL, SCHEMA_T( cStdD, float, mstrate_mod ),                "",  "",               "Mean strain rate modifier" },
    { NULL, NULL, SCHEMA_T( cStdD, float, limrat_thdp ),                "",  "",               "Thread power limit ratio" },
    { NULL, NULL, SCHEMA_T( cStdD, float, limrat_locone ),              "",  "",               "low cone limit ratio" },
    { NULL, NULL, SCHEMA_T( cStdD, float, limrat_hicone ),              "",  "",               "High cone limit ratio" },
    { NULL, NULL, SCHEMA_T( cStdD, float, load_ssu ),                   "",  "",               "High cone limit ratio" },
    { NULL, NULL, SCHEMA_T( cStdD, float, fsp_mult ),                   "",  "",               "Per pass flow stress multiplier" },

    { 0 }   // terminate list
};

// Link all the schema's together
cSchema::schema_name_type cStdD::sSchema[]=
{
    {
        "cStdD",                           // name
        sizeof(cStdD),                     // size
        cStdD_schema,                      // schema
        false,                              // packed
        true,                               // allow ptr
        false,                              // Read only
        "Dynamic FM stand data",               // comment
        0                                   // offset to config data
    },

    { 0 } // terminate list
};

// Diagnostic level specific to this file
static const cAlarm::DiagnosticCodeEnum diagLvl(cAlarm::Std);

//-----------------------------------------------------------------------------
// Static Stand Object
//-----------------------------------------------------------------------------
//-----------------------------------------------------------------------------
// cStd CONSTRUCTOR ABSTRACT:
//   Stand constructor
//-----------------------------------------------------------------------------
int cStd::count   (0); // initialize static members
cStd::cStd()
    : cBaseStd()
#ifdef ALUMINUM_HSM
	, pcTensiometer (NULL)
	, pcTensionreel (NULL)
#else
	, pcLooper (NULL)
#endif
{
    Set_Class_Name("cStd");
    Set_Schema("cStd",sSchema);

    // Zero out member data
    Zero_Data(this, sizeof(cStd), Get_Schema("cStd"));
}

cStd::cStd( const MString     &objectName,
              const objTypEnum stdType,
              const objPosEnum position,
                    void       *pHash)
    : cBaseStd( objectName, stdType, position, pHash)
#ifdef ALUMINUM_HSM
	, pcTensiometer (NULL)
	, pcTensionreel (NULL)
#else
	, pcLooper (NULL)
#endif
{
    Set_Class_Name("cStd");
    Set_Schema("cStd",sSchema);

    // Zero out member data
    Zero_Data(this, sizeof(cStd), Get_Schema("cStd"));
}

//-------------------------------------------------------------------------
// ~cStd ABSTRACT:
//   Stand deconstructor
//-------------------------------------------------------------------------
cStd::~cStd()
{
#ifdef ALUMINUM_HSM
	pcTensiometer = 0;
	pcTensionreel = 0;
#else
	pcLooper = 0;
#endif
}

//-------------------------------------------------------------------------
// cStd ABSTRACT:
//   Stand copy constructor
//-------------------------------------------------------------------------
cStd::cStd (const cStd& source)
    : cBaseStd( source )
#ifdef ALUMINUM_HSM
	, pcTensiometer      (0)
	, pcTensionreel      (0)
{
    if (source.pcTensiometer)     pcTensiometer     = new cTensiometer (*(source.pcTensiometer));
    if (source.pcTensionreel)     pcTensionreel     = new cTensionreel (*(source.pcTensionreel));
}
#else
	, pcLooper      (0)
{
    if (source.pcLooper)     pcLooper     = new cLooper (*(source.pcLooper));
}
#endif
//-------------------------------------------------------------------------
// OPERATOR = ABSTRACT:
//   Stand assignment operator
//-------------------------------------------------------------------------
cStd& cStd::operator= (const cStd& source)
{
    if (this != &source)
    {
        cBaseStd::operator = (source);

        num  = source.num;

        Copy_Data(this, (void *)&source,sizeof(cStd),cStd_schema);
#ifdef ALUMINUM_HSM
        if ( NULL != source.pcTensiometer )
        {
            pcTensiometer = source.pcTensiometer;
        }
        if ( NULL != source.pcTensionreel )
        {
            pcTensionreel = source.pcTensionreel;
        }
#else
        if ( NULL != source.pcLooper )
        {
            pcLooper = source.pcLooper;
        }
#endif
	}
    return (*this);
}

//-------------------------------------------------------------------------
// LINKOBJ ABSTRACT
//   Stand link to static sub-objects
//-------------------------------------------------------------------------
bool cStd::linkObj(const void        *pVoid,
                   const objTypEnum  objType,
                   const objPosEnum  objPos )
{
    bool retValue (true);
    char class_name[32];

    if (!pVoid)
    {
        EMSG << "Passed child pointer is NULL - exiting" << END_OF_MESSAGE;
        return (retValue);
    }

    // Get the class name and link base stand objects to the base stand and
    // create links for parent-child relationship
    sprintf( class_name, "%s", ((const char *)((cObj *)(pVoid))->Get_Class_Name()) );
#ifdef ALUMINUM_HSM
    if ( 0 == _stricmp("cTensiometer", class_name) )
    {
        if (pcTensiometer)
        {
            pcTensiometer = (cTensiometer*) pVoid;
            retValue = false;
        }
        else
        {
            pcTensiometer = (cTensiometer*) pVoid;
        }
    }
    if ( 0 == _stricmp("cTensionreel", class_name) )
    {
        if (pcTensionreel)
        {
            pcTensionreel = (cTensionreel*) pVoid;
            retValue = false;
        }
        else
        {
            pcTensionreel = (cTensionreel*) pVoid;
        }
    }
#else
    if ( 0 == _stricmp("cLooper", class_name) )
    {
        if (pcLooper)
        {
            pcLooper = (cLooper*) pVoid;
            retValue = false;
        }
        else
        {
            pcLooper = (cLooper*) pVoid;
        }
    }
#endif
	return cBaseStd::linkObj(pVoid, objType, objPos);

} // end cStd::linkObj()


//-------------------------------------------------------------------------
// DUMP ABSTRACT:
//   Std dump contents of the struct.
// The bool composed if true indicates that the
// object should call the dump function for the
// objects that it contains.
//-------------------------------------------------------------------------
void cStd::dump(const bool composed)
{

    // Generically dump the data
    Dump_Data(stdout, "cStd", this, 0, (const char *)objName());
    if (composed)
    {
#ifdef ALUMINUM_HSM
		if (pcTensiometer)     pcTensiometer->dump(composed);
		if (pcTensionreel)     pcTensionreel->dump(composed);
#else
		if (pcLooper)     pcLooper->dump(composed);
#endif
	}
} // end cStd::dump


// Create a new dynamic object.  Intended to be used
// where we have a static/dynamic pair so that the
// static object may create its dynamic equivalent
cBase*  cStd::Create_Dynamic_Object()
{
    cStdD   StdD;

    return StdD.Create_Object();
}

cBase*  cStd::Create_Dynamic_Object(
                    const MString&     objName,
                    const objTypEnum  objType,
                    const objPosEnum  objPosition,
                    void              *pHash,
                    const int         size,
                    void              *pVoid,
                    void              *pFbkVoid,
                    void              *pRaw,
                    void              *pAdapted)
{
    cStdD   StdD;

    return StdD.Create_Object(
                objName,
                objType,
                objPosition,
                pHash, size, pVoid);
}

//-----------------------------------------------------------------------------
// Dynamic Stand Object
//-----------------------------------------------------------------------------
//-----------------------------------------------------------------------------
// cStdD CONSTRUCTOR ABSTRACT:
//   Stand constructor
//-----------------------------------------------------------------------------
cStdD::cStdD()
    : pcStd (NULL)
#ifdef ALUMINUM_HSM
	, pcTensiometerD     (NULL)
	, pcTensionreelD     (NULL)
#else
	, pcLooperD     (NULL)
#endif
	, pcEnPceD (NULL)
    , pcExPceD (NULL)
    , pcSGP (NULL)
    , pcFAGP (NULL)
    , pcFAPP (NULL)
    , pcFPRP (NULL)
    , pcPDI(NULL)
    , pcPDIChem(NULL)
    , pcFPassFbkSC(NULL)
    , pcFPassFbkMD(NULL)
{
    Set_Class_Name("cStdD");
    Set_Schema("cStdD",sSchema);

    // Zero out member data
    Zero_Data(this, sizeof(cStdD), Get_Schema("cStdD"));

    fsp_mult = 1.0F;
}

cStdD::cStdD( const MString     &objectName,
                const objTypEnum stdType,
                const objPosEnum position,
                      void       *pHash)
    : cBaseStdD( objectName, stdType, position, pHash )
    , pcStd         (NULL)
#ifdef ALUMINUM_HSM
	, pcTensiometerD     (NULL)
	, pcTensionreelD     (NULL)
#else
	, pcLooperD     (NULL)
#endif
	, pcEnPceD (NULL)
    , pcExPceD (NULL)
    , pcSGP (NULL)
    , pcFAGP (NULL)
    , pcFAPP (NULL)
    , pcFPRP (NULL)
    , pcPDI(NULL)
    , pcPDIChem(NULL)
    , pcFPassFbkSC(NULL)
    , pcFPassFbkMD(NULL)
{
    Set_Class_Name("cStdD");
    Set_Schema("cStdD",sSchema);

    int stdNum_save = stdNum;   // Preserve stdNum from Zero_Data.

    // Zero out member data
    Zero_Data(this, sizeof(cStdD), Get_Schema("cStdD"));

    stdNum = stdNum_save;

    fsp_mult = 1.0F;

    // Create the exit piece state associated with this stand and link it
    // up to the stand
    pcExPceD = new cPceD (objectName + (MString)"_pce", ot_undef, op_undef, pHash);
    // Link the exit piece as a child of this dynamic derived stand.
    linkObj(pcExPceD, ot_undef, op_exit);

    next_obj = pcExPceD;
    pcExPceD->previous_obj = this;

    // Find the static stand object
    const char    *pName = strchr(objectName, '_') + 1;
    objHashType::ResultT    result;
    result = Objhash.cmnFind(pName);
    if (HashTableHlp::duplicate == result.status)
    {
        linkObj(result.data, ot_undef, op_undef);
    }
    else
    {
        EMSG << "No object named "
             << pName << " exists"
             << END_OF_MESSAGE;
    }

    char str_buf1[ 32 ];                        // [-] string buffer
    char str_buf2[ 32 ];                        // [-] string buffer

    //----------------------------------------------------------------------
    // Copy the name of dynamic STD object passed into the constructor into
    // the string buffer array.
    //----------------------------------------------------------------------
    strncpy( str_buf1, ( const char* )( objectName ), sizeof(str_buf1) );

    //-------------------------------------------------------------------
    // Find the position of the underscore in the string buffer array and
    // terminate the string after the underscore.  Dynamic names are mm#_SSSS
    // where SSSS is the static objects name as registered inthe hash tables
    // # is the dynamic objects number (pass 0 etc) and mm is some number
    // of characters used to identify the type of this instance such as
    // su for setup, fb feedback hdsu for head setup...
    //-------------------------------------------------------------------
    *( strchr( str_buf1, '_' ) + 1 ) = 0;
#ifdef ALUMINUM_HSM
    //--------------------------------------------------------------------
    // Create a dynamic tensiometer if a static tensiometer is on this stand
    //--------------------------------------------------------------------
    if ((NULL != pcStd) && (NULL != pcStd->pcTensiometer))
    {
        sprintf( str_buf2, "%s%s", str_buf1, ( const char* )( pcStd->pcTensiometer->objName() ) );
        pcTensiometerD =  new cTensiometerD (str_buf2, ot_undef, op_undef, pHash);

        // Link the tensiometer as a child of this dynamic derived stand.
        linkObj(pcTensiometerD, ot_undef, op_undef);
    }
    if ((NULL != pcStd) && (NULL != pcStd->pcTensionreel))
    {
        sprintf( str_buf2, "%s%s", str_buf1, ( const char* )( pcStd->pcTensionreel->objName() ) );
        pcTensionreelD =  new cTensionreelD (str_buf2, ot_undef, op_undef, pHash);

        // Link the tensionreel as a child of this dynamic derived stand.
        linkObj(pcTensionreelD, ot_undef, op_undef);
    }
#else
    //--------------------------------------------------------------------
    // Create a dynamic looper if a static looper is on this stand
    //--------------------------------------------------------------------
    if ((NULL != pcStd) && (NULL != pcStd->pcLooper))
    {
        sprintf( str_buf2, "%s%s", str_buf1, ( const char* )( pcStd->pcLooper->objName() ) );
        pcLooperD =  new cLooperD (str_buf2, ot_undef, op_undef, pHash);

        // Link the looper as a child of this dynamic derived stand.
        linkObj(pcLooperD, ot_undef, op_undef);
    }
#endif
} // end cStdD::cStdD

//-------------------------------------------------------------------------
// ~cStdD ABSTRACT:
//   Stand deconstructor
//-------------------------------------------------------------------------
cStdD::~cStdD()
{

}

void cStdD::zeroData(void)
{
    // Zero out member data

    Zero_Data(this, sizeof(cStdD), Get_Schema("cStdD"));

    cBaseStdD::zeroData();
}

//-------------------------------------------------------------------------
// LINKOBJ ABSTRACT
//   Stand link to dynamic sub-objects
//-------------------------------------------------------------------------
bool cStdD::linkObj( const void       *pVoid,
                     const objTypEnum objType,
                     const objPosEnum objPos )
{
    cBase* pcBase = ( cBase* )pVoid;        // [-] pointer to BASE object

    bool retValue (true);
    char    class_name[32];

    if (!pVoid)
    {
        EMSG << "Passed child pointer is NULL - exiting" << END_OF_MESSAGE;
        return (retValue);
    }

    // Get the class name and link the objects to the stand and create
    // links for parent-child relationships by calling base stand linkObj
    sprintf( class_name, "%s", (const char *)(pcBase->Get_Class_Name()) );

    if ( Is_Base_Class( "cStd", pcBase ) )
    {
        if (pcStd)
        {
            pcStd    = (cStd*) pVoid;
            retValue = false;
        }
        else
            pcStd = (cStd*) pVoid;
    }

    return cBaseStdD::linkObj(pVoid, objType, objPos) && retValue;

} // end cStdD::linkObj()

//-------------------------------------------------------------------------
// cStdD ABSTRACT:
//   Stand copy constructor
//-------------------------------------------------------------------------
cStdD::cStdD (const cStdD& source)
    : cBaseStdD( source )
{

}

//---------------------------------------------------------------------------
// ASSIGN_STATE ABSTRACT:
//  Assigns state data from a source object to a destination object of
//  the same type.
//---------------------------------------------------------------------------
bool  cStdD::Assign_State(cBase * pcDest, cBase * pcSource)
{
    //-----------------------------------------------------------------------
    // Check pointers.  Alarm and abort if source or destination pointer is
    //      NULL.
    //-----------------------------------------------------------------------
    if ( pcDest == NULL )
    {
        EMSG << "NULL pointer in pcDest"
             << END_OF_MESSAGE;
        return false;
    }
    if ( pcSource == NULL )
    {
        EMSG << "NULL pointer in pcSource"
             << END_OF_MESSAGE;
        return false;
    }
    //-----------------------------------------------------------------------
    // Check object types.  Alarm and abort if the source and destination
    //      objects are not identical.
    //-----------------------------------------------------------------------
    if ( pcDest->Get_Class_Name() != pcSource->Get_Class_Name() )
    {
        EMSG << "Cannot assign " << (const char *)pcSource->objName()
             << " to " << (const char *)pcDest->objName()
             << ".  Assignment aborted."
             << END_OF_MESSAGE;
        return false;
    }
    //-----------------------------------------------------------------------
    // Assign source to destination object.
    //-----------------------------------------------------------------------
    *((cStdD *)(pcDest)) = *((cStdD *)(pcSource));
    return true;

}   // end cStdD::Assign_State()

//-------------------------------------------------------------------------
// OPERATOR = ABSTRACT:						Operate()：更新出口带钢状态，计算各种极限比率和超前速度
//   Stand assignment operator
//-------------------------------------------------------------------------
cStdD& cStdD::operator= (const cStdD& source)
{
    if (this != &source)
    {
        cBaseStdD::operator=(source);
        Copy_Data(this,(void *)&source,sizeof(cStdD),cStdD_schema);
    }
    return (*this);
}

//-------------------------------------------------------------------------
//   Std dump contents of the struct.
// The bool composed if true indicates that the
// object should call the dump function for the
// objects that it contains.
//-------------------------------------------------------------------------
void cStdD::dump(const bool composed)
{

    // Generically dump the data
    Dump_Data(stdout, "cStdD", this, 0, (const char *)objName());

    if (composed)
    {
    }

    cBaseStdD::dump( composed );

}// end cStdD::dump

//---------------------------------------------------------------------
// Virtual function to allow the user to carry out post processing for
// a dynamic stand.
//---------------------------------------------------------------------
bool cStdD::Post_Config(
                char *name,         // unused
                void *psStruct)     // unused
{
    pcEnPceD = (cPceD *)(previous_obj);

    //  Call Post_Config() for the enclosed cBaseStdD
    //  so that its entry base piece pointer is set.

    cBaseStdD::Post_Config( name, psStruct ) ;

    return true;
}

//-----------------------------------------------------------------
// Operate()
//
// This method updates the exit piece state, given the entry state		根据入口带钢的状态信息，计算并更新出口带钢的状态参数
// information for a piece.
//
// On the base object this method does nothing.
//-----------------------------------------------------------------
bool cStdD::Operate(void)
{
    //-------------------------------------------------------------
    // Bulk copy data from entry piece to exit piece
    //-------------------------------------------------------------
    *pcExPceD = *pcEnPceD;

    // If Base Stand Operate successful
    // Calculate critical limit ratio numbers
    if (cBaseStdD::Operate())
    {

        if ( !dummied )					定义为非虚拟状态，也就是工作状态
        {
            // Calculate force limit ratio			轧制力限制比例
            if ( force_max > 0.0 )
            {
                limrat_frc = force_strip / force_max;		使用当前对象的最大轧制力限制
            }
            else
            {
                limrat_frc = force_strip / pcStd->force_max;	使用静态配置对象的最大轧制力限制
            }

            if ( limrat_frc > 1.0F + Physcon.tol3 )
                lim = sl_frcmax;

            // Calculate thread power & run power limit ratio		计算功率限制比例
            if ( rpm <= pcStd->pcMtr->base_rpm )			低转速区间
            {
                limrat_thdp = power_shaft /						power_shaft ：实际轴功率
                              ( pcStd->pcMtr->base_ovrl * pcStd->pcMtr->power_rate);

                limrat_pwr  = power_shaft /
                              ( pcStd->pcMtr->base_ovrl * pcStd->pcMtr->power_rate);		limrat_thdp 、limrat_pwr 为不同工况的功率限制
            }
            else						高转速区间
            {
                float slope = (( pcStd->pcMtr->top_ovrl - pcStd->pcMtr->base_ovrl ) /		计算功率曲线斜率
                               ( pcStd->pcMtr->top_rpm * pcStd->pcMtr->max_rpm_pu -
                                pcStd->pcMtr->base_rpm )) * pcStd->pcMtr->power_rate;

                // Calculate required thread power
                limrat_thdp = power_shaft /
                             ( pcStd->pcMtr->thd_ovrl * pcStd->pcMtr->power_rate +
                               slope * ( rpm - pcStd->pcMtr->base_rpm ) );

                // Calculate required run power
                limrat_pwr  = power_shaft /
                             ( pcStd->pcMtr->top_ovrl * pcStd->pcMtr->power_rate +
                               slope * ( rpm - pcStd->pcMtr->base_rpm ) );
            }

            if ( limrat_pwr > 1.0F + Physcon.tol3 )
                lim = sl_pwrmax;

            // For logging purposes only.
            if (feedback)
            {
                limrat_pwr  = 100.0F * power_meas /
                              pcStd->pcMtr->power_rate;
            }

            // Calculate low cone limit
            if ( rpm > 0.0 )
            {
                limrat_locone = ( pcStd->pcMtr->base_rpm * pcStd->pcMtr->min_rpm_pu ) / rpm;
            }

            // Calculate hi come limit
            limrat_hicone = rpm / ( pcStd->pcMtr->top_rpm * pcStd->pcMtr->max_rpm_pu );

            // Calculate Lead Speed
            if ( ( rpm > 0.0 ) && ( pcStd->wk2 > 0.0 ) )
            {
                lead_speed =
                      Physcon.slscon * Physcon.sls_units
                    * power_shaft
                    * speed
                    * pcStd->slscoef
                    * pcStd->pcMtr->rectim
                    / ( pcStd->wk2 * rpm * rpm );
            }
            else
            {
                lead_speed = 0.0;
            }

        }

        // Safe return from derived stand
        return true;

    }
    return false;

}

//-------------------------------------------------------------------------
// TENSION ABSTRACT:
// Calculate the tension applied to the exit piece				轧机出口带钢张力的核心计算
//-------------------------------------------------------------------------
float cStdD::Tension(void)
{
#ifdef ALUMINUM_HSM
	// If no tensiometer, set exit tension to zero.
	if (pcStd->pcTensionreel)
	{
		if (feedback)
		{
			// Tensiometer object tension units are in N_Kg_lb
			// need to convert into N/mm2_kg/mm2_lb/in2
			return pcTensionreelD->tension /
				( pcExPceD->thick *
					pcExPceD->width );
		}

		//-----------------------------------------------------------------
		// For setup determine the tension from the current FPRP table.
		// NOTE: BaseStdD operate() handles setting tension correctly
		//       when a stand is dummied:
		//          exit tension = entry tension
		//-----------------------------------------------------------------
		if ( NULL == pcFPRP ) {return 0.0;}
		pcTensionreelD->tension = pcTensionreelD->multiplier *
							pcFPRP->state.tension[pcStd->num-1];

		float force = pcTensionreelD->tension *  pcExPceD->thick * pcExPceD->width / Physcon.kgpt_lbpt;
		if (force > pcTensionreelD->pcTensionreel->max_tension_force)
		{
			pcTensionreelD->tension = pcTensionreelD->pcTensionreel->max_tension_force * Physcon.kgpt_lbpt
									/ (pcExPceD->thick * pcExPceD->width);
		}

		return pcTensionreelD->tension;

	}
	else if (pcStd->pcTensiometer)
	{
		if (feedback)
		{
			// Tensiometer object tension units are in N_Kg_lb
			// need to convert into N/mm2_kg/mm2_lb/in2
			return pcTensiometerD->tension /
				( pcExPceD->thick *
					pcExPceD->width );
		}

		//-----------------------------------------------------------------
		// For setup determine the tension from the current FPRP table.
		// NOTE: BaseStdD operate() handles setting tension correctly
		//       when a stand is dummied:
		//          exit tension = entry tension
		//-----------------------------------------------------------------
		if ( NULL == pcFPRP ) {return 0.0;}
		pcTensiometerD->tension = pcTensiometerD->multiplier *
							pcFPRP->state.tension[pcStd->num-1];

		float force = pcTensiometerD->tension *  pcExPceD->thick * pcExPceD->width / Physcon.kgpt_lbpt;
		if (force > pcTensiometerD->pcTensiometer->max_tension_force)
		{
			pcTensiometerD->tension = pcTensiometerD->pcTensiometer->max_tension_force * Physcon.kgpt_lbpt
									/ (pcExPceD->thick * pcExPceD->width);
		}

		return pcTensiometerD->tension;
	}
	else
    {
        return 0.0;
    }

    // If feedback, return the measured tension
#else
	// If no looper, set exit tension to zero.
    if (NULL == pcStd->pcLooper)
    {
        return 0.0;
    }

    // If feedback, return the measured tension
    if (feedback)
    {
        // Looper object tension units are in N_Kg_lb
        // need to convert into N/mm2_kg/mm2_lb/in2
        return pcLooperD->tension /
               ( pcExPceD->thick *
                 pcExPceD->width );
    }

    //-----------------------------------------------------------------
    // For setup determine the tension from the current FPRP table.
    // NOTE: BaseStdD operate() handles setting tension correctly
    //       when a stand is dummied:
    //          exit tension = entry tension
    //-----------------------------------------------------------------
    if ( NULL == pcFPRP ) {return 0.0;}
    pcLooperD->tension = pcLooperD->multiplier *
                         pcFPRP->state.tension[pcStd->num-1];

    return pcLooperD->tension;
#endif
}

//-------------------------------------------------------------------------
// FLOWSTRESS ABSTRACT:
// Calculate the mean flowstress in the rollbite.
//-------------------------------------------------------------------------
float cStdD::Flowstress(void)
{
    float  sigma       = 0.0;                  // [kgpmm2_lbpin2_MPa] calculated mean flow stress
    float  base_flows  = 0.0;                  // [kgpmm2_lbpin2_MPa] base flow stress
    float  radius      = pcStdRollPrD->getAvgDiam(op_work)/2.0F;   // [mm_in] work roll radius
    float  temperature = pcRbheat->Average(cTmpGrad::whole); // [C_F] entry piece avg temperature

    // variables for complex flow stress calcs
    cFlowstress::status_type  status;          // status from flow stress calcs
    double inc_time      (0.0);                // [sec] interstand recovery time
    double complex_sigma (0.0);                // [kgpmm2_lbpin2_MPa] complex flow stress value
    char   class_name[32];

    //-------------------------------------------------------------------------
    // If input data is available, calculate the flow stress
    //-------------------------------------------------------------------------
    if ( (pcSGP        != NULL) &&
         (pcFAGP       != NULL) &&
         (pcFAPP       != NULL) &&
         (pcPDI        != NULL) &&
         (pcPDIChem    != NULL) &&
         (pcFlowstress != NULL) )
    {
        //---------------------------------------------------------------------
        // If using the curve method, use the model table flow stress curve
        //---------------------------------------------------------------------
        if (pcSGP->state.fm_use_curve)
        {
            //--------------------------------
            // Calculate  mean strain rate
            //--------------------------------
            mstrate = pcFlowstress->Mean_Strain_Rate( radius,
                                                      radius,
                                                      pcEnPceD->thick,
                                                      pcEnPceD->thick - draft,
                                                      speed);

            //--------------------------------------------------------------------------------------------
            // Calculate flowstress, strain rate modifier
            // Components used in the flow stress calculations
            //    base_fs  = steady state flow stress value for a given grade
            //    fsn_mult = adaptive (avg or normalized) flow stress multiplier to detect chem vari.
            //    fs_mult  = adaptive multiplier derived in association with exponent
            //    fs_tmp_mult = flow stress vs temperature multipliers.
            //    str_ratio = stress ratio calculated based on chemistry elements (long term stat. analy)
            //--------------------------------------------------------------------------------------------
            if ( feedback )
            {
                //--------------------------------
                // Calculate the base flow stress
                //--------------------------------
                base_flows = pcSGP->state.base_fs *
                             pcFAGP->adapted_state.fs_mult  *
                             cMathUty::rlnint(
                                              &temperature,
                                              &pcFAGP->adapted_state.fs_tmp[0],
                                              &pcFAGP->adapted_state.fs_tmp_mult[0],
                                              (int *)&numFsVsTemp );

                mstrate_mod  = (float) ( pow ( mstrate, pcFAPP->adapted_state.exponent ) );
            }
            else
            {
                base_flows = pcSGP->state.base_fs  *
                             pcFAGP->state.fs_mult *
                             cMathUty::rlnint(
                                              &temperature,
                                              &pcFAGP->state.fs_tmp[0],
                                              &pcFAGP->state.fs_tmp_mult[0],
                                              (int *)&numFsVsTemp );

                mstrate_mod  = (float) ( pow ( mstrate, pcFAPP->state.exponent ) );
            }

            //--------------------------------
            // Calculate final flow stress
            //--------------------------------
            sigma = base_flows * mstrate_mod * pcPDI->state.fm_str_ratio;

        }
        else
        //---------------------------------------------------------------------
        // Use the theoretical calculation of flow stress
        //---------------------------------------------------------------------
        {
            //-----------------------------------------------------------------
            // Calculate travel time from previous stand to this stand
            //-----------------------------------------------------------------
            cBase * pcBase = Previous_Object( this, "cStdD" );
            while (( NULL != pcBase ) && (pcBase != this ))
            {
                sprintf( class_name, "%s", ((const char *)(pcBase->Get_Class_Name())) );
                if ( 0 == _stricmp(class_name, "cStdD") )
                {
                    inc_time += ((cStdD *)(pcBase))->duration;
                }
                else if ( 0 == _stricmp(class_name, "cTableD") )
                {
                    inc_time += ((cTableD *)(pcBase))->duration;
                }
                pcBase = pcBase->next_obj;
            }

            //-----------------------------------------------------------------
            // Calculate the flow stress
            //-----------------------------------------------------------------
            if ( !pcFlowstress->Mean_Flow_Stress(
                        status,                     // OUT [-] calculation status
                        complex_sigma,              // OUT [pressure] calculated mean flow stress
                        pcExPceD->xdyn,             // OUT [-] fraction dynamic recrystallized
                        pcExPceD->xstat,            // OUT [-] fraction static recrystallized
                        pcExPceD->eps,              // OUT [-] exit strain
                        pcExPceD->d_aust,           // OUT [micron] austenitic grain size this stand
                        mstrate,                    // OUT [
                        radius,                     // IN  [mm_in] work roll radius
                        pcEnPceD->thick,            // IN  [mm_in] strip entry thickness
                        pcEnPceD->thick-draft,      // IN  [mm_in] strip exit thickness
                        speed,                      // IN  [mpm_fpm_mps] work roll speed
                        temperature,                // IN  [C_F] average strip temperature
                        pcEnPceD->eps,              // IN  [-] entry strain
                        pcEnPceD->xdyn,             // IN  [-] fraction dynamic recrystallized
                        pcEnPceD->xstat,            // IN  [-] fraction static recrystallized
                        pcFlowstress->d_aust[pcStd->num-1], // IN  [micron] austenitic grain size this stand
                        pcEnPceD->d_aust,           // IN  [micron] austenitic grain size previous stand
                        inc_time,                   // IN  [sec] incubation time
                        pcPDIChem->state.percnt_c,  // IN  [-] percent carbon
                        pcPDIChem->state.percnt_mn, // IN  [-] percent manganese
                        pcEnPceD->ferrite,          // IN  [-] ferrite fraction
                        pcSGP->state.sigma_fer_mult ) )  // IN [-] ferrite flowstress modifier
            {
                EMSG << "Error in calculation of flow stress for stand "
                     << pcStd->num
                     << END_OF_MESSAGE;
            }

            if ( feedback )
            {
                sigma = (float)(complex_sigma) *
                        pcFAGP->adapted_state.fsn_mult *
                        fsp_mult;
            }
            else
            {
                sigma = (float)(complex_sigma) *
                        pcFAGP->state.fsn_mult *
                        fsp_mult;
            }
        } // normal or complex flowstress calculations

    } // valid pointer

    return sigma;

} // End cStdD::Flowstress()
//-------------------------------------------------------------------------
// SPEED ABSTRACT:
// Calculate the roll peripheral speed and motor rpm.  The user may
// override this on the derived stand to suite his specific purposes.
//-------------------------------------------------------------------------
void    cStdD::Speed(void)
{
    float   approx_slip = 0.0;

    // ---------------------------------------------------------------
    // Set the required piece input data for
    // the roll pressure distribution calculations.
    // ---------------------------------------------------------------
    if ( pcRollbite->Force_Valid() && (slip != 0.0) )
    {
        // use last calculated slip
        approx_slip = slip;
    }
    else
    {
        // first time, use approximation for slip
        approx_slip =  
            pcRollbite->Approximate_Slip(
            pcStdRollPrD->getAvgDiam(op_work),  // roll diameter
            pcExBasePceD->thick,             // exit thickness
            pcEnBasePceD->thick);            // entry thickness
    }

    // Calculate roll peripheral speed
    // only in setup mode. In feedback mode
    // speed will be initialized to measured 
    // roll speed
    if ( false == feedback )
    {
        // First time into this segment calculations
        if ( speed <= 0.0F )
            speed = pcEnPceD->speed * pcEnPceD->thick    / 
                    ((pcEnPceD->thick - draft) *
                    approx_slip);
        else
            speed = pcEnBasePceD->pcBasePce->mass_flow  /
                    (pcExBasePceD->thick * approx_slip);
            
    }

    // Calculate motor rpm for power to torque conversions
    rpm = Physcon.mmpm_inpft * speed * 
              pcBaseStd->gearat * Physcon.secpmin /
              (Physcon.pi * 
               pcStdRollPrD->getAvgDiam(op_work) *
               Physcon.vel_time);

} // End cStdD::Speed()


//-------------------------------------------------------------------------
// VOLUME_FLOW ABSTRACT:
// Calculate the volume flow.  The user may override this on the derived
// stand to suite his specific purposes.
//-------------------------------------------------------------------------
float   cStdD::Volume_Flow()
{
    float volume_flow = pcEnPceD->thick * pcEnPceD->speed *
                        rbite_width *
                        Physcon.mmpm_inpft / Physcon.vel_time;

    return volume_flow;

} // End cStdD::Volume_Flow()