import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from sklearn.model_selection import train_test_split
from sklearn.linear_model import LinearRegression, HuberRegressor
from sklearn.preprocessing import PolynomialFeatures
from sklearn.metrics import r2_score, mean_squared_error
import os
from datetime import datetime

plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

class RMVernierAnalysis:
    def __init__(self):
        self.base_path = r"d:\work\pycharm\width - 副本\Q235B"
        self.setup_directories()
        
    def setup_directories(self):
        """创建必要的目录结构"""
        self.analysis_dir = os.path.join(self.base_path, "analysis")
        os.makedirs(self.analysis_dir, exist_ok=True)
        
    def load_production_data(self, file_path):
        """加载生产数据并进行预处理"""
        df = pd.read_excel(file_path)
        
        # 数据基本信息
        print("\n=== 数据基本信息 ===")
        print(f"总记录数: {len(df)}")
        print("\n列名列表:")
        print(df.columns.tolist())
        
        # 筛选Q235B数据
        q235b_data = df[df['grade_name'] == 'Q235B'].copy()
        print(f"\nQ235B钢种数据量: {len(q235b_data)}")
        
        # 数据质量检查
        self._check_data_quality(q235b_data)
        
        return q235b_data
    
    def _check_data_quality(self, data):
        """检查数据质量"""
        print("\n=== 数据质量检查 ===")
        
        # 检查缺失值
        missing_data = data[['rx_pr_ver', 'width_hit_rate']].isnull().sum()
        print("\n缺失值统计:")
        print(missing_data)
        
        # 检查异常值
        print("\n数值范围检查:")
        print(data[['rx_pr_ver', 'width_hit_rate']].describe())
        
        # 检查rm_vernier分布
        self._plot_distribution(data)
    
    def _plot_distribution(self, data):
        """绘制rm_vernier和命中率的分布图"""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
        
        # rm_vernier分布
        sns.boxplot(y=data['rx_pr_ver'], ax=ax1)
        ax1.set_title('rm_vernier分布')
        
        # 命中率分布
        sns.boxplot(y=data['width_hit_rate'], ax=ax2)
        ax2.set_title('宽度命中率分布')
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.analysis_dir, 'distributions.png'))
        plt.close()
    
    def analyze_time_patterns(self, data):
        """分析时间模式"""
        if 'timestamp' in data.columns:
            data['date'] = pd.to_datetime(data['timestamp']).dt.date
            daily_stats = data.groupby('date').agg({
                'rx_pr_ver': ['mean', 'std'],
                'width_hit_rate': ['mean', 'std']
            }).reset_index()
            
            # 绘制时间序列图
            fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8))
            
            ax1.errorbar(daily_stats['date'], 
                        daily_stats['rx_pr_ver']['mean'],
                        yerr=daily_stats['rx_pr_ver']['std'],
                        fmt='o-')
            ax1.set_title('rm_vernier随时间变化')
            
            ax2.errorbar(daily_stats['date'],
                        daily_stats['width_hit_rate']['mean'],
                        yerr=daily_stats['width_hit_rate']['std'],
                        fmt='o-')
            ax2.set_title('宽度命中率随时间变化')
            
            plt.tight_layout()
            plt.savefig(os.path.join(self.analysis_dir, 'time_patterns.png'))
            plt.close()
            
            return daily_stats
    
    def analyze_relationships(self, data):
        """分析rm_vernier与其他参数的关系"""
        # 相关性分析
        correlation_matrix = data[['rx_pr_ver', 'width_hit_rate']].corr()
        print("\n=== 相关性分析 ===")
        print(correlation_matrix)
        
        # 绘制散点图
        plt.figure(figsize=(8, 6))
        sns.scatterplot(data=data, x='rx_pr_ver', y='width_hit_rate')
        plt.title('rm_vernier vs 宽度命中率')
        plt.savefig(os.path.join(self.analysis_dir, 'relationship.png'))
        plt.close()
        
        # 计算最优rm_vernier值
        return self._find_optimal_rm_vernier(data)
    
    def _find_optimal_rm_vernier(self, data):
        """寻找最优rm_vernier值"""
        # 使用稳健回归
        model = HuberRegressor()
        X = data['rx_pr_ver'].values.reshape(-1, 1)
        y = data['width_hit_rate'].values
        
        model.fit(X, y)
        
        # 在rm_vernier范围内寻找最优值
        test_values = np.linspace(data['rx_pr_ver'].min(), 
                                data['rx_pr_ver'].max(), 
                                1000).reshape(-1, 1)
        predicted_hit_rates = model.predict(test_values)
        optimal_index = np.argmax(predicted_hit_rates)
        optimal_rm_vernier = test_values[optimal_index][0]
        
        return {
            'optimal_rm_vernier': optimal_rm_vernier,
            'predicted_max_hit_rate': predicted_hit_rates[optimal_index],
            'model': model
        }
    
    def generate_report(self, data, analysis_results):
        """生成分析报告"""
        doc_path = os.path.join(self.analysis_dir, 'rm_vernier_analysis_report.docx')
        from docx import Document
        from docx.shared import Inches
        
        doc = Document()
        
        # 标题
        doc.add_heading('Q235B钢种rm_vernier优化分析报告', 0)
        doc.add_paragraph(f'报告生成时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}')
        
        # 1. 数据概述
        doc.add_heading('1. 数据概述', level=1)
        doc.add_paragraph(f'分析样本数量: {len(data)}')
        doc.add_paragraph(f'数据时间范围: {data["timestamp"].min()} 至 {data["timestamp"].max()}')
        doc.add_paragraph(f'rm_vernier范围: {data["rx_pr_ver"].min():.2f} - {data["rx_pr_ver"].max():.2f}')
        doc.add_paragraph(f'宽度命中率范围: {data["width_hit_rate"].min():.2f}% - {data["width_hit_rate"].max():.2f}%')
        
        # 2. 关键发现
        doc.add_heading('2. 关键发现', level=1)
        opt_rm = analysis_results['optimal_rm_vernier']
        pred_hit = analysis_results['predicted_max_hit_rate']
        doc.add_paragraph(f'最优rm_vernier值: {opt_rm:.2f}')
        doc.add_paragraph(f'预测最高命中率: {pred_hit:.2f}%')
        
        # 3. 建议
        doc.add_heading('3. 优化建议', level=1)
        current_mean = data['rx_pr_ver'].mean()
        if abs(current_mean - opt_rm) > 0.5:
            doc.add_paragraph(f'当前rm_vernier平均值({current_mean:.2f})与最优值差异较大，建议调整至{opt_rm:.2f}')
        else:
            doc.add_paragraph('当前rm_vernier设定基本合理，建议保持现有设置')
        
        # 添加图片
        doc.add_picture(os.path.join(self.analysis_dir, 'distributions.png'), width=Inches(6))
        doc.add_picture(os.path.join(self.analysis_dir, 'time_patterns.png'), width=Inches(6))
        doc.add_picture(os.path.join(self.analysis_dir, 'relationship.png'), width=Inches(6))
        
        doc.save(doc_path)
    
    def run_analysis(self, file_path):
        """运行完整分析流程"""
        print("开始分析Q235B钢种rm_vernier优化...")
        
        # 1. 加载数据
        data = self.load_production_data(file_path)
        
        # 2. 分析时间模式
        time_patterns = self.analyze_time_patterns(data)
        
        # 3. 分析关系
        analysis_results = self.analyze_relationships(data)
        
        # 4. 生成报告
        self.generate_report(data, analysis_results)
        
        print(f"\n分析完成！请查看分析报告：{self.analysis_dir}")
        return analysis_results

if __name__ == "__main__":
    analyzer = RMVernierAnalysis()
    file_path = "production_data.xlsx"  # 替换为实际的数据文件路径
    results = analyzer.run_analysis(file_path)
