import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from sklearn.metrics import r2_score, mean_squared_error
import os

plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

class WidthControlAnalysis:
    def __init__(self):
        self.base_path = r"d:\work\pycharm\width - 副本\Q235B"
        self.setup_directories()
        
    def setup_directories(self):
        """创建必要的目录结构"""
        os.makedirs(self.base_path, exist_ok=True)
        self.analysis_dir = os.path.join(self.base_path, "analysis")
        os.makedirs(self.analysis_dir, exist_ok=True)

    def load_and_preprocess_data(self, file_path):
        """加载并预处理数据"""
        try:
            df = pd.read_excel(file_path)
            print(f"成功读取数据，共{len(df)}条记录")
            
            # 筛选Q235B钢种数据
            q235b_data = df[df['grade_name'] == 'Q235B'].copy()
            print(f"Q235B钢种数据共{len(q235b_data)}条记录")
            
            # 添加效率指标：命中率/rm_vernier值的比率
            # 该指标越大说明单位rm_vernier带来的命中率越高
            q235b_data['efficiency'] = q235b_data['width_hit_rate'] / q235b_data['rx_pr_ver'].abs()
            
            # 对efficiency进行无穷大处理（当rx_pr_ver接近0时）
            q235b_data['efficiency'] = q235b_data['efficiency'].replace([np.inf, -np.inf], np.nan)
            
            return q235b_data
            
        except Exception as e:
            print(f"数据加载错误：{str(e)}")
            return None

    def analyze_control_effectiveness(self, data):
        """分析控制效果"""
        # 1. 按rm_vernier值分组分析
        vernier_groups = pd.cut(data['rx_pr_ver'].abs(), bins=5)  # 将rm_vernier分成5组
        grouped_analysis = data.groupby(vernier_groups).agg({
            'width_hit_rate': ['mean', 'std', 'count'],
            'efficiency': ['mean', 'std']
        }).round(2)
        
        # 2. 计算最优控制区间
        best_performance = self._find_best_performance(data)
        
        return {
            'grouped_analysis': grouped_analysis,
            'best_performance': best_performance
        }

    def _find_best_performance(self, data):
        """寻找最优控制区间"""
        # 1. 找出高命中率的数据（比如命中率>90%的数据）
        high_performance = data[data['width_hit_rate'] >= 90]
        
        if len(high_performance) == 0:
            return None
            
        # 2. 在高命中率数据中找出rm_vernier最小的区间
        min_vernier = high_performance['rx_pr_ver'].abs().min()
        good_control = high_performance[high_performance['rx_pr_ver'].abs() <= min_vernier * 1.2]
        
        return {
            'min_vernier': min_vernier,
            'avg_hit_rate': good_control['width_hit_rate'].mean(),
            'sample_count': len(good_control),
            'efficiency': good_control['efficiency'].mean()
        }

    def generate_analysis_report(self, data, analysis_results):
        """生成分析报告"""
        from docx import Document
        from docx.shared import Inches
        
        doc = Document()
        
        # 1. 报告标题
        doc.add_heading('Q235B宽度控制系统优化分析报告', 0)
        
        # 2. 数据概述
        doc.add_heading('1. 数据概述', level=1)
        doc.add_paragraph(f'分析样本数量: {len(data)}条')
        doc.add_paragraph(f'rm_vernier范围: {data["rx_pr_ver"].abs().min():.2f} - {data["rx_pr_ver"].abs().max():.2f}')
        doc.add_paragraph(f'宽度命中率范围: {data["width_hit_rate"].min():.2f}% - {data["width_hit_rate"].max():.2f}%')
        
        # 3. 控制效果分析
        doc.add_heading('2. 控制效果分析', level=1)
        grouped = analysis_results['grouped_analysis']
        doc.add_paragraph('rm_vernier分组分析结果：')
        for idx, row in grouped.iterrows():
            doc.add_paragraph(
                f'rm_vernier范围 {idx.left:.2f}-{idx.right:.2f}:\n'
                f'  - 平均命中率: {row[("width_hit_rate","mean")]:.2f}%\n'
                f'  - 样本数量: {row[("width_hit_rate","count")]}\n'
                f'  - 控制效率: {row[("efficiency","mean")]:.2f}'
            )
        
        # 4. 最优控制区间
        doc.add_heading('3. 最优控制区间分析', level=1)
        best_perf = analysis_results['best_performance']
        if best_perf:
            doc.add_paragraph(
                f'最优rm_vernier值: {best_perf["min_vernier"]:.2f}\n'
                f'对应平均命中率: {best_perf["avg_hit_rate"]:.2f}%\n'
                f'样本数量: {best_perf["sample_count"]}\n'
                f'控制效率: {best_perf["efficiency"]:.2f}'
            )
        
        # 5. 优化建议
        doc.add_heading('4. 优化建议', level=1)
        current_avg_vernier = data['rx_pr_ver'].abs().mean()
        doc.add_paragraph(f'当前平均rm_vernier值: {current_avg_vernier:.2f}')
        
        if best_perf and current_avg_vernier > best_perf["min_vernier"] * 1.2:
            doc.add_paragraph(
                f'建议优化方向：\n'
                f'1. 当前rm_vernier值偏高，可以尝试降低到{best_perf["min_vernier"]:.2f}附近\n'
                f'2. 重点分析rm_vernier较小但命中率高的工况特点\n'
                f'3. 优化模型参数，提高系统自适应能力'
            )
        
        # 6. 添加可视化图表
        self.generate_visualizations(data)
        doc.add_picture(os.path.join(self.analysis_dir, 'control_analysis.png'), width=Inches(6))
        
        # 保存报告
        doc.save(os.path.join(self.analysis_dir, 'width_control_optimization.docx'))

    def generate_visualizations(self, data):
        """生成可视化分析图表"""
        plt.figure(figsize=(15, 5))
        
        # 1. rm_vernier vs 命中率散点图
        plt.subplot(131)
        plt.scatter(data['rx_pr_ver'].abs(), data['width_hit_rate'], alpha=0.5)
        plt.xlabel('|rm_vernier|值')
        plt.ylabel('宽度命中率(%)')
        plt.title('rm_vernier与命中率关系')
        
        # 2. 控制效率分布
        plt.subplot(132)
        plt.hist(data['efficiency'].dropna(), bins=30, alpha=0.7)
        plt.xlabel('控制效率(命中率/|rm_vernier|)')
        plt.ylabel('频数')
        plt.title('控制效率分布')
        
        # 3. 箱线图
        plt.subplot(133)
        vernier_groups = pd.cut(data['rx_pr_ver'].abs(), bins=5)
        plt.boxplot([group['width_hit_rate'].values for name, group in data.groupby(vernier_groups)])
        plt.xlabel('rm_vernier分组')
        plt.ylabel('宽度命中率(%)')
        plt.title('不同rm_vernier值的命中率分布')
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.analysis_dir, 'control_analysis.png'))
        plt.close()

    def run_analysis(self, file_path):
        """运行完整的分析流程"""
        print("开始分析宽度控制系统效果...")
        
        # 1. 加载数据
        data = self.load_and_preprocess_data(file_path)
        if data is None:
            return
            
        # 2. 分析控制效果
        analysis_results = self.analyze_control_effectiveness(data)
        
        # 3. 生成报告
        self.generate_analysis_report(data, analysis_results)
        
        print(f"分析完成！请查看报告：{self.analysis_dir}")
        return analysis_results

if __name__ == "__main__":
    analyzer = WidthControlAnalysis()
    # 替换为实际的数据文件路径
    file_path = "钢种数据分析结果.xlsx"
    results = analyzer.run_analysis(file_path)
