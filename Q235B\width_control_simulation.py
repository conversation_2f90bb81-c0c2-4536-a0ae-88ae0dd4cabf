import numpy as np
import pandas as pd
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
from docx import Document
from docx.shared import Inches
import os

plt.rcParams['font.sans-serif'] = ['SimHei']  # 设置中文字体为黑体
plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

class WidthControlSimulation:
    def __init__(self):
        self.base_path = r"d:\work\pycharm\width - 副本\Q235B"
        self.setup_directories()
        
    def setup_directories(self):
        """创建必要的目录结构"""
        os.makedirs(self.base_path, exist_ok=True)
        os.makedirs(os.path.join(self.base_path, "data"), exist_ok=True)
        os.makedirs(os.path.join(self.base_path, "results"), exist_ok=True)
        os.makedirs(os.path.join(self.base_path, "figures"), exist_ok=True)

    def generate_simulation_data(self, n_samples=200):
        """生成模拟轧制数据"""
        np.random.seed(42)  # 设置随机种子以确保可重复性
        
        # 生成基础数据
        data = {
            'timestamp': [(datetime.now() + timedelta(minutes=i)).strftime('%Y-%m-%d %H:%M:%S') 
                         for i in range(n_samples)],
            'steel_grade': ['Q235B'] * n_samples,
            'target_width': np.random.uniform(800, 1200, n_samples),  # 目标宽度范围
            'entry_temperature': np.random.uniform(1000, 1100, n_samples),  # 入口温度
            'rolling_force': np.random.uniform(1000, 2000, n_samples),  # 轧制力
            'rolling_speed': np.random.uniform(2, 5, n_samples),  # 轧制速度
        }
        
        # 添加系统偏差和随机噪声
        systematic_error = 4.0  # 已知的系统偏差
        random_noise = np.random.normal(0, 0.5, n_samples)  # 随机误差
        
        # 计算实际宽度（包含系统偏差和随机误差）
        data['actual_width'] = data['target_width'] + systematic_error + random_noise
        
        # 计算原始rm_vernier值（用于当前模型）
        data['rm_vernier'] = np.ones(n_samples) * 4.0
        
        # 创建DataFrame并保存
        df = pd.DataFrame(data)
        df.to_excel(os.path.join(self.base_path, "data", "simulation_data.xlsx"), index=False)
        return df

    def current_model_simulation(self, data):
        """模拟当前宽度控制模型"""
        # 使用当前rm_vernier=4的模型
        predictions = {
            'predicted_width': data['target_width'] + data['rm_vernier'],
            'width_error': np.abs(data['actual_width'] - (data['target_width'] + data['rm_vernier'])),
            'hit_rate': None
        }
        
        # 计算命中率（误差在±2mm内）
        hit_count = np.sum(np.abs(predictions['width_error']) <= 2.0)
        predictions['hit_rate'] = hit_count / len(data) * 100
        
        return predictions

    def optimized_model_simulation(self, data):
        """模拟优化后的宽度控制模型"""
        # 新模型考虑温度补偿和轧制力的影响
        temperature_factor = (data['entry_temperature'] - 1050) * 0.01  # 温度影响因子
        force_factor = (data['rolling_force'] - 1500) * 0.001  # 轧制力影响因子
        
        # 优化后的宽度预测（rm_vernier=0）
        predictions = {
            'predicted_width': data['target_width'] + temperature_factor + force_factor,
            'width_error': np.abs(data['actual_width'] - 
                                (data['target_width'] + temperature_factor + force_factor)),
            'hit_rate': None
        }
        
        # 计算命中率
        hit_count = np.sum(np.abs(predictions['width_error']) <= 2.0)
        predictions['hit_rate'] = hit_count / len(data) * 100
        
        return predictions

    def generate_analysis_report(self, data, current_results, optimized_results):
        """生成分析报告"""
        doc = Document()
        
        # 添加标题
        doc.add_heading('Q235B钢种宽度控制系统优化分析报告', 0)
        doc.add_paragraph(f'报告生成时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}')
        
        # 数据概述
        doc.add_heading('1. 数据概述', level=1)
        doc.add_paragraph(f'分析样本数量: {len(data)}')
        doc.add_paragraph(f'目标宽度范围: {data["target_width"].min():.2f}mm - {data["target_width"].max():.2f}mm')
        
        # 当前模型分析
        doc.add_heading('2. 当前模型分析 (rm_vernier = 4)', level=1)
        doc.add_paragraph(f'平均宽度误差: {current_results["width_error"].mean():.2f}mm')
        doc.add_paragraph(f'宽度命中率: {current_results["hit_rate"]:.2f}%')
        
        # 优化模型分析
        doc.add_heading('3. 优化模型分析 (rm_vernier = 0)', level=1)
        doc.add_paragraph(f'平均宽度误差: {optimized_results["width_error"].mean():.2f}mm')
        doc.add_paragraph(f'宽度命中率: {optimized_results["hit_rate"]:.2f}%')
        
        # 生成对比图
        self.generate_comparison_plots(data, current_results, optimized_results)
        
        # 添加图片到报告
        doc.add_heading('4. 模型对比分析', level=1)
        doc.add_picture(os.path.join(self.base_path, "figures", "width_error_comparison.png"), 
                       width=Inches(6))
        
        # 保存报告
        doc.save(os.path.join(self.base_path, "results", "width_control_analysis_report.docx"))

    def generate_comparison_plots(self, data, current_results, optimized_results):
        """生成对比图表"""
        plt.figure(figsize=(12, 6))
        
        plt.subplot(1, 2, 1)
        plt.hist(current_results['width_error'], bins=30, alpha=0.5, label='当前模型')
        plt.hist(optimized_results['width_error'], bins=30, alpha=0.5, label='优化模型')
        plt.xlabel('宽度误差 (mm)')
        plt.ylabel('频数')
        plt.title('宽度误差分布对比')
        plt.legend()
        
        plt.subplot(1, 2, 2)
        plt.scatter(data['target_width'], current_results['width_error'], 
                   alpha=0.5, label='当前模型')
        plt.scatter(data['target_width'], optimized_results['width_error'], 
                   alpha=0.5, label='优化模型')
        plt.xlabel('目标宽度 (mm)')
        plt.ylabel('宽度误差 (mm)')
        plt.title('宽度误差vs目标宽度')
        plt.legend()
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.base_path, "figures", "width_error_comparison.png"))
        plt.close()

    def run_simulation(self):
        """运行完整的模拟分析流程"""
        print("开始运行宽度控制系统仿真分析...")
        
        # 1. 生成模拟数据
        print("正在生成模拟数据...")
        data = self.generate_simulation_data()
        
        # 2. 运行当前模型仿真
        print("正在运行当前模型仿真...")
        current_results = self.current_model_simulation(data)
        
        # 3. 运行优化模型仿真
        print("正在运行优化模型仿真...")
        optimized_results = self.optimized_model_simulation(data)
        
        # 4. 生成分析报告
        print("正在生成分析报告...")
        self.generate_analysis_report(data, current_results, optimized_results)
        
        print(f"分析完成！请在以下目录查看结果：{self.base_path}")
        print(f"- 模拟数据：{os.path.join(self.base_path, 'data')}")
        print(f"- 分析报告：{os.path.join(self.base_path, 'results')}")
        print(f"- 对比图表：{os.path.join(self.base_path, 'figures')}")

if __name__ == "__main__":
    simulation = WidthControlSimulation()
    simulation.run_simulation()
