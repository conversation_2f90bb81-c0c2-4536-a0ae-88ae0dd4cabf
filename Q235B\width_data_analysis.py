import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from sklearn.model_selection import train_test_split
from sklearn.linear_model import LinearRegression
from sklearn.metrics import r2_score, mean_squared_error
import os

plt.rcParams['font.sans-serif'] = ['SimHei']  # 设置中文字体为黑体
plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题


class WidthAnalysis:
    def __init__(self):
        self.base_path = r"d:\work\pycharm\width - 副本\Q235B"
        self.setup_directories()

    def setup_directories(self):
        """创建必要的目录结构"""
        os.makedirs(self.base_path, exist_ok=True)
        self.analysis_dir = os.path.join(self.base_path, "analysis")
        os.makedirs(self.analysis_dir, exist_ok=True)

    def load_and_preprocess_data(self, file_path):
        """加载并预处理Excel数据"""
        try:
            # 读取Excel数据
            df = pd.read_excel(file_path)
            print(f"成功读取Excel文件: {file_path}")
            print(f"数据共 {df.shape[0]} 行, {df.shape[1]} 列")
        except Exception as e:
            print(f"读取Excel文件时出错: {str(e)}")
            raise  # 重新抛出异常，让调用者知道发生了错误

        # 筛选Q235B钢种数据
        q235b_data = df[df['grade_name'] == 'Q235B'].copy()

        # 检查数据质量
        print("\n数据质量检查报告：")
        print("-" * 50)
        print(f"Q235B总样本数: {len(q235b_data)}")

        # 检查关键列是否存在
        required_columns = ['rx_pr_ver', 'width_hit_rate']
        for col in required_columns:
            if col not in q235b_data.columns:
                raise ValueError(f"数据中缺少必要的列: {col}")

        print(f"rx_pr_ver非空值数量: {q235b_data['rx_pr_ver'].notna().sum()}")
        print(f"width_hit_rate非空值数量: {q235b_data['width_hit_rate'].notna().sum()}")

        # 移除包含缺失值的行
        q235b_clean = q235b_data.dropna(subset=required_columns)
        print(f"移除缺失值后的样本数: {len(q235b_clean)}")

        # 删除异常值
        q235b_clean = self.remove_outliers(q235b_clean)
        print(f"清洗（移除异常值）后的样本数: {len(q235b_clean)}")

        return q235b_clean

    def remove_outliers(self, df):
        """使用IQR方法移除异常值"""
        for column in ['rx_pr_ver', 'width_hit_rate']:
            Q1 = df[column].quantile(0.25)
            Q3 = df[column].quantile(0.75)
            IQR = Q3 - Q1
            # 定义异常值边界
            lower_bound = Q1 - 1.5 * IQR
            upper_bound = Q3 + 1.5 * IQR
            # 过滤异常值
            df = df[(df[column] >= lower_bound) & (df[column] <= upper_bound)]
            print(f"移除{column}异常值后样本数: {len(df)}")
        return df

    def analyze_relationship(self, data):
        """分析rx_pr_ver和宽度命中率的关系"""
        # 计算相关系数
        correlation = data['rx_pr_ver'].corr(data['width_hit_rate'])
        print(f"rx_pr_ver与width_hit_rate的相关系数: {correlation:.4f}")

        # 进行线性回归
        X = data['rx_pr_ver'].values.reshape(-1, 1)
        y = data['width_hit_rate'].values

        model = LinearRegression()
        model.fit(X, y)

        # 预测值
        y_pred = model.predict(X)

        # 计算R²和RMSE
        r2 = r2_score(y, y_pred)
        rmse = np.sqrt(mean_squared_error(y, y_pred))

        print(f"回归模型R²值: {r2:.4f}")
        print(f"回归模型RMSE值: {rmse:.4f}")

        # 计算理论最优rx_pr_ver值（当宽度命中率为100%时）
        if model.coef_[0] != 0:
            optimal_rx_pr_ver = (100 - model.intercept_) / model.coef_[0]
            print(f"理论最优rx_pr_ver值（命中率100%时）: {optimal_rx_pr_ver:.2f}")
        else:
            optimal_rx_pr_ver = None
            print("无法计算理论最优rx_pr_ver值：回归系数为0")

        # 记录分析结果，包括理论最优值
        results = {
            'correlation': correlation,
            'slope': model.coef_[0],
            'intercept': model.intercept_,
            'r2': r2,
            'rmse': rmse,
            'model': model,
            'optimal_rx_pr_ver': optimal_rx_pr_ver  # 新增：保存理论最优值
        }

        return results

    def generate_analysis_report(self, data, results):
        """生成分析报告"""
        doc_path = os.path.join(self.analysis_dir, "Q235B_width_analysis.docx")
        chart_path = os.path.join(self.analysis_dir, "relationship_analysis.png")

        try:
            # 先确保图表已生成
            self.generate_visualizations(data, results)

            # 检查图表文件是否存在
            if not os.path.exists(chart_path):
                print(f"警告: 图表文件不存在 - {chart_path}")

            from docx import Document
            from docx.shared import Inches

            doc = Document()

            # 添加标题
            doc.add_heading('Q235B钢种rx_pr_ver与宽度命中率关系分析报告', 0)

            # 1. 数据概述
            doc.add_heading('1. 数据概述', level=1)
            doc.add_paragraph(f'样本数量: {len(data)}')
            doc.add_paragraph(f'rx_pr_ver范围: {data["rx_pr_ver"].min():.2f} - {data["rx_pr_ver"].max():.2f}')
            doc.add_paragraph(
                f'宽度命中率范围: {data["width_hit_rate"].min():.2f}% - {data["width_hit_rate"].max():.2f}%')

            # 2. 相关性分析
            doc.add_heading('2. 相关性分析', level=1)
            doc.add_paragraph(f'Pearson相关系数: {results["correlation"]:.4f}')
            doc.add_paragraph(f'决定系数(R²): {results["r2"]:.4f}')
            doc.add_paragraph(f'均方根误差(RMSE): {results["rmse"]:.4f}')

            # 3. 回归模型
            doc.add_heading('3. 回归模型', level=1)
            doc.add_paragraph(f'线性回归方程: y = {results["slope"]:.4f}x + {results["intercept"]:.4f}')
            doc.add_paragraph('其中：y为宽度命中率（%），x为rx_pr_ver值')

            # 4. 关键发现
            doc.add_heading('4. 关键发现', level=1)
            if results["optimal_rx_pr_ver"] is not None:
                # 新增：检查理论最优值是否在实际数据范围内
                rx_min, rx_max = data["rx_pr_ver"].min(), data["rx_pr_ver"].max()
                if rx_min <= results["optimal_rx_pr_ver"] <= rx_max:
                    doc.add_paragraph(f'理论最优rx_pr_ver值（当宽度命中率为100%时）: {results["optimal_rx_pr_ver"]:.2f}')
                else:
                    doc.add_paragraph(f'理论最优rx_pr_ver值（当宽度命中率为100%时）: {results["optimal_rx_pr_ver"]:.2f}')
                    doc.add_paragraph(f'注意：该理论值超出实际数据范围（{rx_min:.2f} - {rx_max:.2f}），仅供参考')
            else:
                doc.add_paragraph('无法计算理论最优rx_pr_ver值：回归系数为0')


            # 添加图表（只有文件存在时）
            if os.path.exists(chart_path):
                doc.add_picture(chart_path, width=Inches(6))
            else:
                doc.add_paragraph("警告: 无法添加图表，图表文件不存在")

            # 保存报告
            doc.save(doc_path)
            print(f"分析报告已保存至: {doc_path}")

        except ImportError:
            print("未安装python-docx库，无法生成Word报告。请安装该库: pip install python-docx")
        except Exception as e:
            print(f"生成Word报告时出错: {str(e)}")

    def generate_visualizations(self, data, results):
        """生成可视化图表，新增理论最优值标记"""
        try:
            plt.figure(figsize=(15, 5))

            # 1. 散点图和回归线，新增理论最优值标记
            plt.subplot(1, 3, 1)
            plt.scatter(data['rx_pr_ver'], data['width_hit_rate'], alpha=0.5)
            X = np.linspace(data['rx_pr_ver'].min(), data['rx_pr_ver'].max(), 100).reshape(-1, 1)
            y_pred = results['model'].predict(X)
            plt.plot(X, y_pred, 'r-', label='回归线')

            # 标记理论最优值（当宽度命中率为100%时）
            if results["optimal_rx_pr_ver"] is not None:
                plt.axvline(x=results["optimal_rx_pr_ver"], color='g', linestyle='--',
                            label=f'理论最优值: {results["optimal_rx_pr_ver"]:.2f}')
                plt.axhline(y=100, color='b', linestyle=':', label='目标命中率100%')

            plt.xlabel('rx_pr_ver值')
            plt.ylabel('宽度命中率(%)')
            plt.title('rx_pr_ver与宽度命中率关系')
            plt.legend()

            # 2. rx_pr_ver分布
            plt.subplot(1, 3, 2)
            sns.histplot(data['rx_pr_ver'], bins=30)
            plt.xlabel('rx_pr_ver值')
            plt.ylabel('频数')
            plt.title('rx_pr_ver分布')

            # 3. 宽度命中率分布
            plt.subplot(1, 3, 3)
            sns.histplot(data['width_hit_rate'], bins=30)
            plt.xlabel('宽度命中率(%)')
            plt.ylabel('频数')
            plt.title('宽度命中率分布')

            plt.tight_layout()
            chart_path = os.path.join(self.analysis_dir, "relationship_analysis.png")
            plt.savefig(chart_path, dpi=300)
            plt.close()
            print(f"分析图表已保存至: {chart_path}")
            return chart_path
        except Exception as e:
            print(f"生成图表时出错: {str(e)}")
            return None

    def run_analysis(self, file_path):
        """运行完整的分析流程"""
        print("开始进行Q235B钢种rx_pr_ver与宽度命中率关系分析...")

        try:
            # 1. 加载和预处理数据
            print("正在加载和预处理数据...")
            data = self.load_and_preprocess_data(file_path)

            if len(data) < 10:  # 检查样本量是否足够
                print(f"警告: 有效样本量较少({len(data)}), 分析结果可能不可靠")

            # 2. 分析关系
            print("正在分析变量关系...")
            results = self.analyze_relationship(data)

            # 3. 生成报告和可视化
            print("正在生成分析报告和可视化...")
            self.generate_analysis_report(data, results)

            print(f"\n分析完成！请在以下目录查看结果：{self.analysis_dir}")
            return results

        except Exception as e:
            print(f"分析过程中发生错误: {str(e)}")
            return None


if __name__ == "__main__":
    # 使用示例
    analyzer = WidthAnalysis()
    # 替换为实际的Excel文件路径
    file_path = "钢种数据分析结果.xlsx"
    results = analyzer.run_analysis(file_path)
