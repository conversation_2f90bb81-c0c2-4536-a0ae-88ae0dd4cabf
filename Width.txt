       //---------------------------------------------------------------
       // Copyright (c) 2006 by
       // Toshiba Mitsubishi-Electric Industrial Systems Corp. 
       // TMGE Automation Systems LLC, U.S.A.
       // Published in only a limited, copyright sense, and all
       // rights, including trade secret rights are reserved.
       //---------------------------------------------------------------
//-----------------------------------------------------------------------------
//
//     TITLE:         Width Class Implementation
//
//     FILE NAME:     MDS\SOURCE\WIDTH.CXX
//
//     PREPARED BY:   TMGE Automation Systems LLC, U.S.A.
//                      1501 Roanoke Blvd., Salem, Virginia, USA
//                    Toshiba Mitsubishi-Electric Industrial Systems Corp.
//                      Mita 43 MT Bldg. Mita 3-13-16, Minato-ku Tokyo, Japan
//
//     CUSTOMER:      STANDARD
//
//     SYSTEM:        Mill Automation System
//
//--------------------------------------------------------------------------------------
//
//     REVISIONS:
//     level  review date  author        change content
//     -----  -----------  ------------  -----------------------------------------------
//     1.0-0  20-Sep-2006  FM Williams   Original
//--------------------------------------------------------------------------------------

//-----------------------------------------------------------------------------
//
// ABSTRACT:
//      This class contains methods that calculates spread and recovery in
//      roughing mill area as well as spread in the finishing mill area.  The 
//      methods handle all necessary unit conversions.
//
//
//     FUNCTION                 DESCRIPTION
//     -----------------------  -----------------------------------------------
//      Bkl_Lmt                 Calculates the buckling draft limit for edger
//      Tpr_Cor                 Calculates tapered edger max draft correction
//      Recovery                Calculates edger recovery
//      Spread                  Calculates horizontal stand spread
//      Fm_Spread               Calculates finishing mill area spread
//
//-----------------------------------------------------------------------------

#define WIDTH_CXX

#include "mathuty.hxx"
#include "alarm.hxx"
#include "physcon.hxx"
#include "width.hxx"

#ifdef WIN32
	#ifdef _DEBUG
	#define new DEBUG_NEW
	#endif
    #pragma warning(disable: 4244)  // double to float conversion (NT thinks constants are doubles)
#endif

// Diagnostic level specific to this file
//static const cGlobal::DiagnosticCodeEnum diagLvl(cGlobal::Setup);
static const int diagLvl = 0;

// Data schema for the cWidth class.
static cSchema::schema_type cWidth_schema[]=
{
    //Next  Enum  Schema details                            Fmt  Units        Comment
    //====  ====  ========================================  ==== ===========  ==================================================
    { NULL, NULL, SCHEMA_T (cWidth,bool, recov_sumit),      "",  "",          "if TRUE, use Sumitomo Recovery model" },
    { NULL, NULL, SCHEMA_T (cWidth,bool, sprd_sumit),       "",  "",          "if TRUE, use Sumitomo Spread model" },
    { NULL, NULL, SCHEMA_T (cWidth,float,recov_dslope),     "",  "",          "slope of recovery modifier" },
    { NULL, NULL, SCHEMA_T (cWidth,float,grvmult_min),      "",  "",          "minimum amount of groove multiplier" },
    { NULL, NULL, SCHEMA_T (cWidth,float,grvmult_max),      "",  "",          "maximum amount of groove multiplier" },
    { NULL, NULL, SCHEMA_T (cWidth,float,grv_m),            "",  "",          "slope of correction" },
    { NULL, NULL, SCHEMA_T (cWidth,float,grv_b),            "",  "",          "offset of correction" },
    { NULL, NULL, SCHEMA_T (cWidth,float,bkl_mult),         "",  "",          "buckling multiplier" },
    { NULL, NULL, SCHEMA_T (cWidth,int,  max_wid_red_pts),  "",  "",          "number of points for max_wid_red_y array" },
    { NULL, NULL, SCHEMA_T1(cWidth,float,wid_thk_rat_x,10), "",  "",          "width to thickness ratio array (x-values)" },
    { NULL, NULL, SCHEMA_T1(cWidth,float,max_wid_red_y,10), "",  "%",         "maximum width reduction array (y-values)" },
    { NULL, NULL, SCHEMA_T1(cWidth,float,fm_sprd_min,num_stl_fam+1),     "",  "mm_in",     "minimum amount of FM spread for this steel family" },
    { NULL, NULL, SCHEMA_T1(cWidth,float,fm_sprd_max,num_stl_fam+1),     "",  "mm_in",     "maximum amount of FM spread for this steel family" },
    { NULL, NULL, SCHEMA_T2(cWidth,float,fm_sprd_coeff,num_stl_fam+1,4), "",  "",          "FM spread regression coefficients for this steel family" },
    { NULL, NULL, SCHEMA_T1(cWidth,float,recov_mod,num_stl_fam+1),"",  "",    "Recovery modifier" },
    { NULL, NULL, SCHEMA_T1(cWidth,float,sprd_mod,num_stl_fam+1), "",  "",    "Spread modifier" },
    { 0 }   // terminate list
};

// Link all the schema's together
cSchema::schema_name_type cWidth::sSchema[]=
{
    { 
        "cWidth",                           // name
        sizeof(cWidth),                     // size
        cWidth_schema,                      // schema
        false,                              // packed
        false,                              // allow ptr
        false,                              // Read only
        "Width class configuration",        // comment
        0                                   // offset to config data
    },
    { 0 } // terminate list
};

// Use this constructor if no hash table support required
cWidth::cWidth()
{
    Set_Class_Name("cWidth");
    Set_Schema("cWidth",sSchema);

    // Zero out member data
    Zero_Data(this, sizeof(cWidth), Get_Schema("cWidth"));

}

// Use this constructor if hash table support required
cWidth::cWidth( const MString        &objName,
                const objTypEnum    targets,
                const objPosEnum    position,
                void                *pHash,
                const int           size )
                : cBase( objName, targets, position, pHash, size, 0 )
{
    Set_Class_Name("cWidth");
    Set_Schema("cWidth",sSchema);

    // Zero out member data
    Zero_Data(this, sizeof(cWidth), Get_Schema("cWidth"));

}

//---------------------------------------------------------------------
// Virtual function to allow the user to carry out post processing after
// a config file has been read.  In this case set the global pcWidth
// pointer to point to the object.  NOTE this means that pcWidth points
// to the last width object configured.
//---------------------------------------------------------------------
bool        cWidth::Post_Config(
                        char *name,         // name of schema
                        void *psStruct)     // address of binary structure
                                            // holding configured data
{
    pcWidth = (cWidth *)(this);
    return true;
}

// cWidth     Width;     // create an instance for general use


// ---------------------------------------------------------------
//  This function calculates the edger recovery amount.
//
//   Configured Variables:
//   recov_sumit                  // [-] if TRUE use Sumitomo method
//   recov_dslope                 // [-] recovery slope 
//   grvmult_min                  // [mm_in] minimum amount of groove multiplier
//   grvmult_max                  // [mm_in] maximum amount of groove multiplier
//   grv_m                        // [-] slope of correction
//   grv_b                        // [-] offset of correction
// ---------------------------------------------------------------
bool cWidth::Recovery(
                 int     family,    // IN  [-]      steel family
                 float   thick,     // IN  [mm_in]  entry slab thickness
                 float   wid,       // IN  [mm_in]  entry slab width
                 float   draft,     // IN  [mm_in]  edger draft
                 float   diam,      // IN  [mm_in]  edger roll diameter
                 bool    grooved,   // IN  [-]      if true, edger is grooved
                 float   throat,    // IN  [mm_in]  grooved edger throat
                 float   angle,     // IN  [degree] grooved edger angle
                 float   diam_max,  // IN  [mm_in]  grooved edger max diameter
                 float   diam_min,  // IN  [mm_in]  grooved edger min diameter
                 float&  grvmult,   // OUT [-]      grooved edger multiplier
                 float&  recovery,  // OUT [mm_in]  edger recovery
                 float&  effi       // OUT [-]      edging efficincy
                     )

{
    //-----------------------
    // Define local variables
    //-----------------------
    float a, b, c, d;
    float agrv, dgrv, sgrv, area, grv_depth;

    //---------------------------------------------
    // Initialize return values for zero edge draft
    //---------------------------------------------
    grvmult     = 1.0F;
    recovery    = 0.0F;
    effi        = 1.0F;

    //----------------------
    // Check entry dimension
    //----------------------
    if ( thick <= 0.0F ||
         wid   <= 0.0F ||
         diam  <= 0.0F   )
    {
        // Output alarm for INVALID input data
        DMSG(diagLvl) 
            << "Recovery: INVALID input data "
            << END_OF_MESSAGE;

        return false;
    }
    else if ( draft <= 0.0 )
    {
        // force a valid condition for 0 edge draft
        return true;
    }

    //----------------------------------
    // Check for Grooved drafting edger
    //----------------------------------
    if ( grooved && draft > 0.0F && wid > 0.0F )
    {
        grv_depth = ( diam_max - diam_min ) / 2.0F;

        // ---------------------------
        //    Check for grooved edger.
        // ---------------------------
        if ( grv_depth > 0.0F )
        {
            // -------------------------------------
            //    Extra opening due to groove angle.
            // -------------------------------------
            sgrv = tan( angle * Physcon.radpdeg ) * grv_depth;
            // ---------------------------------------------------------
            //    Area of groove cross section available for a dog bone.
            // ---------------------------------------------------------
            area = ( throat + sgrv - thick ) * 2.0F * grv_depth;
            agrv = cMathUty::Max( 1.0F, area );
            // -----------------------
            //  Area of edger draft.
            // -----------------------
            dgrv = draft * thick;

            if ( dgrv > agrv )
            {
                // ----------------------------------
                //    The groove will have an effect.
                // ----------------------------------
                grvmult = sqrt( agrv / dgrv * sqrt( wid / diam ) );
                grvmult = grvmult * this->grv_m + this->grv_b;
                grvmult = cMathUty::Clamp( grvmult, 
                                       this->grvmult_min, 
                                       this->grvmult_max );
            }
        }
    }   // grooved && draft > 0.0F && wid > 0.0F


    //-------------------
    // Calculate recovery
    //-------------------
    if ( this->recov_sumit )
    {
        // -------------------
        //  Sumitomo version
        // -------------------
        a = - 1.887F * pow( (draft / wid), 0.063F );
        b = pow( (thick / (diam / 2.0F)), 0.441F );
        c = pow( ((diam / 2.0F) / wid), 0.989F );
        d = pow( (wid/(wid - draft)), 7.591F );
        recovery = exp( a * b * c * d );									这两个恢复量计算的区别是什么？什么时候用哪个？  F有什么具体意义？
    }
    else
    {
        // -------------------------
        //  New simplified version
        // -------------------------
        a = 0.500F + this->recov_dslope * 12.25F * draft / wid;
        b = ( 1.272F * sqrt( thick * diam / 2.0F ) ) / wid;
        recovery = exp( -1.877F * a * b );
    }

    recovery = recovery * draft * this->recov_mod[family] * grvmult;

    //----------------------------
    // Calculate edging efficiency
    //----------------------------
    effi = 1.0F - recovery / draft;

    return true;

} // cWidth::Recovery

//-----------------------------------------------------------------
// This function calculates the tapered edger max draft correction.
//     max_draft = max_draft - tprcor;
//
//   Configured Variables:
//-----------------------------------------------------------------
float   cWidth::Tpr_Cor(
                 float   thick,       // IN  [mm_in]  entry slab thickness
                 float   angle        // IN  [degree] tapered edger angle
                       )              // OUT [mm_in]  tprcor
{
    float tprcor = 0.0F;   // [mm_in] tapered edger max draft correction
                           //         to be calculated

    // ---------------------------
    //    Check for tapered edger.
    // ---------------------------
    if ( angle > 0.0F)
    {
        // -------------------------------------------------
        //  Calculate the tapered edger max draft correction
        // -------------------------------------------------
        tprcor = tan( angle * Physcon.radpdeg ) * thick;

    }

    return tprcor;

} // cWidth::Tpr_Cor


// -----------------------------------------------------------------
//  This function calculates the maximum edger draft to prevent
//  slab buckling for a given slab width and thickness.
//
//   Configured Variables:
//   bkl_mult               [-] Buckling multiplier
//   max_wid_red_pts        [-] number of points for array
//   wid_thk_rat_x[]        [-] width to thickness ratio array
//   max_wid_red_y[]        [%] maximum width reduction array
// -----------------------------------------------------------------
float   cWidth::Bkl_Lmt(
                 float   thick,     // IN  [mm-in]  entry slab thickness
                 float   width      // IN  [mm-in]  entry slab width
                       )            // OUT [mm-in]  buckling "darft" limit
{
    float bkllmt = 0.0F;

    // Limit check the input data
    if ( 0.0F == thick || 0.0F == width )
    {
        // Alarm for INVALID input data
        EMSG << "Bkl_Lmt:INVALID input data - width or thick = 0 "
             << END_OF_MESSAGE;
        return bkllmt;
    }

    // Calculate width to thickness ratio
    float wid_thk_rat = width / thick;

    // Get maximum width reduction [%]
    float max_wid_red = cMathUty::rlnint( 
                                       &wid_thk_rat,
                                       this->wid_thk_rat_x,
                                       this->max_wid_red_y,
                                       (int*)&this->max_wid_red_pts );

    //  Calculate buckling "draft" limit
    bkllmt = this->bkl_mult * width * max_wid_red / 100.0F;

    return bkllmt;

} // cWidth::Bkl_Lmt


// ----------------------------------------------------------------------------
//  This function calculates the amount of spread due to horizontal draft via
//  Sumitomo or Sedlaczek's equations										这两个代表什么含义？粗轧的水平展宽计算
//
//   Configured Variables:
//   sprd_sumit                  // [-] if TRUE use Sumitomo method
// ----------------------------------------------------------------------------
float   cWidth::Spread(
                 int     family,    // IN  [-]     steel family
                 float   thick,     // IN  [mm_in] entry slab thickness
                 float   wid,       // IN  [mm_in] entry slab width
                 float   draft,     // IN  [mm_in] horizontal stand draft
                 float   diam       // IN  [mm_in] horizontal stand avg roll diametetr
                      )             // OUT [mm_in] spread
{
    float spread;                    // [mm_in] horizontal stand spread to be calculated

#ifdef ALUMINUM_HSM																这个是计算什么材料时候用到的水平展宽？
// From "A New Spread Formula for Hot Flat Rolling of Aluminum Alloys", T. Sheppard and X. Duan.			

		double l, hm, r_h1, w1_h1, hm_l, w1_l;
		double p1, p2, p3;
		double x, ch;

		l = sqrt(0.5*diam*draft);

		hm = thick - 0.5*draft;

		r_h1 = diam*0.5/thick;
		if (r_h1 < 1.0)
			r_h1 = 1.0;

		w1_h1 = wid/thick;

		hm_l = hm/l;
		if (hm_l < 0.42)
			hm_l = 0.42;

		w1_l = wid/l;
		if (w1_l > 28.6)
			w1_l = 28.6;

		x = exp(0.1071*w1_l);
		p1 = pow(r_h1,-0.848);
		p2 = pow(w1_h1,-1.481);
		p3 = pow(hm_l,-2.6978);

		ch = 0.2187*x*p1*p2*p3;

		spread = exp(ch) - 1.0;
#else
    float wgratio, a, b, c, x, ch;   // local variables
    if ( sprd_sumit )
    {

		// -------------------
        //  Sumitomo version
        // -------------------
        wgratio = wid/thick;
        x = sqrt( (diam / 2.0F) * draft );

        // ------------------------------------------------
        //    a = -1.64 * (wgratio ** 0.376);
        // ------------------------------------------------
        a = -1.64F * pow( wgratio, 0.376F );
        // ------------------------------------------------
        //    b = (wid/x) ** (0.016 * wgratio);
        // ------------------------------------------------
        b = pow( (wid / x), (0.016F * wgratio) );
        // ----------------------------------------------------
        //    c = (thick / (diam/2.0) ) ** (0.015 * wgratio);
        // ----------------------------------------------------
        c = pow( (thick / (diam / 2.0F)), (0.015F * wgratio) );
        ch = exp ( a * b * c);
        // -------------------------------------------------------------------
        //    spread  = ((( thick / (thick - draft)) ** ch) - 1.0) * wid;
        // -------------------------------------------------------------------
        spread = pow((thick / (thick - draft)), ch) - 1.0F; 
    }
    else
    {
        // ---------------------
        //  Sedlaczek version
        // ---------------------
        spread = draft * sqrt( wid * diam/2.0F ) /
                 (wid * wid + thick * (thick - draft));
    }
#endif                   

    spread = spread * wid * this->sprd_mod[family];

	//--------------------------
	// Spread cannot be negative
	//--------------------------
    if( spread < 0.0F )
	{
        spread = 0.0F;
	}

    return spread;

} // cWidth::Spread

// ----------------------------------------------------------------------------
//   This function calculates the amount of spread in finnishing area.
//   The FM spread model is based on a regresssion line analysis of width
//   data from existing installation.
//  
//   The general form of the spread equation is as follows:
//  
//       fm_spread := A0 + A1*sqrt(gauge) + A2*width + A3*draft;
//  
//   Where the regression coefficients A0-A3 are obtained by linear
//   regression using offline statistical analysis model.
//
//   Configured Variables:
//   fm_sprd_coeff[family][4]   // [-] FM spread regression coefficients for
//                              //         this steel family
//   fm_sprd_min[family]        // [mm_in] minimum amount of FM spread for
//                              //         this steel family
//   fm_sprd_max[family]        // [mm_in] maximum amount of FM spread for
//                              //         this steel family
// ----------------------------------------------------------------------------
float   cWidth::Fm_Spread(
                 int     family,     // IN  [-]     steel family        
                 float   gauge,      // IN  [mm_in] finishing target thickness
                 float   width,      // IN  [mm_in] finishing target width
                 float   draft,       // IN  [mm_in] finishing area thickness reduction			这个厚度减小量指的是什么？ 是精轧每个道次的厚度压下量？
                 float   a,   //Added by mjh 20150319
                 float   b,   //Added by mjh 20150319
                 float   c,   //Added by mjh 20150319
                 float   d   //Added by mjh 20150319
									 )                   // OUT [mm_in] finishing area spread
{
    float   fm_spread;               // FM spread amount at RMX temerature

    //fm_spread =   this->fm_sprd_coeff[family][0]                         //deleted by mjh 20150319
    //            + this->fm_sprd_coeff[family][1] * sqrt(gauge)
    //            + this->fm_sprd_coeff[family][2] * width
    //            + this->fm_sprd_coeff[family][3] * draft;
    fm_spread =   a + b * sqrt(gauge) + c * width + d * draft;           //deleted by mjh 20150319

    //------------------------------------
    //   Clamp predicted fm_spread value
    //------------------------------------
    fm_spread = cMathUty::Clamp( fm_spread, 
                                 this->fm_sprd_min[family],
                                 this->fm_sprd_max[family] );

    return fm_spread;

} // cWidth::Fm_Spread

