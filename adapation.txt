//-----------------------------------------------------------------------------
//
// ABSTRACT:
//
//     This package contains RSU setup adaptation calculations.
//
//     FUNCTION/PROCEDURE/TASK  DESCRIPTION
//     -----------------------  -----------------------------------------------
//      Error_Adapt             Given error vs an independent variable x in
//                               the form of a set of error values and a first
//                               set of associated x values, this procedure
//                               determines new error values for a second set
//                               of given x values.
//
//      Force_Multiplier        A function returns stand Force multiplier
//                                required for force calculations
//
//      Power_Multiplier        A function returns stand Power multiplier
//                                required for power calculations
//
//      Grt_Index               A function which calculates the GRT index (indgrt)
//
//      Initialize              Initialize setup data
//
//      Flow_Stress             Calculates flow stress for setup
//
//      Get_Ystress             Calculates yield stress at given temperature
//
//      Gain_Lmt_Selection      Procedure to determine gains and limits
//-----------------------------------------------------------------------------

#define ADAPTATION_CXX

//----------------------
// system include files
//----------------------
#include <iostream>

//---------------------
// mds include files
//---------------------
#include "alarm.hxx"
#include "flowstress.hxx"
#include "mathuty.hxx"
#include "mdlparam.hxx"
#include "millparam.hxx"
#include "objchain.hxx"
#include "physcon.hxx"
#include "rollbite.hxx"
#include "rollpair.hxx"
#include "utility.hxx"

//---------------------
// shared include files
//---------------------
#include "pce.hxx"

//---------------------
// rsu include files
//---------------------
#include "adaptation.hxx"
#include "edg.hxx"
#include "feedback.hxx"
#include "pass.hxx"
#include "sched.hxx"
#include "std.hxx"

//---------------------
// records include files
//---------------------
#include "pdi.hxx"
#include "ragp.hxx"
#include "ramp.hxx"
#include "rapp.hxx"
#include "rmt.hxx"
#include "sgp.hxx"
#include "rsys.hxx"

#ifdef WIN32
    #ifdef _DEBUG
    #define new DEBUG_NEW
    #endif
    #pragma warning(disable: 4244)  // double to float conversion (NT thinks constants are doubles)
    #pragma warning(disable: 4305) // truncation from 'const double' to 'const float'
#endif

// Diagnostic level specific to this file
static cAlarm::DiagnosticCodeEnum diagLvl(cAlarm::Adaptation);

// Data schema for the cAdaptation class.
static cSchema::schema_type cAdaptation_schema[]=
{
    //Next  Enum  Schema details                            Fmt  Units        Comment
    //====  ====  ========================================  ==== ===========  ==================================================
    { NULL, NULL, SCHEMA_T(cAdaptation,bool,frc_mult_enabled), "", "",        "force multiplier enable boolean" },
    { NULL, NULL, SCHEMA_T(cAdaptation,float,frc_mult_hi_gain), "", "",       "force multiplier hi gain" },
    { NULL, NULL, SCHEMA_T(cAdaptation,float,frc_mult_lo_gain), "", "",       "force multiplier lo gain" },
    { NULL, NULL, SCHEMA_T(cAdaptation,float,frc_mult_hi_lim), "", "",        "force multiplier hi limit" },
    { NULL, NULL, SCHEMA_T(cAdaptation,float,frc_mult_lo_lim), "", "",        "force multipler lo limit" },

    { NULL, NULL, SCHEMA_T(cAdaptation,bool,pwr_mult_enabled), "", "",        "power multiplier enable boolean" },
    { NULL, NULL, SCHEMA_T(cAdaptation,float,pwr_mult_hi_gain), "", "",       "power mutiplier hi gain" },
    { NULL, NULL, SCHEMA_T(cAdaptation,float,pwr_mult_lo_gain), "", "",       "power multiplier lo gain" },
    { NULL, NULL, SCHEMA_T(cAdaptation,float,pwr_mult_hi_lim), "", "",        "power multiplier hi limit" },
    { NULL, NULL, SCHEMA_T(cAdaptation,float,pwr_mult_lo_lim), "", "",        "power multiplier lo limit" },

    { NULL, NULL, SCHEMA_T(cAdaptation,bool,fs_mult_enabled), "", "",         "FS multiplier enable boolean" },
    { NULL, NULL, SCHEMA_T(cAdaptation,float,fs_mult_hi_gain), "", "",        "FS multiplier high gain" },
    { NULL, NULL, SCHEMA_T(cAdaptation,float,fs_mult_lo_gain), "", "",        "FS multiplier lo gain" },
    { NULL, NULL, SCHEMA_T(cAdaptation,float,fs_mult_hi_lim), "", "",         "FS multiplier hi limit" },
    { NULL, NULL, SCHEMA_T(cAdaptation,float,fs_mult_lo_lim), "", "",         "FS mutliplier lo limit" },

    { NULL, NULL, SCHEMA_T(cAdaptation,bool,fs_tmp_mult_enabled), "", "",     "FS temperature mult enable boolean" },
    { NULL, NULL, SCHEMA_T(cAdaptation,float,fs_tmp_mult_hi_gain), "", "",    "FS temperature curve multiplier high gain" },
    { NULL, NULL, SCHEMA_T(cAdaptation,float,fs_tmp_mult_lo_gain), "", "",    "FS temperature curve multiplier lo gain" },
    { NULL, NULL, SCHEMA_T(cAdaptation,float,fs_tmp_mult_hi_lim), "", "",     "FS temperature curve multiplier hi limit" },
    { NULL, NULL, SCHEMA_T(cAdaptation,float,fs_tmp_mult_lo_lim), "", "",     "FS temperature curve multiplier lo limit" },
    { NULL, NULL, SCHEMA_T(cAdaptation,int,fs_curve_anchor),      "", "",     "FS temperature curve anchor point" },

    { NULL, NULL, SCHEMA_T(cAdaptation,bool,plim_mult_enabled),   "", "",     "Power limit multiplier enable boolean" },
    { NULL, NULL, SCHEMA_T(cAdaptation,float,plim_mult_gain),     "", "",     "Power limit multiplier gain" },
    { NULL, NULL, SCHEMA_T(cAdaptation,float,plim_ratio_hi_lim),  "", "",     "Power limit ratio hi limit" },
    { NULL, NULL, SCHEMA_T(cAdaptation,float,plim_ratio_lo_lim),  "", "",     "Power limit ratio lo limit" },
    { NULL, NULL, SCHEMA_T(cAdaptation,float,plim_mult_hi_lim),   "", "",     "Power limit multiplier hi limit" },
    { NULL, NULL, SCHEMA_T(cAdaptation,float,plim_mult_lo_lim),   "", "",     "Power limit multiplier lo limit" },
    { NULL, NULL, SCHEMA_T(cAdaptation,float,plim_mult_dbnd),     "", "",     "Power limit multiplier deadband" },

    { NULL, NULL, SCHEMA_T(cAdaptation,bool,ztmp_corr_enabled), "", "",       "ztmp corr term enabled" },
    { NULL, NULL, SCHEMA_T(cAdaptation,float,ztmp_corr_gain), "", "",         "ztmp corr term gain" },
    { NULL, NULL, SCHEMA_T(cAdaptation,float,ztmp_corr_hi_lim), "", "C_F",    "ztmp corr hi lim" },
    { NULL, NULL, SCHEMA_T(cAdaptation,float,ztmp_corr_lo_lim), "", "C_F",    "ztmp corr lo lim" },

    { NULL, NULL, SCHEMA_T(cAdaptation,int,gain_swt_lim), "", "",             "gain switch from high to low" },

    { NULL, NULL, SCHEMA_T(cAdaptation,int,max_frc_samples), "", "",          "force error statistics" },
    { NULL, NULL, SCHEMA_T(cAdaptation,int,max_fs_samples), "", "",           "flow stress multiplier statistics" },

    { NULL, NULL, SCHEMA_T(cAdaptation,float, gain_tmp_err),  "", "",         "Temp error gain" },
    { NULL, NULL, SCHEMA_T(cAdaptation,float, gain_tmp_ver),  "", "",         "temp vernier gain" },
    { NULL, NULL, SCHEMA_T(cAdaptation,float,  pi_intr_gain), "", "",         "Integral gain" },
    { NULL, NULL, SCHEMA_T(cAdaptation,float, pi_prop_gain),  "", "",         "Proportional gain" },
    { NULL, NULL, SCHEMA_T(cAdaptation,float, ftime_min),     "", "",         "Minimum furnace to rougher travel time" },
    { NULL, NULL, SCHEMA_T(cAdaptation,float, ftime_max),     "", "",         "Maximum furnace to rougher travel time" },
    { NULL, NULL, SCHEMA_T(cAdaptation,float, ftime_gain),    "", "",         "Furnace to rougher travel time gain" },
    { NULL, NULL, SCHEMA_T(cAdaptation,float, rtime_min),     "", "",         "Minimum rme to rollbite travel time" },
    { NULL, NULL, SCHEMA_T(cAdaptation,float, rtime_max),     "", "",         "Maximum rme to rollbite travel time" },
    { NULL, NULL, SCHEMA_T(cAdaptation,float, rtime_gain),    "", "",         "Rme to rollbite travel time gain" },
    { NULL, NULL, SCHEMA_T(cAdaptation,float, srf_mean_bf),   "", "",         "Weighring factor for surf/mean - furnace" },
    { NULL, NULL, SCHEMA_T(cAdaptation,float, fce_temp_error_max), "",  "",   "Maximum allowed furnace temp error" },
    { NULL, NULL, SCHEMA_T(cAdaptation,float, wid_vern_gain), "",  "",        "Width vernier update gain" },
    { NULL, NULL, SCHEMA_T(cAdaptation,float, wid_vern_cl), "",  "",          "Width vernier clamp" },
    { NULL, NULL, SCHEMA_T(cAdaptation,float, wid_error_cl), "",  "",         "Width error clamp" },
    { NULL, NULL, SCHEMA_T(cAdaptation,float, temp_err_cl),     "",  "C_F",   "Temperature error clamp" },
    { NULL, NULL, SCHEMA_T(cAdaptation,float, temp_err_lim),    "",  "C_F",   "head temperature error limit" },
    { NULL, NULL, SCHEMA_T(cAdaptation,float, temp_ver_cl),     "",  "C_F",   "temp_ver_cl clamp (for limiting temp vernier)" },

    { 0 }   // terminate list
};
// Link all the schema's together
cSchema::schema_name_type cAdaptation::sSchema[]=
{
    //
    {
        "cAdaptation",                      // name
        sizeof(cAdaptation),                // size
        cAdaptation_schema,                 // schema
        false,                              // packed: true for STRUCTURE, false for CLASS
        false,                              // allow pointer
        false,                              // read only
        "Adaptation configuration data",    // comment
        0                                   // offset to config data
    },
    { 0 } // terminate list
};


//----------------------------------------------
// Declare variables for Error_Adapt routine use
//----------------------------------------------
const int ea_array_size = 100;
float ea_error_new[ea_array_size];
int   ea_index_start;
int   ea_index_end;
float ea_error_old[ea_array_size];
float ea_x_old[ea_array_size];
float ea_x_new[ea_array_size];
int   ea_x_old_last;
int   ea_x_new_last;

cAdaptation::cAdaptation()
{
    Set_Class_Name("cAdaptation");
    Set_Schema("cAdaptation",sSchema);

    // Zero out member data
    Zero_Data(this, sizeof(cAdaptation), Get_Schema("cAdaptation"));
}

// Use this constructor if no hash table support required
cAdaptation::cAdaptation( const MString        &objName,
                          const objTypEnum     objType,
                          const objPosEnum     objPosition,
                          void                *pHash,
                          const int            size )
                          : cBase( objName, objType, objPosition, pHash, size, 0)
{
    Set_Class_Name("cAdaptation");
    Set_Schema("cAdaptation",sSchema);

    // Zero out member data
    Zero_Data(this, sizeof(cAdaptation), Get_Schema("cAdaptation"));
}

//------------------------------------
// cAdaptation DECONSTRUCTOR ABSTRACT:
//------------------------------------
//cAdaptation::~cAdaptation();

//---------------------------------------------------------------------
// Virtual function to allow the user to carry out post processing after
// a config file record has been read.
//---------------------------------------------------------------------
bool    cAdaptation::Post_Config(
                            char *name,       // name of schema
                            void *psStruct)   // address of binary structure
                                              // holding configured data
{
    pcAdaptation = this;
    return true;
}

//----------------------------------------------------------
// cAdaptation::dump
//----------------------------------------------------------
void cAdaptation::dump(const bool composed)
{
    Dump_Data(stdout, "cAdaptation", this, 0, (const char *)this->objName());

    if (composed)
    {
        ; // NULL
    }
}


//------------------------------------------------------------------------
// Procedure :: Error_Adapt
// Given error vs an independent variable x in the form of a set of error
// values and a first set of associated x values, this procedure determines
// new error values for a second set of given x values.
//------------------------------------------------------------------------
void cAdaptation::Error_Adapt (
        float  ea_error_new[],         // OUT
        int&   ea_index_start,         // OUT
        int&   ea_index_end,           // OUT
        float  ea_error_old[],         // IN
        float  ea_x_old[],             // IN
        float  ea_x_new[],             // IN
        int    ea_x_old_last,          // IN
        int    ea_x_new_last )         // IN
{

    // Local declarations
    float  error_pack[ea_array_size];
    float  x_pack[ea_array_size];

    float  avg_error   = 0.0;
    float  avg_x       = 0.0;
    int    index_x_new = 0;
    int    index_x_old = 0;
    int    loc_index_start = 0;
    int    loc_index_end;
    int    num_pts_ave     = 2;
    int    num_pts_pack;
    int    shift_pt        = 1;
    int j;

    loc_index_end = ea_x_new_last;
    num_pts_pack  = ea_x_old_last;

    //-------------------
    // initialize outputs
    //-------------------
    for(int i=0; i<ea_array_size; i++ )
    {
        ea_error_new[i] = 0.0;
        error_pack[i] = 0.0;
        x_pack[i] = 0.0;
    } // end loop

    ea_index_start = 0;
    ea_index_end = 0;


    //--------------------------------------------------------------
    // If no intersection of old x values associated with input error
    // and new x values, alarm and raise exception.
    //---------------------------------------------------------------
    if( (ea_x_old[ea_x_old_last] < ea_x_new[0]) ||
        (ea_x_old[0] > ea_x_new[ea_x_new_last]) )
    {

        DMSG(diagLvl)
            << " : adaptation.Error_Adapt: Invalid inputs "   << NEWLINE
            << " ea_x_old_last=  " << ea_x_old_last           << NEWLINE
            << " ea_x_old[last]= " << ea_x_old[ea_x_old_last] << NEWLINE
            << " ea_x_new[0]=    " << ea_x_new[0]             << NEWLINE
            << " ea_x_old[0]=    " << ea_x_old[0]             << NEWLINE
            << " ea_x_new_last=  " << ea_x_new_last           << NEWLINE
            << " ea_x_new[last]= " << ea_x_new[ea_x_new_last]
            << END_OF_MESSAGE;

        return;

    } // end if

    // Copy old values to local working arrays
    for( j=0; j<=ea_x_old_last; j++ )
    {
        x_pack[j] = ea_x_old[j];
        error_pack[j] = ea_error_old[j];
    } // end loop

    // Find old x value index pointing to first (least) old x value
    // within the new x values range.
    // This index was initialized to first element of old x array.
    while(x_pack[index_x_old] <= ea_x_new[0])
    {
       index_x_old = index_x_old + 1;
    } // end loop
    //---------------------------------------------------------------------
    // If more than 1 old x value falls between any 2 new x values, average
    // the old x values and their associated error, replace the first value
    // with the average, then pack the arrays toward their first elements
    // replacing unused elements.  This yields either 0 or 1 old x value
    // between any 2 new x value.
    //---------------------------------------------------------------------
    // index_x_new initialized to first element of those arrays

    // Exit when no more valid old x values
    // or no more new x intervals to consider.

    while( (index_x_old < num_pts_pack) && (index_x_new < ea_x_new_last - 1) )
    {
        // If 1 old x value in this new x interval:
        if( x_pack[index_x_old] >= ea_x_new[index_x_new]
                &&  x_pack[index_x_old] < ea_x_new[index_x_new + 1] )
        {

            // If 2 or more old x values in this new x interval
            if( x_pack[index_x_old + 1] < ea_x_new[index_x_new + 1] )
            {
                // Found 2 or more old x values in this new x interval.
                // Calculate average old x and average old error for these 2 points.
                // Initialize number of points in this new x interval to 2.
                // Calculate running averages for 3 or more points, if required.
                avg_x = ( x_pack[index_x_old]
                             + x_pack[index_x_old + 1] ) / 2.0F;
                avg_error = ( error_pack[index_x_old]
                             + error_pack[index_x_old + 1] ) / 2.0F;
                num_pts_ave = 2;

                // Exit when no more old x values to consider (this exit must be first)
                // or no more old x values in this new x interval.
                while( ((index_x_old + num_pts_ave - 1) < num_pts_pack) &&
                        (x_pack[index_x_old + num_pts_ave] <= ea_x_new[index_x_new + 1]) )
                {

                    // Increment number old x in this new x interval.
                    // Do running averages:
                    num_pts_ave = num_pts_ave + 1;

                    avg_x = avg_x
                        + ( (x_pack[index_x_old + num_pts_ave - 1] - avg_x)
                        / (float) num_pts_ave );

                    avg_error = avg_error
                        + ( (error_pack[index_x_old + num_pts_ave - 1] - avg_error)
                        / (float) num_pts_ave );

                } // end while

                // Determine the number of valid data points in the packed arrays.
                // Copy average values into the packed arrays.
                num_pts_pack = num_pts_pack - num_pts_ave + 1;
                x_pack[index_x_old] = avg_x;
                error_pack[index_x_old] = avg_error;

                // Shift the remaining points down, if required, to pack the array.
                shift_pt = index_x_old + 1;

                while(shift_pt <= num_pts_pack)
                {
                    x_pack[shift_pt] = x_pack[shift_pt + num_pts_ave - 1];
                    error_pack[shift_pt] = error_pack[shift_pt + num_pts_ave - 1];
                    shift_pt = shift_pt + 1;

                } // end while

            } // end if


            // Found 1 or more old x values in this new x interval.
            // Increment old x index:
            index_x_old = index_x_old + 1;

        } // end if

        index_x_new = index_x_new + 1;
    } // end infinite loop

    //-----------------------------------------------------------------
    // Build new error values vs new x values curve.
    //-----------------------------------------------------------------
    // Determine start & end new x indices for linear interpolation.
    loc_index_start = 0;

    while( ea_x_new[loc_index_start] <= x_pack[0] )
    {
        loc_index_start = loc_index_start + 1;
    } // end loop

    loc_index_end = ea_x_new_last;
    while( ea_x_new[loc_index_end] >= x_pack[num_pts_pack] )
    {
        loc_index_end = loc_index_end - 1;
    } // end loop

    // Determine new error values for those new x values
    // contained within old packed x values.
    for( j=loc_index_start; j<=loc_index_end; j++ )
    {

        ea_error_new[j] = cMathUty::rlnint (
                       (float *)&ea_x_new[j],
                       (float *)&x_pack,
                       (float *)&error_pack,
                       (int   *)&num_pts_pack );

    } // end loop

    //-----------------------------------------------------------------
    // If smallest packed x value is greater than smallest new x value:
    //   Calculate one more new error value, indexed on new x,
    //   based on it's value and location within new x interval.
    //   Decrement starting index to include this point.
    //-----------------------------------------------------------------
    if( loc_index_start > 0 )
    {
        ea_error_new[loc_index_start - 1] =
            error_pack[0]
                * ( x_pack[0] - ea_x_new[loc_index_start - 1] )
                / ( ea_x_new[loc_index_start] - ea_x_new[loc_index_start - 1] ) ;
        loc_index_start = loc_index_start - 1;

    } // end if

    //-----------------------------------------------------------------
    // If largest packed x value is less than largest new x value:
    //   Calculate one more new error, indexed on new x values,
    //   based on it's value and location within new x interval.
    //   Increment ending index to include this point.
    //-----------------------------------------------------------------
    if( loc_index_end < ea_x_new_last )
    {
        ea_error_new[loc_index_end + 1] =
            error_pack[num_pts_pack]
                * ( ea_x_new[loc_index_end + 1] - x_pack[num_pts_pack] )
                / ( ea_x_new[loc_index_end + 1] - ea_x_new[loc_index_end] ) ;
        loc_index_end = loc_index_end + 1;
    } // end if

    // Copy local indices for output:
    ea_index_start = loc_index_start;
    ea_index_end = loc_index_end;

    return;

} // END Error_Adapt


//----------------------------------------------------------------
// RAGP_Feedback() ABSTRACT
// This procedure updates values maintained in the RAGP model table
//----------------------------------------------------------------
void cAdaptation::RAGP_Feedback ( cSched*  pcSched )
{
    int     ps, pass_index;

    if ( pcSched->pcRAGP->adapted_state.perm_update )
    {
        // Bulk copy feedback state to adapted state
        pcSched->pcRAGP->adapted_state = pcSched->pcRAGP->fbk_state;

        // Check configured adapted flow stress multiplier flag, if not set
        // restore the setup values.
        if ( !this->fs_mult_enabled )
        {
            for ( ps=1; ps<=pcSched->pcFeedbackD->fbkpas; ps++ )
            {
                pass_index = pcSched->pcFbkPassD[ps]->ps_idx;

                pcSched->pcRAGP->adapted_state.fs_mult[pass_index] =
                                pcSched->pcRAGP->state.fs_mult[pass_index];

                pcSched->pcRAGP->adapted_state.fs_mult_count[pass_index] =
                                pcSched->pcRAGP->state.fs_mult_count[pass_index];

                pcSched->pcRAGP->adapted_state.fs_mult_sum[pass_index] =
                                pcSched->pcRAGP->state.fs_mult_sum[pass_index];

                pcSched->pcRAGP->adapted_state.fs_mult_ss[pass_index] =
                                pcSched->pcRAGP->state.fs_mult_ss[pass_index];

                pcSched->pcRAGP->adapted_state.fs_mult_mean[pass_index] =
                                pcSched->pcRAGP->state.fs_mult_mean[pass_index];

                pcSched->pcRAGP->adapted_state.fs_mult_sd[pass_index] =
                                pcSched->pcRAGP->state.fs_mult_sd[pass_index];
            }
        }
        else
        {
            pcSched->pcRAGP->adapted_state.updates++;
            pcSched->pcRAGP->adapted_state.last_update = Time::Get_Clock();
        }
    }

}   //  End RAGP_Feedback()


//----------------------------------------------------------------
// RAMP_Feedback() ABSTRACT
// This procedure updates values maintained in the RAMP model table
//----------------------------------------------------------------
void cAdaptation::RAMP_Feedback ( cSched*  pcSched )
{
    int     ps, pass_index;

    for ( ps=1; ps<=pcSched->pcFeedbackD->fbkpas; ps++ )
    {
        pass_index = pcSched->pcFbkPassD[ps]->ps_idx;

        pcSched->pcRAMP->fbk_state.pwr_lim_mult[pass_index] =
                        pcSched->pcFeedbackD->pwr_lim_mult[pass_index];
    }

    if ( pcSched->pcRAMP->adapted_state.perm_update )
    {
        // Bulk copy feedback state to adapted state
        pcSched->pcRAMP->adapted_state = pcSched->pcRAMP->fbk_state;

        pcSched->pcRAMP->adapted_state.updates++;
        pcSched->pcRAMP->adapted_state.last_update = Time::Get_Clock();

        strncpy(pcSched->pcRAMP->adapted_state.pr_grade,
                pcSched->pcPDI->state.grade_name,
                sizeof(pcSched->pcRAMP->adapted_state.pr_grade));

        pcSched->pcRAMP->adapted_state.pr_tgt_thick = pcSched->pcSuPce->rx_cold_thick;
        pcSched->pcRAMP->adapted_state.pr_thick_err = 0.0F;
        pcSched->pcRAMP->adapted_state.pr_tgt_tmp   = pcSched->pcSuPce->rmx_tgt_tmp;
        pcSched->pcRAMP->adapted_state.pr_tmp_err   = 0.0F;
        //pcSched->pcRAMP->adapted_state.pr_tgt_wid   = pcSched->pcSuPce->rx_cold_width;
        pcSched->pcRAMP->adapted_state.pr_tgt_wid   = pcSched->pcPDI->state.fxwaim;		//add by billy c 20150827
        pcSched->pcRAMP->adapted_state.pr_wid_err   = 0.0F;
		pcSched->pcRAMP->adapted_state.family_prv   = pcSched->pcSuPce->family;			//add by xubin 2012.10.9
		pcSched->pcRAMP->adapted_state.pr_pdi_rwid  = pcSched->pcPDI->state.slabw;		//add by billy c 20150827

		// Do not adapt furnace temp.vernier, if mill is in shadow_mode
        if ( pcSched->pcFeedbackD->pcFeedback->shadow_mode )
        {
            pcSched->pcRAMP->adapted_state.fce_tmp_vern[pcSched->pcPDI->state.fcenum-1] =
                        pcSched->pcRAMP->state.fce_tmp_vern[pcSched->pcPDI->state.fcenum-1];
        }
        // Check configured adapted power limit multiplier flag, if not set
        // restore the setup values.
        if ( !this->plim_mult_enabled )
        {
            for ( ps=1; ps<=pcSched->pcFeedbackD->fbkpas; ps++ )
            {
                pass_index = pcSched->pcFbkPassD[ps]->ps_idx;

                pcSched->pcRAMP->adapted_state.pwr_lim_mult[pass_index] =
                                pcSched->pcRAMP->state.pwr_lim_mult[pass_index];
            }
        }
    }

}   //  End     RAMP_Feedback()


//----------------------------------------------------------------
// RAPP_Feedback() ABSTRACT
// This procedure updates values maintained in the RAPP model table
//----------------------------------------------------------------
void cAdaptation::RAPP_Feedback ( cSched*  pcSched )
{
    int     ps, pass_index;

    if ( pcSched->pcRAPP->adapted_state.perm_update )
    {
        // Bulk copy feedback state to adapted state
        pcSched->pcRAPP->adapted_state = pcSched->pcRAPP->fbk_state;

        if ( this->frc_mult_enabled || this->pwr_mult_enabled )
        {
            pcSched->pcRAPP->adapted_state.updates++;
            pcSched->pcRAPP->adapted_state.last_update = Time::Get_Clock();
//=================================================================================start add by xubin 2012.10.9
			pcSched->pcRAPP->adapted_state.rmx_wid_off = pcSched->pcRAMP->fbk_state.rmx_wid_vern;

            pcSched->pcRAPP->adapted_state.fmx_wid_off = pcSched->pcRAMP->fbk_state.fmx_wid_vern;
//=================================================================================end add by xubin 2012.10.9
        }

        // Check configured adapted force multiplier flag, if not set
        // restore the setup values.
        if ( !this->frc_mult_enabled )
        {
            for ( ps=1; ps<=pcSched->pcFeedbackD->fbkpas; ps++ )
            {
                pass_index = pcSched->pcFbkPassD[ps]->ps_idx;

                pcSched->pcRAPP->adapted_state.force_mult[pass_index] =
                                pcSched->pcRAPP->state.force_mult[pass_index];

                pcSched->pcRAPP->adapted_state.frc_err_count[pass_index] =
                                pcSched->pcRAPP->state.frc_err_count[pass_index];

                pcSched->pcRAPP->adapted_state.frc_err_sum[pass_index] =
                                pcSched->pcRAPP->state.frc_err_sum[pass_index];

                pcSched->pcRAPP->adapted_state.frc_err_ss[pass_index] =
                                pcSched->pcRAPP->state.frc_err_ss[pass_index];

                pcSched->pcRAPP->adapted_state.frc_err_mean[pass_index] =
                                pcSched->pcRAPP->state.frc_err_mean[pass_index];

                pcSched->pcRAPP->adapted_state.frc_err_sd[pass_index] =
                                pcSched->pcRAPP->state.frc_err_sd[pass_index];
            }
        }

        // Check configured adapted power multiplier flag, if not set
        // restore the setup values.
        if ( !this->pwr_mult_enabled )
        {
            for ( ps=1; ps<=pcSched->pcFeedbackD->fbkpas; ps++ )
            {
                pass_index = pcSched->pcFbkPassD[ps]->ps_idx;

                pcSched->pcRAPP->adapted_state.power_mult[pass_index] =
                                pcSched->pcRAPP->state.power_mult[pass_index];
            }
        }
    }

}   //  End     RAPP_Feedback()


//-----------------------------------------------------------------
// Calc_Observed_Flowstress() ABSTRACT
//
// Calculate the observed flowstress for each pass to achieve the
// measured stand forces.  Use feedback calculated results.
//-----------------------------------------------------------------
void cAdaptation::Calc_Observed_Flowstress(
                        cSched  *pcSched,
                        cPassD  **pcPassD)
{
    int                     ps;
    int                     iseg;

    //-------------------------------------------------------------------------
    // For each pass, calculate the observed flowstress for measured body forces
    // and feedback temperatures.
    //-------------------------------------------------------------------------
    pcSched->pcFeedbackD->num_data_pts = 0;
    for ( ps=0; ps<=pcSched->pcFeedbackD->fbkpas; ps++ )
    {
        iseg = pcObjChain->Body_Index();

        // Check for none dummied stand
        if ( pcSched->pcFbkPassD[ps]->StdD_Present() &&
            !pcSched->pcFbkPassD[ps]->pcStdD[iseg]->dummied )
        {
            // Check for zero measured force.
            if ( pcSched->pcFbkPassD[ps]->pcStdD[iseg]->force_meas > 0.0F )
            {
                if ( pcSched->pcFbkPassD[ps]->pcStdD[iseg]->Observed_Flowstress() )
                {
                    pcSched->pcFeedbackD->num_data_pts += 1;
                }
                else
                {
                    EMSG << "Observed flow stress is invalid for pass " << ps
                         << END_OF_MESSAGE;
                }
            }
            else
            {
                    DMSG(-diagLvl) << "No observed flow stress for pass " << ps
                                   << " - force_meas = 0.0 "
                                   << END_OF_MESSAGE;
            }
        }
    }

    if ( pcSched->pcFeedbackD->num_data_pts < 2 )
    {
        DMSG(-diagLvl) << "Flow stress adaptation disabled - not enough valid data "
                       << pcSched->pcFbPce->prod_id
                       << END_OF_MESSAGE;

        return;
    }

    return;

}   //  End     Calc_Observed_Flowstress()

//-----------------------------------------------------------------
// Update_FSCurve() ABSTRACT
//
// Update the flowstress vs temperature curve.
//-----------------------------------------------------------------
void cAdaptation::Update_FSCurve(
                        cSched  *pcSched,
                        cPassD  **pcPassD)
{
    float   delta_fs[max_passes_rm+1];
    float   loc_temps[max_passes_rm+1];
    float   fs_tmp_gain;
    int     i, j, ps, num_temps;

    DMSG(-diagLvl) << "Calling Update_FSCurve()" << END_OF_MESSAGE;

    //-----------------------------------------------------------------
    // Exit if curve update is not enabled.
    //-----------------------------------------------------------------
    if ( !this->fs_tmp_mult_enabled )
    {
        return;
    }

    //-----------------------------------------------------------------
    // Exit if insufficient data points.
    //-----------------------------------------------------------------
    if ( pcSched->pcFeedbackD->num_data_pts < 2 )
    {
        DMSG(-diagLvl) << "Insufficient data points to adapt flowstress vs temperature curve"
                       << END_OF_MESSAGE;
        return;
    }

    //-----------------------------------------------------------------
    // Select gain term to use.
    //-----------------------------------------------------------------
    if ( pcSched->pcRAGP->fbk_state.updates < this->gain_swt_lim )
    {
        fs_tmp_gain = this->fs_tmp_mult_hi_gain;
    }
    else
    {
        fs_tmp_gain = this->fs_tmp_mult_lo_gain;
    }

    //-----------------------------------------------------------------
    // Build delta flowstress vs strip temperatures curve.
    // Strip temps in local array are in ascending order.
    //-----------------------------------------------------------------
    num_temps = -1;
    for( ps=pcSched->pcFeedbackD->fbkpas; ps>0; ps-- )
    {
        if( pcSched->pcFbkPassD[ps]->StdD_Present() &&
            pcSched->pcFbkPassD[ps]->pcStdD[1]->sigmat_obs_vld )
        {
            num_temps ++;
            //---------------------------------------------------------
            // Calculate flowstress error, observed - repredicted
            //---------------------------------------------------------
            delta_fs[num_temps] =
                fs_tmp_gain *
                (pcSched->pcFbkPassD[ps]->pcStdD[1]->sigmat_obs -
                pcSched->pcFbkPassD[ps]->pcStdD[1]->sigmat_rep );
            loc_temps[num_temps] =
                pcSched->pcFbkPassD[ps]->pcStdD[1]->pcEnPceD->temp_avg;
        }
    }

    //------------------------------------------------------
    // Determine delta hardness for flow stress temperatures
    // which bound strip temperatures.
    //------------------------------------------------------
    if( num_temps > 0 && fs_tmp_gain > 0.0 )
    {
        DMSG(diagLvl) << "Calling Error_Adapt()" << END_OF_MESSAGE;

        //------------------------------
        // prepare inputs to Error_Adapt
        //------------------------------
        for( i=0; i<=num_temps; i++ )
        {
            ea_error_old[i] = delta_fs[i];
            ea_x_old[i] = loc_temps[i];
        }

        for(i=0; i<num_fs_pts; i++ )
        {
            ea_x_new[i] = pcSched->pcRAGP->fbk_state.fs_tmp[i];
        }

        ea_x_old_last = num_temps;
        ea_x_new_last = num_fs_pts - 1;

        Error_Adapt (
            ea_error_new,    // OUT  errors translated to fs_tmp temperature points
            ea_index_start,  // OUT  starting index for update
            ea_index_end,    // OUT  ending index for update
            ea_error_old,    // IN   errors at actual temperatures
            ea_x_old,        // IN   actual strip temperatures
            ea_x_new,        // IN   base temperature (fs_tmp) table
            ea_x_old_last,   // IN   temperature index
            ea_x_new_last ); // IN   index of last element in base fs_tmp array

        //------------------------------------------------------
        // Clamp delta hardness.  Update flowstress multiplier curve
        //------------------------------------------------------
        for( j=ea_index_start; j<=ea_index_end; j++ )
        {
            ea_error_new[j] = cMathUty::Clamp(
                                            ea_error_new[j],
                                            -this->fs_tmp_mult_hi_lim,
                                            this->fs_tmp_mult_hi_lim );

            pcSched->pcRAGP->fbk_state.fs_tmp_mult[j] *=
                ( ea_error_new[j] + 1.0F );

            //-----------------------------------------------------
            // Clamp calculated flowstress multiplier value
            // within configuration hi/low limits
            //-----------------------------------------------------
            pcSched->pcRAGP->fbk_state.fs_tmp_mult[j] =
                cMathUty::Clamp ( pcSched->pcRAGP->fbk_state.fs_tmp_mult[j],
                                  this->fs_tmp_mult_lo_lim,
                                  this->fs_tmp_mult_hi_lim );

        }

        //------------------------------------------------------
        // Force the slope between the 1st and 2nd points in the
        // fs_vs_temp array to be zero or negative
        //------------------------------------------------------
        if( pcSched->pcRAGP->fbk_state.fs_tmp_mult[0] <
            pcSched->pcRAGP->fbk_state.fs_tmp_mult[1] )
        {
            pcSched->pcRAGP->fbk_state.fs_tmp_mult[0] =
                pcSched->pcRAGP->fbk_state.fs_tmp_mult[1];
        }

        //------------------------------------------------------------
        // Force the fs_vs_temp array to be monatonically decreasing.
        // If a slope between two adjacent points is positive then
        // letting the 1st pt be point i and the 2nd i+1.  Using the
        // difference from points i and i+1 raise point i by two thirds
        // of the difference.  Points i-1 and i+1 are lowered by one
        // third the difference.  Start at the 2nd point in the array
        // and stop at the next to last point.
        //-------------------------------------------------------------
        for( i=1; i<num_fs_pts-1; i++ )
        {
            if( pcSched->pcRAGP->fbk_state.fs_tmp_mult[i] <
                pcSched->pcRAGP->fbk_state.fs_tmp_mult[i+1] )
            {
                float error = pcSched->pcRAGP->fbk_state.fs_tmp_mult[i+1] -
                    pcSched->pcRAGP->fbk_state.fs_tmp_mult[i];

                pcSched->pcRAGP->fbk_state.fs_tmp_mult[i-1]  =
                    pcSched->pcRAGP->fbk_state.fs_tmp_mult[i-1] - error / 3.0F;

                pcSched->pcRAGP->fbk_state.fs_tmp_mult[i]    =
                    pcSched->pcRAGP->fbk_state.fs_tmp_mult[i] + error * 2.0F / 3.0F;

                pcSched->pcRAGP->fbk_state.fs_tmp_mult[i+1]  =
                    pcSched->pcRAGP->fbk_state.fs_tmp_mult[i+1] - error / 3.0F;
            }
        }

        for( i=num_fs_pts-1; i>0; i-- )
        {
            if( pcSched->pcRAGP->fbk_state.fs_tmp_mult[i-1] <
                pcSched->pcRAGP->fbk_state.fs_tmp_mult[i] )
            {
                pcSched->pcRAGP->fbk_state.fs_tmp_mult[i-1] =
                    pcSched->pcRAGP->fbk_state.fs_tmp_mult[i];
            }
        }

        //----------------------------------------------
        // Normalize curve to selected temperature point
        //----------------------------------------------
        if ( 0 )
        {
            float anchor_point_value = pcSched->pcRAGP->fbk_state.fs_tmp_mult[fs_curve_anchor];
            for( i=ea_index_start; i<=ea_index_end; i++ )
            {
                pcSched->pcRAGP->fbk_state.fs_tmp_mult[i] =
                pcSched->pcRAGP->fbk_state.fs_tmp_mult[i] / anchor_point_value;
            }
        }
    }
    else
    {
        DMSG(-diagLvl) << "num_temps = " << num_temps
                       <<" fs_tmp_gain = " << fs_tmp_gain
                       << END_OF_MESSAGE;
    }
}   //  End     Update_FSCurve()

//-----------------------------------------------------------------
// Update_FSMult() ABSTRACT
//
// Update the flowstress multipliers to minimise the mean squared
// flowstress error.
//-----------------------------------------------------------------
void cAdaptation::Update_FSMult(
                        cSched  *pcSched,
                        cPassD  **pcPassD)
{
    // Future, adaptation for analytic flowstress is currently only by force multipliers
    ;
}   //  End     Update_FSMult()


//-------------------------------------------------------------------------
// Update_Force_Power_Multipliers() ABSTRACT
//
// Calculates Force/Power Multiplier for model table updates.
//
// Observed force/power ratios are calculated from measured
// force/power and repredicted power. From observed force/power ratio,
// model table force ratios are calculated.
//-------------------------------------------------------------------------
void cAdaptation::Update_Force_Power_Multipliers( cSched* pcSched)
{
    float   force_err (0.0);
    int     updates;
    float   frc_mult_gain;
    float   pwr_mult_gain;
    int     ps;
    int     iseg;

    // check pointers
    if ( NULL == pcSched->pcRAPP )
    {
        DMSG(diagLvl) << " Bad Model table pointer. Force & Power "
                      << " adaptation disabled id "
                      << pcSched->objName()
                      << END_OF_MESSAGE;
        return;
    }
    updates = pcSched->pcRAPP->fbk_state.updates;

    //---------------------------------------------------------
    // Set adaptation gain factors.
    //---------------------------------------------------------
    if ( updates < this->gain_swt_lim )
    {
        frc_mult_gain = this->frc_mult_hi_gain;
        pwr_mult_gain = this->pwr_mult_hi_gain;
    }
    else
    {
        frc_mult_gain = this->frc_mult_lo_gain;
        pwr_mult_gain = this->pwr_mult_lo_gain;
    }

    //---------------------------------------------------------
    // Loop thru all passes
    //---------------------------------------------------------
    for ( ps=1; ps<=pcSched->pcFeedbackD->fbkpas; ps++ )
    {
        int     pass_index = pcSched->pcFbkPassD[ps]->ps_idx;

        iseg = pcObjChain->Body_Index();

        if ( pcSched->pcFbkPassD[ps]->StdD_Present() )
        {
            cStdD    *pcStdD = pcSched->pcFbkPassD[ps]->pcStdD[iseg];

            //-----------------------------------------------------
            // Reset Accumulators if number of updates are zero or
            // number of samples exceeds the configuration limit.
            //-----------------------------------------------------
            if ( 0 == pcSched->pcRAPP->fbk_state.updates ||
                 pcSched->pcRAPP->fbk_state.frc_err_count[pass_index] >
                 this->max_frc_samples )
            {
                pcSched->pcRAPP->fbk_state.frc_err_count[pass_index] = 0;
                pcSched->pcRAPP->fbk_state.frc_err_sum[pass_index]   = 0.0F;
                pcSched->pcRAPP->fbk_state.frc_err_ss[pass_index]    = 0.0F;
                pcSched->pcRAPP->fbk_state.frc_err_sd[pass_index]    = 0.0F;
                pcSched->pcRAPP->fbk_state.frc_err_mean[pass_index]  = 0.0F;
            }

            //---------------------------------------------------------
            // calculate observed force ratio
            // obs_force_ratio = measured force  / repredicted force
            //---------------------------------------------------------
            if ( !pcStdD->dummied && pcStdD->force_strip > 0.0F )
            {
                //-----------------------------------------------------
                // Calculate observed force multiplier value
                //-----------------------------------------------------
                pcStdD->force_ratio = pcStdD->force_meas /
                                      pcStdD->force_strip;

                //-----------------------------------------------------
                // Calculate new force mutiplier
                //-----------------------------------------------------
                pcSched->pcRAPP->fbk_state.force_mult[pass_index] +=
                    frc_mult_gain *
                        ( pcStdD->force_ratio - 1.0F ) *
                          pcSched->pcRAPP->fbk_state.force_mult[pass_index];

                //-----------------------------------------------------
                // Clamp calculated force multiplier value
                // within configuration hi/low limits
                //-----------------------------------------------------
                pcSched->pcRAPP->fbk_state.force_mult[pass_index] =
                    cMathUty::Clamp ( pcSched->pcRAPP->fbk_state.force_mult[pass_index],
                                      this->frc_mult_lo_lim,
                                      this->frc_mult_hi_lim );

                //-----------------------------------------------------
                // calculate force error for current product
                //-----------------------------------------------------
                force_err = pcStdD->force_meas -
                            pcStdD->force_strip;

                //-----------------------------------------------------
                // increment force err counter
                //-----------------------------------------------------
                ++pcSched->pcRAPP->fbk_state.frc_err_count[pass_index];

                //-----------------------------------------------------
                // increment sum of force error term
                //-----------------------------------------------------
                pcSched->pcRAPP->fbk_state.frc_err_sum[pass_index] += force_err;

                //-----------------------------------------------------
                // increament sum of squares term
                //-----------------------------------------------------
                pcSched->pcRAPP->fbk_state.frc_err_ss[pass_index] +=
                            force_err * force_err;

                // Clamped frc_err_ss to database limits
                pcSched->pcRAPP->fbk_state.frc_err_ss[pass_index]  =
				    cMathUty::Clamp ( pcSched->pcRAPP->fbk_state.frc_err_ss[pass_index],
                                                0.0F,
                                        999999998.0F );

                if ( pcSched->pcRAPP->fbk_state.frc_err_count[pass_index] > 1 )
                {
                    //-----------------------------------------------------
                    // Calculate force error mean for model table update.
                    //-----------------------------------------------------
                    pcSched->pcRAPP->fbk_state.frc_err_mean[pass_index] =
                        pcSched->pcRAPP->fbk_state.frc_err_sum[pass_index] /
                        (float) pcSched->pcRAPP->fbk_state.frc_err_count[pass_index];

                    //-----------------------------------------------------
                    // calculate new Standard deviation term
                    //-----------------------------------------------------
                    pcSched->pcRAPP->fbk_state.frc_err_sd[pass_index] =
                        ( pcSched->pcRAPP->fbk_state.frc_err_ss[pass_index]  -
                          ( (float) pow ( pcSched->pcRAPP->fbk_state.frc_err_sum[pass_index], 2 ) /
                            pcSched->pcRAPP->fbk_state.frc_err_count[pass_index] ) ) /
                            (pcSched->pcRAPP->fbk_state.frc_err_count[pass_index]-1);

                    pcSched->pcRAPP->fbk_state.frc_err_sd[pass_index] =
                                    sqrt(pcSched->pcRAPP->fbk_state.frc_err_sd[pass_index]);
                }

            } // if not dummied
            else
            {
                pcStdD->force_mult = 0.0;
            }//  pass dummied

            // if stand is not dummied and
            // calculated power is valid
            if ( ( !pcStdD->dummied ) &&
                 ( pcStdD->power_shaft > 0.0 ) )
            {
                //-----------------------------------------------------
                // Calculate observed power multiplier value from power
                // meas if available and power shaft calculated by base
                // stand method.
                //-----------------------------------------------------
                pcStdD->power_ratio = pcStdD->power_meas /
                                      pcStdD->power_shaft;

                //-----------------------------------------------------
                // Calculate new multiplier
                //-----------------------------------------------------
                pcSched->pcRAPP->fbk_state.power_mult[pass_index] +=
                    pwr_mult_gain *
                        ( pcStdD->power_ratio - 1.0F ) *
                          pcSched->pcRAPP->fbk_state.power_mult[pass_index];

                //-----------------------------------------------------
                // Clamp calculated number between configuration
                // high and low power mult limits
                //-----------------------------------------------------
                pcSched->pcRAPP->fbk_state.power_mult[pass_index] =
                    cMathUty::Clamp ( pcSched->pcRAPP->fbk_state.power_mult[pass_index],
                                      this->pwr_mult_lo_lim,
                                      this->pwr_mult_hi_lim );

            } // if not dummied
            else
            {
                pcStdD->power_mult = 0.0;
            } // stand/pass dummied
        } // StdD_Present()
        // Check for stand alone edger (VSB)
        else if ( pcSched->pcFbkPassD[ps]->EnEdgD_Present() &&
                  !pcSched->pcFbkPassD[ps]->StdD_Present() )
        {
            cEdgD    *pcEdgD = pcSched->pcFbkPassD[ps]->pcEnEdgD[iseg];

            // if Edger is not dummied and
            // calculated power is valid
            if ( ( !pcEdgD->dummied ) &&
                 ( pcEdgD->power_shaft > 0.0 ) )
            {
                //-----------------------------------------------------
                // Calculate observed power multiplier value from power
                // meas if available and power shaft calculated by base
                // stand method.
                //-----------------------------------------------------
                pcEdgD->power_ratio = pcEdgD->power_meas /
                                      pcEdgD->power_shaft;

                //-----------------------------------------------------
                // Calculate new multiplier
                //-----------------------------------------------------
                pcSched->pcRAPP->fbk_state.power_mult[pass_index] +=
                    pwr_mult_gain *
                        ( pcEdgD->power_ratio - 1.0F ) *
                          pcSched->pcRAPP->fbk_state.power_mult[pass_index];

                //-----------------------------------------------------
                // Clamp calculated number between configuration
                // high and low power mult limits
                //-----------------------------------------------------
                pcSched->pcRAPP->fbk_state.power_mult[pass_index] =
                    cMathUty::Clamp ( pcSched->pcRAPP->fbk_state.power_mult[pass_index],
                                      this->pwr_mult_lo_lim,
                                      this->pwr_mult_hi_lim );

            } // if not dummied
            else
            {
                pcEdgD->power_mult = 0.0;
            } // Edger/pass dummied
        }


    } // for all mill passes

    return;

} // end cAdaptation::Update_Force_Power_Multipliers


//-------------------------------------------------------------------------
// Update_Flowstress_Multipliers() ABSTRACT
//
// Calculates Flowstress Multiplier for model table updates.
//
// Observed flowstress multipliers are calculated from observed and
// predicted flowstress values.
//-------------------------------------------------------------------------
void cAdaptation::Update_Flowstress_Multipliers( cSched* pcSched)
{
    float   fs_err = 0.0;
    int     updates;
    float   fs_mult_gain;
    int     ps;
    int     iseg;
    int     num_obs_samples;

    // check pointers
    if ( NULL == pcSched->pcRAGP )
    {
        DMSG(diagLvl) << " Bad Model table pointer. Flowstress "
                      << " adaptation disabled id "
                      << pcSched->objName()
                      << END_OF_MESSAGE;
        return;
    }
    updates = pcSched->pcRAGP->fbk_state.updates;

    //---------------------------------------------------------
    // Set adaptation gain factors.
    //---------------------------------------------------------
    if ( updates < this->gain_swt_lim )
    {
        fs_mult_gain = this->fs_mult_hi_gain;
    }
    else
    {
        fs_mult_gain = this->fs_mult_lo_gain;
    }

    //---------------------------------------------------------
    // Loop thru all passes
    //---------------------------------------------------------
    pcSched->pcFeedbackD->obs_fs_mult = 0.0;
    num_obs_samples = 0;
    for ( ps=1; ps<=pcSched->pcFeedbackD->fbkpas; ps++ )
    {
        int     pass_index = pcSched->pcFbkPassD[ps]->ps_idx;

        iseg = pcObjChain->Body_Index();

        if ( pcSched->pcFbkPassD[ps]->StdD_Present() )
        {
            cStdD    *pcStdD = pcSched->pcFbkPassD[ps]->pcStdD[iseg];

            //-----------------------------------------------------
            // Reset Accumulators if number of updates are zero.
            //-----------------------------------------------------
            if ( 0 == pcSched->pcRAGP->fbk_state.updates )
            {
                pcSched->pcRAGP->fbk_state.fs_mult_count[pass_index] = 0;
                pcSched->pcRAGP->fbk_state.fs_mult_sum[pass_index]   = 0.0F;
                pcSched->pcRAGP->fbk_state.fs_mult_ss[pass_index]    = 0.0F;
                pcSched->pcRAGP->fbk_state.fs_mult_sd[pass_index]    = 0.0F;
                pcSched->pcRAGP->fbk_state.fs_mult_mean[pass_index]  = 0.0F;
            }

            //---------------------------------------------------------
            // calculate flowstress multiplier
            // fs_mult = observed pu flowstress  / repredicted pu flowstress
            //---------------------------------------------------------
            if ( !pcStdD->dummied && pcStdD->sigmat_obs_vld )
            {
                //-----------------------------------------------------
                // Calculate observed flowstress multiplier value
                //-----------------------------------------------------
                pcStdD->fs_mult = pcStdD->sigmat_obs /
                                      pcStdD->sigmat_rep;

                //-----------------------------------------------------
                // Calculate new flowstress mutiplier
                //-----------------------------------------------------
                pcSched->pcRAGP->fbk_state.fs_mult[pass_index] +=
                    fs_mult_gain *
                        ( pcStdD->fs_mult - 1.0F ) *
                          pcSched->pcRAGP->fbk_state.fs_mult[pass_index];

                //-----------------------------------------------------
                // Clamp calculated force multiplier value
                // within configuration hi/low limits
                //-----------------------------------------------------
                pcSched->pcRAGP->fbk_state.fs_mult[pass_index] =
                    cMathUty::Clamp ( pcSched->pcRAGP->fbk_state.fs_mult[pass_index],
                                      this->fs_mult_lo_lim,
                                      this->fs_mult_hi_lim );

                //-----------------------------------------------------
                // calculate flowstress error for current product
                //-----------------------------------------------------
                fs_err = pcStdD->sigmat_obs - pcStdD->sigmat_rep;

                //-----------------------------------------------------
                // Reset Accumulators if number of samples exceeds the
                // configuration limit.
                //-----------------------------------------------------
                if ( pcSched->pcRAGP->fbk_state.fs_mult_count[pass_index] >
                     this->max_fs_samples )
                {
                    pcSched->pcRAGP->fbk_state.fs_mult_count[pass_index] = 0;
                    pcSched->pcRAGP->fbk_state.fs_mult_sum[pass_index]   = 0.0F;
                    pcSched->pcRAGP->fbk_state.fs_mult_ss[pass_index]    = 0.0F;
                }

                //-----------------------------------------------------
                // increment flowstress err counter
                //-----------------------------------------------------
                ++pcSched->pcRAGP->fbk_state.fs_mult_count[pass_index];

                //-----------------------------------------------------
                // increment sum of flowstress error term
                //-----------------------------------------------------
                pcSched->pcRAGP->fbk_state.fs_mult_sum[pass_index] += fs_err;

                //-----------------------------------------------------
                // increament sum of squares term
                //-----------------------------------------------------
                pcSched->pcRAGP->fbk_state.fs_mult_ss[pass_index] +=
                            fs_err * fs_err;

                if ( pcSched->pcRAGP->fbk_state.fs_mult_count[pass_index] > 1 )
                {
                    //-----------------------------------------------------
                    // Calculate flowstress multiplier mean for model table update.
                    //-----------------------------------------------------
                    pcSched->pcRAGP->fbk_state.fs_mult_mean[pass_index] =
                        pcSched->pcRAGP->fbk_state.fs_mult_sum[pass_index] /
                        (float) pcSched->pcRAGP->fbk_state.fs_mult_count[pass_index];

                    //-----------------------------------------------------
                    // calculate new Standard deviation term
                    //-----------------------------------------------------
                    pcSched->pcRAGP->fbk_state.fs_mult_sd[pass_index] =
                        ( pcSched->pcRAGP->fbk_state.fs_mult_ss[pass_index]  -
                          ( (float) pow ( pcSched->pcRAGP->fbk_state.fs_mult_sum[pass_index], 2 ) /
                            pcSched->pcRAGP->fbk_state.fs_mult_count[pass_index] ) ) /
                            (pcSched->pcRAGP->fbk_state.fs_mult_count[pass_index]-1);

                    pcSched->pcRAGP->fbk_state.fs_mult_sd[pass_index] =
                                    sqrt(pcSched->pcRAGP->fbk_state.fs_mult_sd[pass_index]);
                }

                pcSched->pcFeedbackD->obs_fs_mult += pcStdD->fs_mult;
                num_obs_samples++;
            } // if not dummied
            else
            {
                pcStdD->fs_mult = 0.0;
            }//  pass dummied

        } // StdD_Present()

    } // for all mill passes

    //-----------------------------------------------------------------
    // Update the observed flowstress multiplier.
    //-----------------------------------------------------------------
    if ( num_obs_samples > 0 )
    {
        pcSched->pcFeedbackD->obs_fs_mult /= num_obs_samples;
        pcSched->pcFeedbackD->obs_fs_vld   = true;
        pcSched->pcRAGP->fbk_state.fs_mlt +=
                    fs_mult_gain *
                        ( pcSched->pcFeedbackD->obs_fs_mult - 1.0F ) *
                          pcSched->pcRAGP->fbk_state.fs_mlt;

        //-----------------------------------------------------
        // Clamp average flow stress multiplier value
        // within configuration hi/low limits
        //-----------------------------------------------------
        pcSched->pcRAGP->fbk_state.fs_mlt =
            cMathUty::Clamp ( pcSched->pcRAGP->fbk_state.fs_mlt,
                                this->fs_mult_lo_lim,
                                this->fs_mult_hi_lim );

    }

    return;

} // end cAdaptation::Update_Flowstress_Multipliers


//-------------------------------------------------------------------------
// Update_PLimit_Multiplier() ABSTRACT
//
// Calculates Very Head End Power Limit Multiplier for model table updates.
//
//-------------------------------------------------------------------------
void cAdaptation::Update_PLimit_Multiplier(
                        cSched  *pcSched,
                        int     pin)
{
    float   pwr_ratio;
    int     iseg_head = pcObjChain->Head_Index();
    int     iseg_body = pcObjChain->Body_Index();
    int     pass_index = pcSched->pcFbkPassD[pin]->ps_idx;

    // Initialize feedback quantities.
    pcSched->pcFeedbackD->pwr_lim_mult[pass_index] =
            pcSched->pcRAMP->fbk_state.pwr_lim_mult[pass_index];

    if ( pcSched->pcFbkPassD[pin]->StdD_Present() )
    {
        // Horizontal stand
        if ( pcSched->pcFbkPassD[pin]->pcStdD[iseg_head]->power_vhd_vld &&
             pcSched->pcFbkPassD[pin]->pcStdD[iseg_body]->power_meas_vld &&
             pcSched->pcFbkPassD[pin]->pcStdD[iseg_head]->power_vhd != 0.0 )
        {
            pwr_ratio = pcSched->pcFbkPassD[pin]->pcStdD[iseg_body]->power_meas /
                        pcSched->pcFbkPassD[pin]->pcStdD[iseg_head]->power_vhd;
            if ( (pwr_ratio > (1.0F + this->plim_mult_dbnd) ||
                  pwr_ratio < (1.0F - this->plim_mult_dbnd)   ) &&
                 (pwr_ratio < this->plim_ratio_hi_lim ||
                  pwr_ratio > this->plim_ratio_lo_lim)  )
            {
                pcSched->pcFeedbackD->pwr_lim_mult[pass_index] =
                    this->plim_mult_gain *
                    ( pwr_ratio - 1.0F ) *
                    pcSched->pcFeedbackD->pwr_lim_mult[pass_index];

                // Clamp the final result
                pcSched->pcFeedbackD->pwr_lim_mult[pass_index] =
                    cMathUty::Clamp(pcSched->pcFeedbackD->pwr_lim_mult[pass_index],
                                    this->plim_mult_lo_lim,
                                    1.0F);  // Can not be more than one
            }
        }
    }
    else if ( pcSched->pcFbkPassD[pin]->EnEdgD_Present() )
    {
        // Standalone edger (VSB)
        // Do not implement pwr_lim_mult for VSB, only for edgers coupled with stands.
    }

}   //  End     Update_PLimit_Multiplier()
