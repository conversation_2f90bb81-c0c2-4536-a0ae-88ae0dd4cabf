//-----------------------------------------------------------------------------
//
// ABSTRACT:
//      This package contains methods related to AWC setup.
//
//     FUNCTION/PROCEDURE/TASK  DESCRIPTION
//     -----------------------  ------------------------------------------------
//     cAWCSetup                AWCSetup class consrructor.
//     ~cAWCSetup               AWCSetup class destructor.
//     dump                     AWCSetup class dump function.
//
//     Setup                    Main function - calculate AWC setup
//     Taper_Pass               Calculate the taper quantities for slab head
//                               and tail
//
//------------------------------------------------------------------------------
#define AWCSETUP_CXX

//----------------------
// system include files
//----------------------

//---------------------
// mds include files
//---------------------
#include "alarm.hxx"
#include "mathuty.hxx"
#include "objchain.hxx"
#include "objhash.hxx"
#include "stdrollpr.hxx"
#include "utility.hxx"
#include "width.hxx"

//---------------------
// shared include files
//---------------------
#include "pce.hxx"

//---------------------
// rsu include files
//---------------------
#include "awcsetup.hxx"
#include "edg.hxx"
#include "esu.hxx"
#include "pass.hxx"
#include "sched.hxx"
#include "setup.hxx"
#include "std.hxx"

//---------------------
// records include files
//---------------------
#include "esys.hxx"
#include "pdi.hxx"
#include "ramp.hxx"
#include "rapp.hxx"  //add by xubin 2012.10.9
#include "rsys.hxx"
#include "rsu_features.hxx"



#ifdef WIN32
	#ifdef _DEBUG
	#define new DEBUG_NEW
	#endif
    #pragma warning(disable: 4244) // double to float conversion (NT thinks constants are doubles)
#endif

// Diagnostic level specific to this file
static cAlarm::DiagnosticCodeEnum diagLvl(cAlarm::AWCSetup);


// Data schema for the cAWCSetup class.
static cSchema::schema_type cAWCSetup_schema[]=
{
    //Next  Enum  Schema details                            Fmt  Units        Comment
    //====  ====  ========================================  ==== ===========  ==================================================
    { NULL, NULL, SCHEMA_T(cAWCSetup,float,gapkon),         "",  "",          "gap to stroke conversion modifier" },
    { NULL, NULL, SCHEMA_T(cAWCSetup,float,strkhd),         "",  "",          "head stroke modifier" },
    { NULL, NULL, SCHEMA_T(cAWCSetup,float,strktl),         "",  "",          "tail stroke modifier" },
    { NULL, NULL, SCHEMA_T2(cAWCSetup,float,wid_str_mod,2,3), "","",          "width and strain matrix modifier" },
    { NULL, NULL, SCHEMA_T(cAWCSetup,float,max_tpr_len),    "",  "mm_in",     "maximum taper length limit" },
    { NULL, NULL, SCHEMA_T(cAWCSetup,float,DwidDfrc_hi_lim),"",  "",          "delta width to delta force high limit" },
    { NULL, NULL, SCHEMA_T(cAWCSetup,float,DwidDtmp_hi_lim),"",  "",          "delta width to delta temperature high limit" },
    { NULL, NULL, SCHEMA_T(cAWCSetup,bool, len_mod_enab),   "",  "",          "if true, enable taper length modification" },
    { NULL, NULL, SCHEMA_T(cAWCSetup,bool, wid_vern_enab),  "",  "",          "if true, enable RM width vernier usage" },
    { NULL, NULL, SCHEMA_T(cAWCSetup,float,pr_pdi_rwid),	"",  "mm_in",     "Previous product pdi rwidth" },		//add by billy c 20150827

    { 0 }   // terminate list
};

// Link all the schema's together
cSchema::schema_name_type cAWCSetup::sSchema[]=
{
    {
        "cAWCSetup",                           // name
        sizeof(cAWCSetup),                     // size
        cAWCSetup_schema,                      // schema
        false,                            // packed
        false,                            // allow ptr
        false,                            // Read only
        "AWCSetup class configuration",        // comment
        0                                 // offset to config data
    },
    { 0 } // terminate list
};

// Use this constructor if no hash table support required
cAWCSetup::cAWCSetup()
{
    Set_Class_Name("cAWCSetup");
    Set_Schema("cAWCSetup",sSchema);

    // Zero out member data
    Zero_Data(this, sizeof(cAWCSetup), Get_Schema("cAWCSetup"));

}

//-------------------------------------------------------------------------
// cAWCSetup CONSTRUCTOR ABSTRACT:
//   Schedule Definition destructor
//-------------------------------------------------------------------------
cAWCSetup::cAWCSetup( const MString       &objName,
                      const objTypEnum   objType,
                      const objPosEnum   position,
                      void               *pHash )
    : cBase( objName, objType, position,  pHash, 0, 0 )
{
    Set_Class_Name("cAWCSetup");
    Set_Schema("cAWCSetup",sSchema);

    // Zero out member data
    Zero_Data(this, sizeof(cAWCSetup), Get_Schema("cAWCSetup"));

}


//---------------------------------------------------------------------
// Virtual function to allow the user to carry out post processing after
// a config file has been read.  In this case set the global pcAWCSetup
// pointer to point to the object.  NOTE this means that pcAWCSetup points
// to the last AWCSetup object configured.
//---------------------------------------------------------------------
bool    cAWCSetup::Post_Config(
                        char *name,         // name of schema
                        void *psStruct)     // address of binary structure
                                            // holding configured data
{
    pcAWCSetup = (cAWCSetup *)(this);
    return true;
}

//-----------------------------------------------------------------------------
// cAWCSetup::Taper_Pass - calculates AWC taper related quantities by pass.
//
// Argument list:
//    fstpas      -     IN  first pass
//    lstpas      -     IN  last pass
//
// Return:
//    bool       - status
//-----------------------------------------------------------------------------
bool cAWCSetup::Taper_Pass(
                    int     fstpas,     // first pass
                    int     lstpas      // last pass
                          )
{
    //---------------------
    // Local variables
    //---------------------
    int     size;
    int     ps;
    float   a;
    float   b;
    float   c;
    float   h           = 0.0;      // entry thickness
    float   w           = 0.0;      // entry width
    float   dh          = 0.0;      // horizontal draft
    float   dw          = 0.0;      // edger draft
    float   dft_reqd    = 0.0;      // draft_required
    float   edgtot      = 0.0;      // total edging
    float   hd_shortage = 0.0;      // head draft shortage
    float   hslbw       = 0.0;      // hot slab width
    float   htwtgt      = 0.0;      // holding table width target
    float   tprfmx      = 0.0;      // maximum cylinder stroke
    float   tprfmn      = 0.0;      // max negative taper
    float   tprgain     = 0.0;      // taper gain
    float   tproff      = 0.0;      // taper head offset
    float   tprstr      = 0.0;      // taper control stroke % draft
    float   tprsav_hd   = 0.0;      // taper stroke saved head
    float   tprsav_tl   = 0.0;      // taper stroke saved head
    float   mintpr      = 0.0;      // min taper amount
    float   maxtpr      = 0.0;      // max taper amount
    float   mintpr_len  = 0.0;      // min taper length allowed
    float   maxtpr_len  = 0.0;      // max taper length allowed

    int     i = 0;

    //---------------------
    // Redefine variables
    //---------------------
    hslbw       = psentw[fstpas];
    htwtgt      = psextw[lstpas];

    //-----------------------------------------------------------
    // initialize head and tail width for pass initial first pass
    //-----------------------------------------------------------
    if ( 1 == fstpas )
    {
        awid_hd[fstpas] = hslbw;
        awid_tl[fstpas] = hslbw;

        // Initialize the first hd & tl taper length
        fst_tprlhd = 0.0F;
        fst_tprltl = 0.0F;
    }

    //---------------------------------
    // For each pass scheduled by setup
    //---------------------------------
    for ( ps = fstpas; ps <= lstpas; ps++ )
    {
        //-----------------------------
        // Initialize taper references
        //-----------------------------
        depdf[ps]  = 0.0;
        tprfhd[ps] = 0.0;
        tprftl[ps] = 0.0;
        tprlhd[ps] = 0.0;
        tprltl[ps] = 0.0;

        //-----------------------------
        // Shorten names for formulas
        //-----------------------------
        dh      = psdrft[ps];       // Stand draft
        dw      = pegdft[ps];       // edger draft
        h       = psthick[ps];      // entry piece thickness
        w       = psentw[ps];       // entry piece width
        tprfmx  = tpr_max[ps];     // max cylinder stroke per pass
        tprfmn  = tpr_min[ps];      // max negative taper

        //--------------------
        // Edging parameters
        //--------------------
        // b = 0.098 * h**0.56 * dw**0.7;       dog bone height
        b = 0.098F * pow( h, 0.56F ) * pow( dw, 0.7F );
        // a = (-0.00018 * w *b+8.0) * (dh**1.11*h**(-0.63));   head spread
        a = ( -0.00018F * awid_hd[ps] * b + 8.0F )
          * ( pow( dh, 1.11F ) * pow( h, -0.63F ) );
        // c = (-0.000034*w*b+1.6) * (dh**0.92*h**(-0.17));     tail spread
        c = ( -0.000034F * awid_tl[ps] * b + 1.6F )
          * ( pow( dh, 0.92F ) * pow( h, -0.17F ) );

        //-------------------------------------------------
        // (head width shortage/edger draft) after edging
        //-------------------------------------------------
        hd_shortage = ( 0.00012F * awid_hd[ps] - 0.071F ) * pow( h, 0.31F );

        //----------------------
        // Head draft required
        //----------------------
        dft_reqd = dw / ( 1.0F + hd_shortage );

        //--------------------
        // Taper head offest
        //--------------------
        tproff = dw - dft_reqd;

        //--------------------------------------------------------
        // Find taper gain as a function of width and total edging
        //--------------------------------------------------------
        edgtot  = hslbw - htwtgt;
        tprgain = 0.75F + 0.001F * edgtot * ( hslbw / mill().data().widMax );
        // find No of elements in second row of wid_str_mod array
        size = sizeof(wid_str_mod[1]) / sizeof( float );
        tprstr  = cMathUty::rlnint(
                                &psentw[ps],
                                wid_str_mod[0],
                                wid_str_mod[1],
                                &size );

        tprgain = tprgain * tprstr / wid_str_mod[1][1];

        //-----------------------
        // Calculate head taper
        //-----------------------
        tprsav_hd = gapkon * tprgain
                  * ( tproff + strkhd * ( pspred[ps] + pegrcv[ps] - a ) );

        //-----------------------
        // Calculate tail taper
        //-----------------------
        tprsav_tl = gapkon * strktl  * tprgain * ( pspred[ps] + pegrcv[ps] - c );

        //--------------------------------------------------
        // If edger cylinder available and AUTO,
        // calcultae head and tail taper offset and length
        //--------------------------------------------------
        if ( awc_ok[ps] )
        {
            //------------------------------------
            // calculate min and max taper limit
            //------------------------------------
            mintpr = cMathUty::Max ( -tprfmx, tprfmn );
            maxtpr = cMathUty::Min ( tprfmx, 0.5F * dw );

            //----------------------------------------
            // Incorporate operator enabled functions.
            //----------------------------------------
            if ( !ps_hd_enab[ps] )
            {
                tprfhd[ps] = 0.0;
                tprlhd[ps] = 0.0;
            }
            else
            {
                //---------------------------------------
                // Head taper including operator offset
                //---------------------------------------
                if ( tprsav_hd >= 0.0 )
                {
                    //--------------------------------------------------------
                    // Head end is narrow, open the edger ( positive taper )
                    //--------------------------------------------------------
                    tprfhd[ps] = tprsav_hd * nhto_mult[ps];
                }
                else
                {
                    //-------------------------------------------------------
                    // Head end is wide, close the edger ( negative taper )
                    //-------------------------------------------------------
                    tprfhd[ps] = tprsav_hd * whto_mult[ps];
                }

                //---------------------------------------------
                // add operator taper adjustment in percentage
                //---------------------------------------------
                tprfhd[ps] = tprfhd[ps] * ( 1.0F + opr_hd_tpr / 100.0F );

                //------------------
                // Limit reference
                //------------------
                tprfhd[ps] = cMathUty::Clamp( tprfhd[ps],
                                              mintpr,
                                              maxtpr);

                //--------------------------------------------
                // if not within the dead_band, reset to zero
                //--------------------------------------------
                if ( ( tprfhd[ps] > -dead_band[ps] ) &&
                     ( tprfhd[ps] < dead_band[ps] ) )
                {
                    tprfhd[ps] = 0.0F;
                }

                //-----------------------------
                // calculate head taper length
                //-----------------------------
                if ( 0.0 == tprfhd[ps] )
                {
                    tprlhd[ps] = 0.0F;
                }
                else
                {
                    //----------------------------------------------
                    // Head taper length including operator offset
                    //----------------------------------------------
                    tprlhd[ps] = ( 0.022F * w + 33.0) * pow( h, 0.3F )
                                                      * pow( dw, 0.26F );
                    tprlhd[ps] = tprlhd[ps] * htl_mult[ps];
                    tprlhd[ps] = tprlhd[ps] * ( 1.0F + opr_hd_len / 100.0F );

                    // Calculate taper length based on the first
                    // taper length and thickness ratio elongation
                    if ( len_mod_enab )
                    {
                        if ( fst_tprlhd <= 0.0F )
                        {
                            for (i=1; i<=lstpas; i++)
                            {
                                if (tprlhd[i] > 0.0F)
                                {
                                    fst_tprlhd = tprlhd[i];
                                    fst_ethick = psthick[i];
                                    break;
                                }
                            }
                        }
                        if ( fst_tprlhd > 0.0F && fst_ethick > 0.0F && psthick[ps] > 0.0F )
                        {
                            tprlhd[ps] = fst_tprlhd * fst_ethick / psthick[ps];
                        }
                    }

                    //------------------------------------------
                    // calculate min length based on the speed
                    // of cylinder and amount of taper
                    //------------------------------------------
                    mintpr_len = tprl_rate[ps] * fabs( tprfhd[ps] );

                    // Calculate maximum taper length
                    maxtpr_len = cMathUty::Min( this->max_tpr_len,
                                                 psentl[ps] / 4.0F );
                    //------------------
                    // Limit reference
                    //------------------
                    tprlhd[ps] = cMathUty::Clamp( tprlhd[ps],
                                                  mintpr_len,
                                                  maxtpr_len );
                }   // end if ( 0.0 == tprfhd[ps] )

            }   // end if ( !ps_hd_enab[ps] )


            //----------------------------------------
            // Incorporate operator enabled functions.
            //----------------------------------------
            if ( !ps_tl_enab[ps] )
            {
                tprftl[ps] = 0.0;
                tprltl[ps] = 0.0;
            }
            else
            {
                //---------------------------------------
                // Tail taper including operator offset
                //---------------------------------------
                if ( tprsav_tl >= 0.0 )
                {
                    //--------------------------------------------------------
                    // Tail end is narrow, open the edger ( positive taper )
                    //--------------------------------------------------------
                    tprftl[ps] = tprsav_tl * ntto_mult[ps];
                }
                else
                {
                    //-------------------------------------------------------
                    // Tail end is wide, close the edger ( negative taper )
                    //-------------------------------------------------------
                    tprftl[ps] = tprsav_tl * wtto_mult[ps];
                }

                //---------------------------------------------
                // add operator taper adjustment in percentage
                //---------------------------------------------
                tprftl[ps] = tprftl[ps] * ( 1.0F + opr_tl_tpr / 100.0F );

                //------------------
                // Limit reference
                //------------------
                tprftl[ps] = cMathUty::Clamp( tprftl[ps],
                                              mintpr,
                                              maxtpr);

                if ( ( tprftl[ps] > -dead_band[ps] ) &&
                     ( tprftl[ps] < dead_band[ps] ) )
                {
                    tprftl[ps] = 0.0F;
                }

                //-----------------------------
                // calculate tail taper length
                //-----------------------------
                if ( 0.0 == tprftl[ps] )
                {
                    tprltl[ps] = 0.0F;
                }
                else
                {
                    //----------------------------------------------
                    // Tail taper length including operator offset
                    //----------------------------------------------
                    tprltl[ps] = (0.013 * w + 5.3) * pow( h, 0.43F )
                                                   * pow( dw, 0.29F);
                    tprltl[ps] = tprltl[ps] * ttl_mult[ps];
                    tprltl[ps] = tprltl[ps] * ( 1.0F + opr_tl_len / 100.0F );

                    // Calculate taper length based on the first
                    // taper length and thickness ratio elongation
                    if ( len_mod_enab )
                    {
                        if ( fst_tprltl <= 0.0F )
                        {
                            for (i=1; i<=lstpas; i++)
                            {
                                if (tprltl[i] > 0.0F)
                                {
                                    fst_tprltl = tprltl[i];
                                    if ( fst_ethick <= 0.0F )
                                    {
                                        fst_ethick = psthick[i];
                                    }
                                    break;
                                }
                            }
                        }
                        if ( fst_tprltl > 0.0F && fst_ethick > 0.0F && psthick[ps] > 0.0F )
                        {
                            tprltl[ps] = fst_tprltl * fst_ethick / psthick[ps];
                        }
                    }

                    //------------------------------------------
                    // calculate min length based on the speed
                    // of cylinder and amount of taper
                    //------------------------------------------
                    mintpr_len = tprl_rate[ps] * fabs( tprftl[ps] );

                    // Calculate maximum taper length
                    maxtpr_len = cMathUty::Min( this->max_tpr_len,
                                                 psentl[ps] / 4.0F );
                    //------------------
                    // Limit reference
                    //------------------
                    tprltl[ps] = cMathUty::Clamp( tprltl[ps],
                                                  mintpr_len,
                                                  maxtpr_len );
                }   // end if ( 0.0 == tprftl[ps] )

            }   // end if ( !ps_tl_enab[ps] )

        }   //  end if ( awc_ok[ps] )


        //-----------------------------------------
        // Compute actual width into next pass --
        //-----------------------------------------
        if ( ps <= lstpas )
        {
            if ( frwd[ps+1] )
            {   // forward direction for next pass
                awid_hd[ps+1] = psextw[ps] - tprsav_hd + tprfhd[ps];
                awid_tl[ps+1] = psextw[ps] - tprsav_tl + tprftl[ps];
            }
            else
            {   // reverse direction for next pass
                awid_hd[ps+1] = psextw[ps] - tprsav_tl+ tprftl[ps];
                awid_tl[ps+1] = psextw[ps] - tprsav_hd+ tprfhd[ps];
            }
        }

    }   //  ( ps = fstpass; ps <= lstpas; ps++ )

    return true;

}   // cAWCSetup::Taper_Pass

//-----------------------------------------------------------------------------
// cAWCSetup::Setup - Main function - calculate AWC setup
//
// Argument list:
//    pcSched     - OUT/IN  Sched pointer
//
// Return:
//    bool        - status
//-----------------------------------------------------------------------------
bool cAWCSetup::Setup(
                cSched  *pcSched       // sched pointer
                     )
{

// if AWC not supported, return false
#if !INCLUDE_AWC
    return false;
#endif

    int     ps;                         // pass number
    int     iseg        = pcObjChain->Body_Index();
    int     awc_avail   = false;
    int     fstpas      = pcSched->pcSetupD->fstpas;
    int     lstpas      = pcSched->pcSetupD->lstpas;

    //----------------------------------
    //  Populate local forward pass
    //----------------------------------
    for ( ps = 1; ps <= lstpas; ps++ )
    {
        frwd[ps] = pcSched->pcSupPassD[ps]->pcPass->frwd;
    }
    frwd[lstpas+1] = true;

    //----------------------------------------------------
    // Check for AWC pass, if there is none return
    //----------------------------------------------------
    for ( ps=fstpas; ps<=lstpas; ps++ )
    {
        cEdgD   *pcEdgD;
        if ( NULL != (pcEdgD= pcSched->pcSupPassD[ps]->Drafting_EdgD(iseg)) )
        {
            if ( pcSched->pcSupPassD[ps]->EnEdgD_Present() )
            {
                pcEdgD = pcSched->pcSupPassD[ps]->pcEnEdgD[iseg];
            }
            else if ( pcSched->pcSupPassD[ps]->ExEdgD_Present() )
            {
                pcEdgD = pcSched->pcSupPassD[ps]->pcExEdgD[iseg];
            }
            if ( pcSched->pcSupPassD[ps]->pcPass->awc )
            {
                if ( !pcEdgD->dummied )
                {
                    awc_avail = true;
                    break;
                }
            }
        }
    }
    if ( !awc_avail )
    {
            DMSG(-diagLvl)
                << (const char*)pcSched->objName()
                << " no AWC passes left from fstpas= " << fstpas
                << " to lstpas= " << lstpas
                << END_OF_MESSAGE;
//AEB        return true;
    }

    //----------
    // Scratch
    //----------
    bool    awc_active;
    int     edg_num;
    float   wrvern = 0.0;
    float   wfvern = 0.0;
	float   pr_pdi_rwid = 0.0;		//add by billy c 20150827
	float   pr_width_mis = 0.0;		//add by billy c 20150827
	float   width_mis = 0.0;		//add by billy c 20150827


    //--------------------------------------
    // Set awc_ok calculation flag
    //--------------------------------------
    pcSched->pcSetupD->awc_ok = false;

    //--------------------------------------
    // Get setup quantities into awc names
    //--------------------------------------
//    wrvern = pcSched->pcRAMP->state.rmx_wid_vern;  // RM width vernier // delete by xubin 2012.10.9
//    wfvern = pcSched->pcRAMP->state.fmx_wid_vern;  // FM width vernier // delete by xubin 2012.10.9
//======================================================================================================start add by xubin 2012.10.9
		if ( pcSched->pcRAMP->adapted_state.family_prv != pcSched->pcRAPP->state.family )
		{
			wrvern = pcSched->pcRAMP->state.rmx_wid_vern * ( 1 - pcSched->pcRAPP->state.rwid_off_mult ) + 
				pcSched->pcRAPP->state.rmx_wid_off * pcSched->pcRAPP->state.rwid_off_mult;
		}
		else
		{
			wrvern = pcSched->pcRAMP->state.rmx_wid_vern;
		}
		if ( pcSched->pcRAMP->adapted_state.family_prv != pcSched->pcRAPP->state.family )
		{
			wfvern = pcSched->pcRAMP->state.fmx_wid_vern * ( 1 - pcSched->pcRAPP->state.fwid_off_mult ) + 
				pcSched->pcRAPP->state.fmx_wid_off * pcSched->pcRAPP->state.fwid_off_mult;
		}
		else
		{
			wfvern = pcSched->pcRAMP->state.fmx_wid_vern;
		}
//======================================================================================================end add by xubin 2012.10.9

//======================================================================================================start add by billy chao 20150827

		DMSG(-diagLvl) 
			<< "wrvern= " << wrvern
            << END_OF_MESSAGE;
		DMSG(-diagLvl) 
			<< "pcSched->pcRAMP->state.pr_pdi_rwid= " 
			<< pcSched->pcRAMP->state.pr_pdi_rwid
            << END_OF_MESSAGE;
		DMSG(-diagLvl) 
			<< "pcSched->pcRAMP->state.pr_tgt_wid = " 
			<< pcSched->pcRAMP->state.pr_tgt_wid
            << END_OF_MESSAGE;
		DMSG(-diagLvl) 
			<< "pcSched->pcPDI->state.slabw= " 
			<< pcSched->pcPDI->state.slabw
            << END_OF_MESSAGE;
		DMSG(-diagLvl) 
			<< "pcSched->pcPDI->state.fxwaim= " 
			<< pcSched->pcPDI->state.fxwaim
            << END_OF_MESSAGE;

		pr_width_mis = (fabs (pcSched->pcRAMP->state.pr_pdi_rwid - pcSched->pcRAMP->state.pr_tgt_wid ));
		width_mis = (fabs (pcSched->pcPDI->state.slabw - pcSched->pcPDI->state.fxwaim));
		
		DMSG(-diagLvl) 
			<< "pr_width_mis= " << pr_width_mis
            << END_OF_MESSAGE;
		DMSG(-diagLvl) 
			<< "width_mis= " << width_mis
            << END_OF_MESSAGE;
		
		if ( pr_width_mis != width_mis )
		{	
			if ( pr_width_mis != 0.0F && width_mis != 0.0F )
			{
				if ( pr_width_mis > width_mis )
				{	
					wrvern = wrvern + 10.0F * ( 1.0F - width_mis / pr_width_mis );
					
					DMSG(-diagLvl) 
						<< "wrvern + 10.0F * ( 1.0F - width_mis / pr_width_mis )= " << wrvern
						<< END_OF_MESSAGE;
				}
				else
				{
					wrvern = wrvern - 10.0F * ( 1.0F - pr_width_mis / width_mis );
					
					DMSG(-diagLvl) 
						<< "wrvern - 10.0F * ( 1.0F - pr_width_mis / width_mis )= " << wrvern
						<< END_OF_MESSAGE;
				}
			}
			else if (pr_width_mis = 0.0F )
			{
				wrvern = wrvern - 2.0F * ( width_mis / 10.0F );	
				DMSG(-diagLvl) 
					<< "wrvern - 2.0F * ( width_mis / 10.0F )= " << wrvern
					<< END_OF_MESSAGE;

			}
			else
			{
				wrvern = wrvern + 2.0F * ( pr_width_mis / 10.0F );	
				DMSG(-diagLvl) 
					<< "wrvern + 2.0F * ( pr_width_mis / 10.0F )= " << wrvern
					<< END_OF_MESSAGE;

			}
		}
        wrvern = cMathUty::Clamp( wrvern,
                                        -15.0F,
                                        15.0F );
		DMSG(-diagLvl) 	
			<< "wrvern= " 
			<< wrvern 
			<< END_OF_MESSAGE;
		

//======================================================================================================end add by billy chao 20150827

	//---------------------------
    // Get operator [%] offsets
    //---------------------------
    opr_hd_tpr = pcSched->pcESys->state.opr_hd_tpr;
    opr_tl_tpr = pcSched->pcESys->state.opr_tl_tpr;
    opr_hd_len = pcSched->pcESys->state.opr_hd_len;
    opr_tl_len = pcSched->pcESys->state.opr_tl_len;

    //----------------------------------
    //  For each pass scheduled by model
    //----------------------------------
    for ( ps = fstpas; ps <= lstpas; ps++ )
    {

        //-------------------------------
        // reset head/tail enable flags
        //-------------------------------
        ps_hd_enab[ps] = false;
        ps_tl_enab[ps] = false;

        //---------------------------
        // clear data structures
        //---------------------------
        awc_ok[ps]    = false;
        edg_num       = 0;
        pegdft[ps]    = 0.0F;
        pegrcv[ps]    = 0.0F;
        htl_mult[ps]  = 0.0F;
        ttl_mult[ps]  = 0.0F;
        nhto_mult[ps] = 0.0F;
        whto_mult[ps] = 0.0F;
        ntto_mult[ps] = 0.0F;
        wtto_mult[ps] = 0.0F;
        tprl_rate[ps] = 0.0F;

        cEdgD   *pcEdgD;

        pcEdgD = NULL;
        if ( pcSched->pcSupPassD[ps]->EnEdgD_Present() )
        {
            pcEdgD = pcSched->pcSupPassD[ps]->pcEnEdgD[iseg];
        }
        else if ( pcSched->pcSupPassD[ps]->ExEdgD_Present() )
        {
            pcEdgD = pcSched->pcSupPassD[ps]->pcExEdgD[iseg];
        }

        if( pcEdgD != NULL && pcEdgD->draft > 0.0F )
        {
            //--------------------------------------
            // Get edger number and maximum stroke
            //--------------------------------------
            edg_num = pcEdgD->pcEdg->num;

            //----------------------------------------------
            // Check for AWC and dummied edger on this pass
            //----------------------------------------------
            if ( pcSched->pcSupPassD[ps]->pcPass->awc &&
                 pcEdgD->draft    > 0.0F                    &&
                 pcEdgD->load_act > 0.0F )
            {

                pcEdgD->active   = false;

                htl_mult[ps]  = pcEdgD->pcEdg->htl_mult;
                ttl_mult[ps]  = pcEdgD->pcEdg->ttl_mult;
                nhto_mult[ps] = pcEdgD->pcEdg->nhto_mult;
                whto_mult[ps] = pcEdgD->pcEdg->whto_mult;
                ntto_mult[ps] = pcEdgD->pcEdg->ntto_mult;
                wtto_mult[ps] = pcEdgD->pcEdg->wtto_mult;
                dead_band[ps] = pcEdgD->pcEdg->dead_band;
                tpr_min[ps]   = pcEdgD->pcEdg->tpr_min;
                tpr_max[ps]   = pcEdgD->pcEdg->tpr_max;

                // Check for exit edger
                if ( !pcSched->pcSupPassD[ps]->EnEdgD_Present() &&
                     pcSched->pcSupPassD[ps]->ExEdgD_Present() )
                {
                    tpr_max[ps]   = pcEdgD->pcEdg->tpr_max_xedg;
                }

                //-------------------------------
                // Edger/cylinder in AUTO logic
                //-------------------------------
                awc_active = ( pcEdgD->cyl_avail &&
                               pcEdgD->cyl_auto );

                //-----------------------------
                // Get awc function selection
                //-----------------------------
                if ( pcSched->pcSupPassD[ps]->pcPass->frwd )
                {   // forward direction
                    ps_hd_enab[ps] = pcEdgD->hd_enab;
                    ps_tl_enab[ps] = pcEdgD->tl_enab;
                }
                else
                {   // reverse direction
                    ps_hd_enab[ps] = pcEdgD->tl_enab;
                    ps_tl_enab[ps] = pcEdgD->hd_enab;
                }

                //----------------------------------------
                // Check for edger and cylinder presence
                //----------------------------------------

                //--------------------------------------------------------------------
                // If edger cylinder available and AUTO
                // NOTE: should not check for edge draft larger than zero, because
                //       in case of extreme spread order edge draft will be zero, or
                //       set to min draft limit. This is a condition that causes
                //       wide head and tail end, and AWC should be active to remove
                //       this excess mat'l.
                //--------------------------------------------------------------------
                if ( awc_active )
                {
                    awc_ok[ps] = true;

                    //----------------------------------------------
                    // Calculate taper length per taper offset rate,
                    // based on max cylinder speed and edger speed
                    // Note: multiplier of 2 indicates of both sides
                    //       (operator & drive) cylinder motions
                    //----------------------------------------------
                    tprl_rate[ps] = pcEdgD->speed / 2.0F
                                  / pcEdgD->pcEdg->cyl_speed;
                }


                //--------------------
                // Set awc action flag
                //--------------------
                pcEdgD->active = false;
                if ( awc_active &&
                     ( pcEdgD->hd_enab   ||
                       pcEdgD->tl_enab   ||
                       pcEdgD->wid_enab  ||
                       pcEdgD->hrd_enab   ) )
                {
                    pcEdgD->active = true;
                }
            }
            else
            {
                pcEdgD->active       = false;
                pcEdgD->hd_tpr       = 0.0F;
                pcEdgD->tl_tpr       = 0.0F;
                pcEdgD->hd_len       = 0.0F;
                pcEdgD->tl_len       = 0.0F;
                pcEdgD->dfrc_ddraft  = 0.0F;
                pcEdgD->dfrc_dtmp    = 0.0F;
                pcEdgD->dwiderr_dfrc = 0.0F;
                pcEdgD->dwiderr_dtmp = 0.0F;
			}   // end if ( pcSched->pcSupPassD[ps]->pcPass->awc )

            //-------------------------------------------
            // include RM width vernier in the edge draft
            // and limit check the new edge draft
            //-------------------------------------------
            pegdft[ps] = pcEdgD->draft;

			DMSG(-diagLvl) 	
				<< "wrvern= " 
				<< wrvern 
				<< END_OF_MESSAGE;
			DMSG(-diagLvl) 	
				<< "pegdft[" << ps << "]= " 
				<< pegdft[ps] 
				<< END_OF_MESSAGE;

            if ( this->wid_vern_enab )
            {
                pegdft[ps] += wrvern * (float)(ps) / (float)((lstpas+1) - fstpas);
            }
			
			DMSG(-diagLvl) 	
				<< "pegdft[" << ps << "]= " 
				<< pegdft[ps] 
				<< END_OF_MESSAGE;
            
			pegdft[ps] = cMathUty::Clamp( pegdft[ps],
                                          pcEdgD->draft_min,
                                          pcEdgD->draft_max );

			DMSG(-diagLvl) 	
				<< "pegdft[" << ps << "]= " 
				<< pegdft[ps] 
				<< END_OF_MESSAGE;


            // Redefine variables for purpose of unit conversion
            psentw[ps] = pcEdgD->pcEnPceD->width;  // entry piece width
            psentl[ps] = pcEdgD->pcEnPceD->length; // entry piece length

            // Check for Drafting Entry/Exit Edger
            if ( pcEdgD != pcSched->pcSupPassD[ps]->pcExEdgD[iseg] )
            {
                pegrcv[ps] = pcEdgD->pcExPceD->recovery;                      // recovery
                psextw[ps] = pcSched->pcSupPassD[ps]->pcExPceD[iseg]->width;  // exit piece width

                // Check for previous pass exit edging
                if ( ps > 1 && NULL != pcSched->pcSupPassD[ps-1]->pcExEdgD[iseg] &&
                     pcSched->pcSupPassD[ps-1]->pcExEdgD[iseg]->draft > 0.0F )
                {
                    pegrcv[ps] -= pcSched->pcSupPassD[ps-1]->pcExEdgD[iseg]->pcExPceD->recovery;                      // recovery
                }
            }
            else
            {
                pegrcv[ps] = pcEdgD->pcExPceD->recovery;    // recovery
                psextw[ps] = pcEdgD->pcExPceD->width;       // exit piece width
            }
            //--------------------------------------------
            // Preportion the recovery to the actual draft
            //--------------------------------------------
            if ( pcEdgD->draft > 0.0F )
            {
                pegrcv[ps] *= pegdft[ps] / pcEdgD->draft;   // recovery
            }

        }   // end if ( pcEdgD != NULL )
        else if( pcEdgD != NULL )
        {   // edger exist, but it is dummied
            //--------------------------------
            // Reset AWC quantities
            //--------------------------------
            pcEdgD->active       = false;
            pcEdgD->hd_tpr       = 0.0F;
            pcEdgD->tl_tpr       = 0.0F;
            pcEdgD->hd_len       = 0.0F;
            pcEdgD->tl_len       = 0.0F;
            pcEdgD->dfrc_ddraft  = 0.0F;
            pcEdgD->dfrc_dtmp    = 0.0F;
            pcEdgD->dwiderr_dfrc = 0.0F;
            pcEdgD->dwiderr_dtmp = 0.0F;


            pegdft[ps] = 0.0F;                     // Edger draft
            psentw[ps] = pcEdgD->pcEnPceD->width;  // entry piece width
            psentl[ps] = pcEdgD->pcEnPceD->length; // entry piece length
            // Check for Entry/Exit Edger
            if ( pcEdgD != pcSched->pcSupPassD[ps]->pcExEdgD[iseg] )
            {
                pegrcv[ps] = pcEdgD->pcExPceD->recovery;                       // recovery
                psextw[ps] = pcSched->pcSupPassD[ps]->pcExPceD[iseg]->width;   // exit piece width
            }
            else
            {
                pegrcv[ps] = pcEdgD->pcExPceD->recovery;    // recovery
                psextw[ps] = pcEdgD->pcExPceD->width;       // exit piece width
            }
        }
        else
        {   // No edger on this pass
            psentw[ps] = pcSched->pcSupPassD[ps]->pcStdD[iseg]->pcEnPceD->width;    // entry piece width
            psentl[ps] = pcSched->pcSupPassD[ps]->pcStdD[iseg]->pcEnPceD->length;   // entry piece length
            psextw[ps] = pcSched->pcSupPassD[ps]->pcExPceD[iseg]->width;            // exit piece width
            pegdft[ps] = 0.0F;                                                      // Edger draft
            pegrcv[ps] = pcSched->pcSupPassD[ps]->pcStdD[iseg]->pcEnPceD->recovery; // recovery
        }   // end if( pcSched->pcSupPassD[ps]->Drafting_EdgD(iseg) != NULL )

        //--------------------------------------------------
        // Redefine variables for purpose of unit conversion
        //--------------------------------------------------

		if( pcSched->pcSupPassD[ps]->pcStdD[iseg] != NULL )
		{
            psdrft[ps] = pcSched->pcSupPassD[ps]->pcStdD[iseg]->draft;    // Stand draft
            pspred[ps] = pcSched->pcSupPassD[ps]->pcStdD[iseg]->spread;   // Stand spread
        }
        else
        {
            psdrft[ps] = 0.0;    // Stand draft
            pspred[ps] = 0.0;   // Stand spread
		} // end if

        psthick[ps] = pcSched->pcSupPassD[ps]->pcEnPceD[iseg]->thick; // entry piece thickness

        // Check for units system
        if ( Physcon.units_english == pcSched->units)
        {
            // convert from "feet" to "milimeters"
            psentl[ps]    *= Physcon.mmpm / Physcon.ftpm;
            // convert from "inches" to "milimeters"
            dead_band[ps] *= Physcon.mmpin;
            tpr_min[ps]   *= Physcon.mmpin;
            tpr_max[ps]   *= Physcon.mmpin;
            pegdft[ps]    *= Physcon.mmpin;
            pegrcv[ps]    *= Physcon.mmpin;
            psentw[ps]    *= Physcon.mmpin;
            psextw[ps]    *= Physcon.mmpin;
            psdrft[ps]    *= Physcon.mmpin;
            pspred[ps]    *= Physcon.mmpin;
            psthick[ps]   *= Physcon.mmpin;
        }
        else
        {
            // convert from "meters" to "milimeters"
            psentl[ps] *= Physcon.mmpm;
        }

    }   //  end for ( ps = fstpas; ps <= lstpas; ps++ )

    //----------------------------------
    // Set AWC status
    //  green   = awc is active
    //  yellow  = awc is not active
    //  red     = awc calculation Invalid
    //----------------------------------
    pcSched->pcSetupD->awc_status = cMdlparam::cs_yellow;
    for ( ps = 1; ps <= lstpas; ps++ )
    {
        cEdgD   *pcEdgD;
        if ( NULL != (pcEdgD= pcSched->pcSupPassD[ps]->Drafting_EdgD(iseg)) )
        {
            if ( pcSched->pcSupPassD[ps]->EnEdgD_Present() )
            {
                pcEdgD = pcSched->pcSupPassD[ps]->pcEnEdgD[iseg];
            }
            else if ( pcSched->pcSupPassD[ps]->ExEdgD_Present() )
            {
                pcEdgD = pcSched->pcSupPassD[ps]->pcExEdgD[iseg];
            }
            if ( pcEdgD->active )
            {
                pcSched->pcSetupD->awc_status = cMdlparam::cs_green;
                break;
            }
        }
    }

    //----------------------------------
    // Calculate references
    //----------------------------------
    this->Taper_Pass ( fstpas, lstpas );

    //----------------------------------
    // For each pass scheduled by model
    //----------------------------------
    for ( ps = fstpas; ps <= lstpas; ps++ )
    {
        // Check for units system
        if ( Physcon.units_english == pcSched->units)
        {
            // convert from "milimeters" to "feet"
            tprlhd[ps]  /= Physcon.mmpm * Physcon.ftpm;
            tprltl[ps]  /= Physcon.mmpm * Physcon.ftpm;
            // convert from "milimeters" to "inches"
            tprfhd[ps]  /= Physcon.mmpin;
            tprftl[ps]  /= Physcon.mmpin;
            awid_hd[ps] /= Physcon.mmpin;
            awid_tl[ps] /= Physcon.mmpin;
            if ( ps == lstpas )
            {
                awid_hd[ps+1] /= Physcon.mmpin;
                awid_tl[ps+1] /= Physcon.mmpin;
            }
        }
        else
        {
            // convert "milimeters" back to "meters"
            tprlhd[ps] /= Physcon.mmpm;
            tprltl[ps] /= Physcon.mmpm;
        }

        //------------------------------
        // check for NULL edger pointer
        //------------------------------
        cEdgD   *pcEdgD;
        if ( NULL != (pcEdgD= pcSched->pcSupPassD[ps]->Drafting_EdgD(iseg)) )
        {
            if ( pcSched->pcSupPassD[ps]->EnEdgD_Present() )
            {
                pcEdgD = pcSched->pcSupPassD[ps]->pcEnEdgD[iseg];
            }
            else if ( pcSched->pcSupPassD[ps]->ExEdgD_Present() )
            {
                pcEdgD = pcSched->pcSupPassD[ps]->pcExEdgD[iseg];
            }
        }

        if( pcEdgD != NULL )
        {
            //-------------------
            // Check for AWC pass
            //-------------------
            if ( pcSched->pcSupPassD[ps]->pcPass->awc &&
                 pcEdgD->draft > 0.0F )
            {
                // Set awc_ok flag
                if ( pcEdgD->active )
                {
                    pcSched->pcSetupD->awc_ok = true;
                }

                //--------------------------------
                // Set head and tail taper offsets
                //--------------------------------
                pcEdgD->hd_tpr = tprfhd[ps];
                pcEdgD->tl_tpr = tprftl[ps];

                //----------------------------------
                // Set head and tail taper distances
                //----------------------------------
                pcEdgD->hd_len = tprlhd[ps];
                pcEdgD->tl_len = tprltl[ps];

                //-----------------------------------------------------------
                // Transfer functions
                //-----------------------------------------------------------
                pcEdgD->dfrc_ddraft = pcEdgD->DfDwx();

                pcEdgD->dfrc_dtmp   = pcEdgD->DfDt();

                float   draft;
                float   groov_mult;
                float   recovery;
                float   effi;
                float   perturb = 0.01F;
                float   ddraft;
                float   dwiderr = pcEdgD->draft
                                * pcEdgD->effi;

                draft = ( 1.0 - perturb/2.0) * pcEdgD->draft;
                // ----------------------------------
                //  Calculates edger recovery amount.
                // ----------------------------------
                if ( !pcWidth->Recovery(
                        pcEdgD->pcEnPceD->pcPce->family,
                        pcEdgD->pcEnPceD->thick,
                        pcEdgD->pcEnPceD->width,
                        draft,
                        pcEdgD->pcStdRollPrD->getAvgDiam(op_edger),
                        pcEdgD->pcEdg->grooved,
                        pcEdgD->pcEdg->throat,
                        pcEdgD->pcEdg->angle,
                        pcEdgD->pcEdg->diam_max,
                        pcEdgD->pcEdg->diam_min,
                        groov_mult,
                        recovery,
                        effi ) )
                {
                    EMSG << "INVALID Recovery  status"
                         << END_OF_MESSAGE;

                    return false;
                }

                dwiderr -= draft  * effi;
                ddraft = draft * perturb / 2.0;

                //-----------------------------------------------------------
                // Calculate dfrc_ddraft
                //
                // dwiderr_dfrc = dwiderr_ddraft / dfrc_ddraft * hcf_mult
                //-----------------------------------------------------------
                if ( pcEdgD->dfrc_ddraft > 0.0F &&
                     ddraft > 0.0F )
                {
                    pcEdgD->dwiderr_dfrc =
                         ( dwiderr / ddraft )
                        / pcEdgD->dfrc_ddraft
                        * pcEdgD->pcEdg->hcf_mult;

                    // Limit check the result
                    pcEdgD->dwiderr_dfrc =
                        cMathUty::Clamp( pcEdgD->dwiderr_dfrc,
                                         0.0F,
                                         this->DwidDfrc_hi_lim );
                }
                else
                {
                    pcEdgD->dwiderr_dfrc = 0.0F;
                }

                //-----------------------------------------------------------
                // Calculate dfrc_dtmp
                //
                // dwiderr_dtmp = dwiderr_dfrc * dfrc_dtmp * hct_mult
                //-----------------------------------------------------------
                if ( pcEdgD->dfrc_dtmp > 0.0F &&
                     ddraft > 0.0F )
                {
                    pcEdgD->dwiderr_dtmp =
                          pcEdgD->dwiderr_dfrc
                        * pcEdgD->dfrc_dtmp
                        * pcEdgD->pcEdg->hct_mult;

                    // Limit check the result
                    pcEdgD->dwiderr_dtmp =
                        cMathUty::Clamp( pcEdgD->dwiderr_dtmp,
                                         0.0F,
                                         this->DwidDtmp_hi_lim );
                }
                else
                {
                    pcEdgD->dwiderr_dtmp = 0.0F;
                }

            }   //  end if ( pcSched->pcSupPassD[ps]->pcPass->awc )

        }   //  end if ( pcEdgD != NULL )

        //----------------------------------
        // Set actual head/tail Entry width
        //----------------------------------
        pcSched->pcSupPassD[ps]->ewidth_hd = awid_hd[ps];
        pcSched->pcSupPassD[ps]->ewidth_tl = awid_tl[ps];

        //----------------------------------
        // Set actual head/tail Exit width
        //----------------------------------
        if ( frwd[ps+1] )
        {   // forward direction for next pass
            pcSched->pcSupPassD[ps]->xwidth_hd = awid_hd[ps+1];
            pcSched->pcSupPassD[ps]->xwidth_tl = awid_tl[ps+1];
        }
        else
        {   // reverse direction for next pass
            pcSched->pcSupPassD[ps]->xwidth_hd = awid_tl[ps+1];
            pcSched->pcSupPassD[ps]->xwidth_tl = awid_hd[ps+1];
        }

    }   //  end for ( ps = fstpas; ps <= lstpas; ps++ )

    return true;

}   // cAWCSetup::Setup
