//---------------------------------------------------------------------------------
//
// ABSTRACT:
//     This file defines the base static and dynamic stand object methods.
//
//
//     FUNCTION/PROCEDURE/TASK  DESCRIPTION
//     -----------------------  -----------------------------------------------
//   static
//     cBaseMillStd             Stand object constructor
//     cBaseMillStd             Stand object copy constructor
//     ~cBaseMillStd            Stand object destructor
//     operator=                assignment operator
//     operator==               equivalence operator
//     operator<                less than operator
//     linkObj                  Method for linking stand to other objects
//     dump                     dump member data and/or composed objects
//     zeroData                 zero data for base std
//   dynamic
//     cBaseMillStdD                Stand object constructor
//     cBaseMillStdD                Stand object copy constructor
//     ~cBaseMillStdD               Stand object destructor
//     operator=                assignment operator
//     operator==               equivalence operator
//     operator<                less than operator
//     linkObj                  Method for linking stand to other objects
//     dump                     dump member data and/or composed objects
//     Post_Config              Virtual function to allow the user to carry
//                              out post processing for a dynamic stand.
//     zeroData                 zero data for base std
//     Assign_State             Assigns state data from a source object to a 
//                              destination object ofthe same type.
//     Operate                  Given entry piece data and other data supplied by external
//                              sources (ex: draft), this method calculates relevant 
//                              dynamic stand values and updates the exit piece state.
//     Tension                  Calculate the tension applied to the exit piece.  The user
//                              must supply a function on the derived stand to calculate
//                              something other than zero.
//     Flowstress               Calculate the mean flowstress in the rollbite.  The user 
//                              must supply a function on the derived stand to calculate
//                              something other than zero. 
//-----------------------------------------------------------------------------
//------------------------------
// C++ standard library includes
//------------------------------
#include <stdlib.h>

#include "basemillstd.hxx"

#include "objhash.hxx"
#include "mill.hxx"
#include "rollbite.hxx"
#include "stdrollpr.hxx"
#include "mtr.hxx"
#include "physcon.hxx"
#include "width.hxx"
#include "mathuty.hxx"
#include "basepce.hxx"
#include "utility.hxx"
#include "flowstress.hxx"
#include "tvd.hxx"

#ifdef WIN32
    #ifdef _DEBUG
    #define new DEBUG_NEW
    #endif
    #pragma warning(disable: 4244) // double to float conversion (NT thinks constants are doubles)
#endif

// Diagnostic level specific to this file
static const cAlarm::DiagnosticCodeEnum diagLvl(cAlarm::Std);

// Data schema for the cBaseMillStd class.
static cSchema::schema_type cBaseMillStd_schema[]=
{
    //Next  Enum  Schema details                            Fmt  Units        Comment
    //====  ====  ========================================  ==== ===========  ==================================================
    { NULL, NULL, SCHEMA_T(cBaseMillStd,float,bitangle),    "",  "deg",       "bite angle limit" },
    { NULL, NULL, SCHEMA_T(cBaseMillStd,float,cof),         "",  "",          "coefficient of friction" },
    { NULL, NULL, SCHEMA_T(cBaseMillStd,float,cof_lube),    "",  "",          "coefficient of friction with lubrication" },
    { NULL, NULL, SCHEMA_T(cBaseMillStd,float,dmy_ofs),     "",  "mm_in",     "dummied stand gap offset" },
    { NULL, NULL, SCHEMA_T(cBaseMillStd,float,force_max),   "",  "mton_aton_kN", "maximum stand force limit" },
    { NULL, NULL, SCHEMA_T(cBaseMillStd,float,gearat),      "",  "",          "gear ratio between stand and motor\n"\
                                                                              "(ROLL RPM * GEARRAT = MOTOR RPM)\n"\
                                                                              "(gearrat = #stand teeth/#motor teeth)" },
    { NULL, NULL, SCHEMA_T(cBaseMillStd,bool,use_operate),  "",  "",          "If true, use operate when calculating transfer functions" },
    //-------------------------------------------------------------------------
    // The following is a pointer to the motor that makes up this mill stand object
    //-------------------------------------------------------------------------
    { NULL, NULL, SCHEMA_PO(cBaseMillStd,cMtr,pcMtr),          "",  "",         "motor" },
    { 0 }   // terminate list
};

// Link all the schema's together
cSchema::schema_name_type cBaseMillStd::sSchema[]=
{
    { 
        "cBaseMillStd",                         // name
        sizeof(cBaseMillStd),                   // size
        cBaseMillStd_schema,                    // schema
        false,                              // packed
        true,                               // allow ptr
        false,                              // Read only
        "Base Static stand configuration",  // comment
        0                                   // offset to config data
    },

    { 0 } // terminate list
};

// Data schema for the cBaseMillStdD class.
static cSchema::schema_type cBaseMillStdD_schema[]=
{
    //Next  Enum  Schema details                            Fmt  Units        Comment
    //====  ====  ========================================  ==== ===========  ==================================================
    LINK_TO_SCHEMA("cBaseCorrD","cBaseCorrD")

    { NULL, NULL, SCHEMA_T(cBaseMillStdD,float,arcon),          "",  "mm_in",     "arc of contact length" },
    { NULL, NULL, SCHEMA_T(cBaseMillStdD,float,cof),            "",  "",          "coefficient of friction" },
    { NULL, NULL, SCHEMA_T(cBaseMillStdD,float,defrad),         "",  "mm_in",     "deformed roll radius" },
    { NULL, NULL, SCHEMA_T(cBaseMillStdD,float,draft),          "",  "mm_in",     "draft" },
    { NULL, NULL, SCHEMA_T(cBaseMillStdD,float,draft_comp),     "",  "",          "draft compensation" },
    { NULL, NULL, SCHEMA_T(cBaseMillStdD,float,draft_load),     "",  "pu",        "draft load distribution" },
    { NULL, NULL, SCHEMA_T(cBaseMillStdD,float,draft_min),      "",  "mm_in",     "minimum draft" },
    { NULL, NULL, SCHEMA_T(cBaseMillStdD,float,draft_max),      "",  "mm_in",     "maximum draft" },
    { NULL, NULL, SCHEMA_T(cBaseMillStdD,float,draft_pu),       "",  "pu",        "per unit draft" },
    { NULL, NULL, SCHEMA_T(cBaseMillStdD,bool,dummied),         "",  "",          "boolean indicating stand dummied" },
    { NULL, NULL, SCHEMA_T(cBaseMillStdD,float,duration),       "",  "sec",       "time required for piece point to traverse roll bite" },
    { NULL, NULL, SCHEMA_T(cBaseMillStdD,float,force_min),      "","mton_eton_kN",     "minimum force on strip" },
    { NULL, NULL, SCHEMA_T(cBaseMillStdD,float,force_max),      "","mton_eton_kN",     "maximum force on strip" },
    { NULL, NULL, SCHEMA_T(cBaseMillStdD,float,force_mult),     "",  "",          "force multiplier" },
    { NULL, NULL, SCHEMA_T(cBaseMillStdD,float,force_strip),    "",  "mton_eton_kN", "force on strip" },
    { NULL, NULL, SCHEMA_T(cBaseMillStdD,float,fs),             "",  "kg/mm2_lb/in2_MPa", "flow stress" },
    { NULL, NULL, SCHEMA_T(cBaseMillStdD,float,gapoff),         "",  "mm_in",     "Roll Heating/Wear Modifier" },
    { NULL, NULL, SCHEMA_T(cBaseMillStdD,float,limrat_dft),     "",  "",          "Draft limit ratio" },
    { NULL, NULL, SCHEMA_T(cBaseMillStdD,float,limrat_frc),     "",  "",          "Force limit ratio" },
    { NULL, NULL, SCHEMA_T(cBaseMillStdD,float,limrat_pwr),     "",  "",          "Power limit ratio" },
    { NULL, NULL, SCHEMA_T(cBaseMillStdD,float,load_act),       "",  "pu",        "per unit (actual) load distribution" },
    { NULL, NULL, SCHEMA_T(cBaseMillStdD,float,load_init),      "",  "pu",        "initial load distribution" },
    { NULL, NULL, SCHEMA_T(cBaseMillStdD,float,power_bear),     "",  "kW",        "bearing power loss" },
    { NULL, NULL, SCHEMA_T(cBaseMillStdD,float,power_def),      "",  "kW",        "deformation power" },
    { NULL, NULL, SCHEMA_T(cBaseMillStdD,float,power_fri),      "",  "kW",        "friction power" },
    { NULL, NULL, SCHEMA_T(cBaseMillStdD,float,power_max),      "",  "",          "maximum power limit" },
    { NULL, NULL, SCHEMA_T(cBaseMillStdD,float,power_mult),     "",  "",          "power multiplier" },
    { NULL, NULL, SCHEMA_T(cBaseMillStdD,float,power_red),      "",  "kW",        "reduction power (deformation + friction)" },
    { NULL, NULL, SCHEMA_T(cBaseMillStdD,float,power_shaft),    "",  "kW",        "motor shaft power" },
    { NULL, NULL, SCHEMA_T(cBaseMillStdD,float,power_ten),      "",  "kW",        "tension power" },
    { NULL, NULL, SCHEMA_T(cBaseMillStdD,float,power_torque),   "",  "kW",        "torque power at work roll" },
    { NULL, NULL, SCHEMA_T(cBaseMillStdD,float,pudmax),         "",  "pu",        "maximum per-unit draft" },
    { NULL, NULL, SCHEMA_T(cBaseMillStdD,float,pudmin),         "",  "pu",        "minimum per-unit draft" },
    { NULL, NULL, SCHEMA_T(cBaseMillStdD,bool,roll_lube),       "",  "",          "roll lubrication is on" },
    { NULL, NULL, SCHEMA_T(cBaseMillStdD,float,rpm),            "",  "rpm",       "Motor rpm" },
    { NULL, NULL, SCHEMA_T(cBaseMillStdD,float,slip),           "",  "",          "slip (strip speed / roll speed)" },
    { NULL, NULL, SCHEMA_T(cBaseMillStdD,float,speed),          "",  "mpm_fpm_mps", "work roll peripheral speed" },
    { NULL, NULL, SCHEMA_T(cBaseMillStdD,float,mstrate),        "",  "1/sec",      "mean strain rate" },
    { NULL, NULL, SCHEMA_T(cBaseMillStdD,float,torque),         "",  "kgm_ftlb_Nm","rolling torque calculated from power and speed" },
    { NULL, NULL, SCHEMA_T(cBaseMillStdD,float,torque_max),     "",  "kgm_ftlb_Nm","maximum rolling torque at motor shaft" },
    { NULL, NULL, SCHEMA_T(cBaseMillStdD,float,torque_pu),      "",  "pu",        "per unit rolling torque" },
    { NULL, NULL, SCHEMA_T(cBaseMillStdD,float,torque_rb),      "",  "kgm_ftlb_Nm","rolling torque calculated by integrating in rollbite" },
    { NULL, NULL, SCHEMA_T(cBaseMillStdD,float,volume_flow),    "",  "mm2m_in2ft","Volume flow in the roll bite" },
    //-------------------------------------------------------------------------
    // The following are pointers to individual data for the rolls, entry
    // and exit piece, rollbite & TVD.
    //-------------------------------------------------------------------------
    { NULL, NULL, SCHEMA_PO(cBaseMillStdD,cBaseMillStd,pcBaseMillStd),          "",  "",         "pointer to base static millstand" },
    { NULL, NULL, SCHEMA_PO(cBaseMillStdD,cStdRollPrD,pcStdRollPrD),            "",  "",         "pointer to work roll pair state" },
    { NULL, NULL, SCHEMA_PO(cBaseMillStdD,cBasePceD,pcEnBasePceD),              "",  "",         "Pointer to entry piece state" },
    { NULL, NULL, SCHEMA_PO(cBaseMillStdD,cBasePceD,pcExBasePceD),              "",  "",         "Pointer to exit piece state" },
    { NULL, NULL, SCHEMA_PO(cBaseMillStdD,cRollbite,pcRollbite),                "",  "",         "Pointer to Rollbite class" },
    { NULL, NULL, SCHEMA_PO(cBaseMillStdD,cTVD,pcTVD),                          "",  "",         "pointer to TVD class which knows TVD for this object" },
    { 0 }   // terminate list

};

// Link all the schema's together
cSchema::schema_name_type cBaseMillStdD::sSchema[]=
{
    { 
        "cBaseMillStdD",                        // name
        sizeof(cBaseMillStdD),                  // size
        cBaseMillStdD_schema,                   // schema
        false,                              // packed
        true,                               // allow ptr
        false,                              // Read only
        "Base Dynamic stand data",          // comment
        0                                   // offset to config data
    },

    { 0 } // terminate list
};




//-----------------------------------------------------------------------------
// Static Stand Object
//-----------------------------------------------------------------------------
//-----------------------------------------------------------------------------
// cBaseMillStd CONSTRUCTOR ABSTRACT:
//   Stand constructor
//-----------------------------------------------------------------------------
int cBaseMillStd::count   (0); // initialize static members

cBaseMillStd::cBaseMillStd()                // default constructor
    : cBase()
    , pcMtr          (0)
{
    Set_Class_Name("cBaseMillStd");
    Set_Schema("cBaseMillStd",sSchema);

    // Zero out member data
    zeroData();
}

cBaseMillStd::cBaseMillStd( const MString     &objectName, 
                            const objTypEnum stdType, 
                            const objPosEnum position, 
                            void       *pHash)
    : cBase( objectName, stdType, position, pHash, 0, 0 )
    , num       (++count)
    , pcMtr      (0)
{
    Set_Class_Name("cBaseMillStd");
    Set_Schema("cBaseMillStd",sSchema);

    // Zero out member data
    zeroData();
}

//-------------------------------------------------------------------------
// ~cBaseMillStd ABSTRACT:
//   Stand deconstructor
//-------------------------------------------------------------------------
cBaseMillStd::~cBaseMillStd()
{
    pcMtr = 0;
}

void cBaseMillStd::zeroData(void)
{
    // Zero out member data  to a valid initial value
    Zero_Data(this, sizeof(cBaseMillStd), Get_Schema("cBaseMillStd::cBaseMillStd"));
}

//-------------------------------------------------------------------------
// cBaseMillStd ABSTRACT:
//   Stand copy constructor
//-------------------------------------------------------------------------
cBaseMillStd::cBaseMillStd (const cBaseMillStd& source)
    : cBase( source )
    , num       (source.num)
    , pcMtr      (0)
{

    if (source.pcMtr)     pcMtr     = new cMtr (*(source.pcMtr));
    //
    //  Data is not copied.
}

//-------------------------------------------------------------------------
// OPERATOR = ABSTRACT:
//   Stand assignment operator
//-------------------------------------------------------------------------
cBaseMillStd& cBaseMillStd::operator= (const cBaseMillStd& source)
{
    if (this != &source)
    {
        cBase::operator = (source);

        num  = source.num;
        
        Copy_Data(this, (void *)&source,sizeof(cBaseMillStd),cBaseMillStd_schema);

        if ( NULL != source.pcMtr )
        {
            pcMtr = source.pcMtr;
        }
    }
    return (*this);
}

//-------------------------------------------------------------------------
// LINKOBJ ABSTRACT
//   Stand link to static sub-objects
//-------------------------------------------------------------------------
bool cBaseMillStd::linkObj(const void        *pVoid, 
                      const objTypEnum  objType,
                      const objPosEnum  objPos )
{
    cBase   *pcBase = (cBase *)(pVoid);
    bool retValue     (TRUE);
    bool retValueBase (TRUE);
    char    class_name[32];

    if (!pcBase)
    {
        EMSG << "Passed child pointer is NULL - exiting" << END_OF_MESSAGE;
        return (retValue);
    }

    // Perform linkObj on next object up in hierarchy
    retValueBase = cBase::linkObj(pVoid, objType, objPos);

    // Get the class name and link base stand objects to the base stand and 
    // create links for parent-child relationship
    sprintf( class_name, "%s", (const char *)(pcBase->Get_Class_Name()) );

    if ( 0 == _stricmp("cRollPair", class_name) )
    {
        EMSG<<"linkObj()  cannot link cRollPair to cBaseMillStd"
            << END_OF_MESSAGE ;
    }
    else if ( 0 == _stricmp("cMtr", class_name) )
    {
        pcBase->parent_obj = this;
        if (pcMtr)
        {
            retValue =FALSE;
        }
        pcMtr = (cMtr*) pcBase;
    }
        
    return retValueBase && retValue;

} // end cBaseMillStd::linkObj()


//-------------------------------------------------------------------------
// DUMP ABSTRACT:
//   Std dump contents of the struct.
// The boolean composed if true indicates that the
// object should call the dump function for the 
// objects that it contains.
//-------------------------------------------------------------------------
void cBaseMillStd::dump(const bool composed)
{
    Dump_Data(stdout, "cBaseMillStd", this, 0, (const char *)objName());

    if (composed)
    {
        if (pcMtr)     pcMtr->dump(composed);
    }
} // end cBaseMillStd::dump


//-----------------------------------------------------------------------------
// Dynamic Stand Object
//-----------------------------------------------------------------------------
//-----------------------------------------------------------------------------
// cBaseMillStdD CONSTRUCTOR ABSTRACT:
//   Stand constructor
//
//   Each dynamic cBaseMillStdD object has a pointer to an instance of
//   the cStdRollPrD object which contains the current roll status.
//   More than one cBaseMillStd object can reference a shared cStdRollPrD.
//
//-----------------------------------------------------------------------------
int cBaseMillStdD::count  (0); // initialize static members
cFlowstress* cBaseMillStdD::pcFlowstress = NULL;

cBaseMillStdD::cBaseMillStdD()
    : cBaseCorrD()
    , pcBaseMillStd  (0)
    , pcEnBasePceD   (0)
    , pcExBasePceD   (0)
    , pcStdRollPrD   (0)
    , pcRollbite     (0)
    , pcTVD          (0)
    , edger          (false)
{
    Set_Class_Name("cBaseMillStdD");
    Set_Schema("cBaseMillStdD",sSchema);

    // Zero out member data
    Zero_Data(this, sizeof(cBaseMillStdD), Get_Schema("cBaseMillStdD"));
    force_mult = 1.0;
    feedback = false;
    slip = 1.0;
}

cBaseMillStdD::cBaseMillStdD( const MString     &objectName, 
                              const objTypEnum stdType,
                              const objPosEnum position,
                              void       *pHash)
    : cBaseCorrD( objectName, stdType, position, pHash )
    , num       (++count)
    , pcBaseMillStd  (0)
    , pcEnBasePceD   (0)
    , pcExBasePceD   (0)
    , pcStdRollPrD   (0)
    , pcRollbite     (0)
    , pcTVD          (0)
    , edger          (false)
{
    Set_Class_Name("cBaseMillStdD");
    Set_Schema("cBaseMillStdD",sSchema);

    // Zero out member data
    Zero_Data(this, sizeof(cBaseMillStdD), Get_Schema("cBaseMillStdD"));
    force_mult = 1.0;
    feedback = false;
    slip = 1.0;

    //  DMSG(-diagLvl)<<"cBaseMillStdD()  created instance "
    //      <<(const char*)(objectName)
    //      << END_OF_MESSAGE ;

    // Create a rollbite object
//    pcRollbite = new cRollbite (objectName+MString("_rollbite"), ot_undef, op_undef, pHash, 0);
    pcRollbite = new cRollbite (objectName + (MString)"_rollbite", ot_undef, op_undef, pHash, 0);
    linkObj(pcRollbite, ot_undef, op_undef);

    if (NULL == pcFlowstress)
    {
        int index = 0;
        pcFlowstress = (cFlowstress *)Objhash.Find_Class_In_Hash(
                                    index,
                                    Objhash.cmnGetPtr(),
                                    MString("cFlowstress"));
        if ( pcFlowstress == NULL )
        {
            EMSG << "Could not find flowstress class in GLOBAL hash"
                 << END_OF_MESSAGE;
        }
    }

    //---------------------------------------------------------------
    //  Before it is used,  the cBaseMillStdD must be linked to a
    //  cStdRollPrD roll data object.  We cannot find in this this
    //  constructor however,  because at the point where this is
    //  called we cannot determine the static base stand name.
    //  The derived object (cBaseStdD or cBaseEdgD ) are responsible
    //  for finding and linking the cStdRollPrD before the objects
    //  are used.
    //----------------------------------------------------------------

} // end cBaseMillStdD::cBaseMillStdD

//---------------------------------------------------------------------
// Virtual function to allow the user to carry out post processing for
// a dynamic stand.
//---------------------------------------------------------------------
bool cBaseMillStdD::Post_Config(
                char *name,         // unused
                void *psStruct)     // unused
{
    EMSG<<"Post_Config(): called for "
        <<(const char*)(this->objName())
        << END_OF_MESSAGE ;

    pcEnBasePceD = (cBasePceD *)(previous_obj);
    return true;
}


//-------------------------------------------------------------------------
// ~cBaseMillStdD ABSTRACT:
//   Stand deconstructor
//-------------------------------------------------------------------------
cBaseMillStdD::~cBaseMillStdD()
{
    pcBaseMillStd      = 0;
    pcEnBasePceD   = 0;
    pcExBasePceD   = 0;
    pcStdRollPrD  = 0;
    pcRollbite = 0;
}
 
void cBaseMillStdD::zeroData(void)
{
    // Zero out member data  to a valid initial value
    Zero_Data(this, sizeof(cBaseMillStdD), Get_Schema("cBaseMillStdD::cBaseMillStdD"));

    // Initialize local values;
    force_mult = 1.0;
    feedback   = false;
    slip       = 1.0;
    draft_comp = 1.0;

    // Zero data in the cBaseCorr class
    cBaseCorrD::zeroData();
}


//-------------------------------------------------------------------------
// LINKOBJ ABSTRACT
//   Stand link to dynamic sub-objects
//   Note:  static cBaseMillStd is intended to be linked as a child
//          of this dynamic cBaseMillStdD
//-------------------------------------------------------------------------
bool cBaseMillStdD::linkObj( const void       *pVoid, 
                        const objTypEnum objType,
                        const objPosEnum objPos )
{
    cBase   *pcBase = (cBase *)(pVoid);
    bool retValue     (TRUE);
    bool retValueBase (TRUE);
    char    class_name[32];

    if (!pcBase)
    {
        EMSG << "Passed child pointer is NULL - exiting" << END_OF_MESSAGE;
        return (retValue);
    }

    // Perform linkObj on next object up in hierarchy
    retValueBase = cBase::linkObj(pVoid, objType, objPos);

    // Get the class name and link base stand objects to the base stand and 
    // create links for parent-child relationship
    sprintf( class_name, "%s", (const char *)(pcBase->Get_Class_Name()) );

    if ( 0 == _stricmp("cRollPairD", class_name) )
    {
        /*
        if (op_work == objPos)
        {
            if (pcWRPairD)
            {
                pcWRPairD->state = ((cRollPairD*) pVoid)->state;
                retValue = FALSE;
            }
            else
            {
                pcWRPairD = (cRollPairD*) pVoid;
            }
        }
        */
        EMSG<<"linkObj()  cannot link RollPairD to cBaseMillStdD"
            << END_OF_MESSAGE ;

    }
    else if ( 0 == _stricmp("cStdRollPrD", class_name ))
    {
        if ( NULL != this->pcStdRollPrD )
        {
            EMSG<<"linkObj(), replace existing pcStdRollPrD reference on "
                <<(const char*)(this->objName())
                << END_OF_MESSAGE ;
        }

        this->pcStdRollPrD = (cStdRollPrD*) pVoid ;

    }
    else if ( Is_Base_Class("cBasePceD", pcBase) )
    {
        if (op_entry == objPos)
        {
            if (pcEnBasePceD)
            {
                pcEnBasePceD = (cBasePceD*) pVoid;
                retValue =FALSE;
            }
            else
            {
                pcEnBasePceD = (cBasePceD*) pVoid;
            }
        }
        else if (op_exit == objPos)
        {
            if (pcExBasePceD)
            {
                pcExBasePceD = (cBasePceD*) pVoid;
                retValue =FALSE;
            }
            else
            {
                pcExBasePceD = (cBasePceD*) pVoid;
            }
        }
    }
    else if ( Is_Base_Class("cBaseMillStd", pcBase) )
    {
        if (pcBaseMillStd)
        {
            pcBaseMillStd = (cBaseMillStd*) pVoid;
            retValue =FALSE;
        }
        else
        {
            pcBaseMillStd = (cBaseMillStd*) pVoid;
        }
    }

    return retValueBase && retValue;

} // end cBaseMillStdD::linkObj()

//---------------------------------------------------------------------------
// ASSIGN_STATE ABSTRACT:
//  Assigns state data from a source object to a destination object of
//  the same type.
//---------------------------------------------------------------------------
bool  cBaseMillStdD::Assign_State(cBase * pcDest, cBase * pcSource)
{
    //-----------------------------------------------------------------------
    // Check pointers.  Alarm and abort if source or destination pointer is
    //      NULL.
    //-----------------------------------------------------------------------
    if ( pcDest == NULL )
    {
        EMSG << "NULL pointer in pcDest"
             << END_OF_MESSAGE;
        return false;
    }
    if ( pcSource == NULL )
    {
        EMSG << "NULL pointer in pcSource"
             << END_OF_MESSAGE;
        return false;
    }
    //-----------------------------------------------------------------------
    // Check object types.  Alarm and abort if the source and destination 
    //      objects are not identical.
    //-----------------------------------------------------------------------
    if ( pcDest->Get_Class_Name() != pcSource->Get_Class_Name() )
    {
        EMSG << "Cannot assign " << (const char *)pcSource->objName()
             << " to " << (const char *)pcDest->objName()
             << ".  Assignment aborted."
             << END_OF_MESSAGE;
        return false;
    }
    //-----------------------------------------------------------------------
    // Assign source to destination object.
    //-----------------------------------------------------------------------
    *((cBaseMillStdD *)(pcDest)) = *((cBaseMillStdD *)(pcSource));
    return true;    
    
}   // end cBaseMillStdD::Assign_State()

//-------------------------------------------------------------------------
// cBaseMillStdD ABSTRACT:
//   Stand copy constructor
//-------------------------------------------------------------------------
cBaseMillStdD::cBaseMillStdD (const cBaseMillStdD& source)
    : cBaseCorrD( source )
    , num       (source.num)
    , stdNum    (source.stdNum)
    , pcEnBasePceD   (0)
    , pcExBasePceD   (0)
    , pcStdRollPrD  (0)
    , pcRollbite (0)
{
    if (source.pcEnBasePceD) pcEnBasePceD = new cBasePceD(*(source.pcEnBasePceD));
    if (source.pcExBasePceD) pcExBasePceD = new cBasePceD(*(source.pcExBasePceD));
    if (source.pcStdRollPrD) pcStdRollPrD = new cStdRollPrD(*(source.pcStdRollPrD));
    if (source.pcRollbite) pcRollbite = new cRollbite(*(source.pcRollbite));

}

//-------------------------------------------------------------------------
// OPERATOR = ABSTRACT:
//   Stand assignment operator
//-------------------------------------------------------------------------
cBaseMillStdD& cBaseMillStdD::operator= (const cBaseMillStdD& source)
{
    if (this != &source)
    {
        cBase::operator=(source);
        Copy_Data(this,(void *)&source,sizeof(cBaseMillStdD),cBaseMillStdD_schema);
        if ( NULL != source.pcRollbite )
        {
            *pcRollbite = *(source.pcRollbite);
        }
    }
    return (*this);
}

//-------------------------------------------------------------------------
//   Std dump contents of the struct.
// The boolean composed if true indicates that the
// object should call the dump function for the 
// objects that it contains.
//-------------------------------------------------------------------------
void cBaseMillStdD::dump(const bool composed)
{
    Dump_Data(stdout, "cBaseMillStdD", this, 0, (const char *)objName());

    if (composed)
    {
        if (pcEnBasePceD)  pcEnBasePceD->dump(composed);    // entry piece
        if (pcExBasePceD)  pcExBasePceD->dump(composed);    // exit piece
        if (pcStdRollPrD) pcStdRollPrD->dump(composed);   // work roll pair
        if (pcRollbite) pcRollbite->dump(composed); // roll bite
    }
}// end cBaseMillStdD::dump


//-------------------------------------------------------------------------
// cBaseMillStdD::Operate ABSTRACT:
//
// Given entry piece data and other data supplied by external sources 
// (ex: draft), this method calculates relevant dynamic stand values and 
// updates the exit piece state.
//
// Return "true" if successfully completed.
//
// Return "false" if an error occurs.  In this case the exit piece state is
// undefined.
//
// Any exceptions are caught, alarmed and then thrown to a higher level
// exception handler.  The exit piece state is undefined if an exception 
// is thrown.
//-------------------------------------------------------------------------
bool cBaseMillStdD::Operate(void)
{

    edger = Is_Base_Class("cBaseEdgD", this);

    return true;

}

//-------------------------------------------------------------------------
// TENSION ABSTRACT:
// Calculate the tension applied to the exit piece.  The user must supply
// a function on the derived stand to calculate something other than zero.
//-------------------------------------------------------------------------
float cBaseMillStdD::Tension(void)
{
    return 0.0;
}

//-------------------------------------------------------------------------
// FLOWSTRESS ABSTRACT:
// Calculate the mean flowstress in the rollbite.  The user must supply
// a function on the derived stand to calculate something other than zero. 
//-------------------------------------------------------------------------
float cBaseMillStdD::Flowstress(void)
{
    EMSG << "Need to provide a flowstress implementation"
         << END_OF_MESSAGE;
    return 0.0;
}

//-------------------------------------------------------------------------
// SPEED ABSTRACT:
// Calculate the roll peripheral speed and motor rpm.  The user may
// override this on the derived stand to suite his specific purposes.
//-------------------------------------------------------------------------
void    cBaseMillStdD::Speed(void)
{
    float   approx_slip = 0.0;

    // ---------------------------------------------------------------
    // Set the required piece input data for
    // the roll pressure distribution calculations.
    // ---------------------------------------------------------------
    if ( pcRollbite->Force_Valid() && (slip != 0.0) )
    {
        // use last calculated slip
        approx_slip = slip;
    }
    else
    {
        // first time, use approximation for slip
        approx_slip =  
            pcRollbite->Approximate_Slip(
            pcStdRollPrD->getAvgDiam(),      // roll diameter
            pcExBasePceD->thick,             // exit thickness
            pcEnBasePceD->thick);            // entry thickness
    }

    // Calculate roll peripheral speed
    // only in setup mode. In feedback mode
    // speed will be initialized to measured 
    // roll speed
    if ( false == feedback )
    {
        speed = pcEnBasePceD->pcBasePce->mass_flow  /
                (pcExBasePceD->thick * approx_slip);
    }

    // Calculate motor rpm for power to torque conversions
    rpm = Physcon.mmpm_inpft * speed * 
              pcBaseMillStd->gearat * Physcon.secpmin /
              (Physcon.pi * 
               pcStdRollPrD->getAvgDiam() *
               Physcon.vel_time);

} // End cBaseMillStdD::Speed()


//-------------------------------------------------------------------------
// VOLUME_FLOW ABSTRACT:
// Calculate the volume flow.  The user may override this on the derived
// stand to suite his specific purposes.
//-------------------------------------------------------------------------
float   cBaseMillStdD::Volume_Flow()
{
    float volume_flow = pcEnBasePceD->pcBasePce->mass_flow * 
                        pcEnBasePceD->width *
                        Physcon.mmpm_inpft / Physcon.vel_time;

    return volume_flow;

} // End cBaseMillStdD::Volume_Flow()



//-------------------------------------------------------------------------
// GAP ABSTRACT:
// Calculate the roll gap.  The user must supply a function on the derived
// stand to calculate something other than zero.
//-------------------------------------------------------------------------
float   cBaseMillStdD::Gap(void)
{
    EMSG << "Need to provide a gap implementation"
         << END_OF_MESSAGE;
    return 0.0;
}


//-------------------------------------------------------------------------
// DRAFT_COMP():
// Calculate draft compensation.  Roll peripheral speed multiplied by
// draft compensation is equal to entry speed.
//-------------------------------------------------------------------------
float   cBaseMillStdD::Draft_Comp(void)
{

    return pcRollbite->Draft_Comp();

} // End cBaseMillStdD::Draft_Comp()



//---------------------------------------------------------------------
// Initial_Exit_Dimensions() ABSTRACT:
//
// Calculate piece exit dimensions from entry side dimensions and
// draft.
//---------------------------------------------------------------------
bool    cBaseMillStdD::Initial_Exit_Dimensions(void)
{
    return false;
}


//-------------------------------------------------------------
// Force_Torque_Power() ABSTRACT:
//
// Calculate Force, Torque and Power.
//-------------------------------------------------------------
bool    cBaseMillStdD::Force_Torque_Power(void)
{
    return false;
}


//---------------------------------------------------------------
// Update_RollBite_Quantities() ABSTRACT
//
// Update rollbite quantities on the stand
//      Volume flow
//      Arc of contact
//      Draft compensation
//      Slip
//      Roll on roll force
//      Strip force
//      Bearing power loss
//      Deformation power
//      Friction power
//      Reduction power
//      Tension power
//      Power torque - Torque calculated from power & speed
//      Shaft power
//      Torque
//      Per unit torque
//---------------------------------------------------------------
bool    cBaseMillStdD::Update_RollBite_Quantities(void)
{
    return false;

} // end Update_RollBite_Quantities()


//-----------------------------------------------------------------
// Draft_From_Torque() ABSTRACT
// Draft_Force_From_Torque() ABSTRACT
//
// Calculate the draft required to obtain the given torque.  Makes
// use of the state of rollbite object already on the edger, plus
// the dynamic edger state itself.  Since the torque we are using
// is calculated from shaft power, we cannot directly call a method
// on rollbite to resolve the draft for the torque.  We provide
// logic here similar to that on rollbite, which resolves the
// torque calculated by integrating moments across the rollbite.
//-----------------------------------------------------------------

//-----------------------------------------------------------------
// Static function to return a value of torque, given a draft.
//-----------------------------------------------------------------
static  bool   FN_Calculate_Torque(float &z, void *ptr, float x, float y)
{
    cBaseMillStdD   *pcBaseMillStdD = (cBaseMillStdD *)(ptr);

    // set the new draft
    pcBaseMillStdD->draft = x;

    // operate on the edger
    pcBaseMillStdD->Operate();

    // return the force
    z = pcBaseMillStdD->torque;

    return true;

} // end FN_Calculate_Torque()


bool    cBaseMillStdD::Draft_Force_From_Torque(
                      float& draft,             // OUT [mm_in] calculated new draft
                      float& force,             // OUT [mton_eton_KN] calculated new force
                      float torque_desired,     // IN [kgm_ftlb_Nm] desired torque
                      float draft_low,          // IN [mm_in] low draft bracket
                      float draft_hi)           // IN [mm_in] high draft bracket
{
    // get the draft to satisfy the desired torque
    draft = this->Draft_From_Torque(
                            torque_desired,
                            draft_low,
                            draft_hi);

    // calculate the force
    if ( edger )
    {
        force = this->pcRollbite->Force() *
                      this->pcEnBasePceD->thick *
                      force_mult;
    }
    else
    {
        force = this->pcRollbite->Force() *
                      this->pcEnBasePceD->width *
                      force_mult;
    }
    return true;
}

float   cBaseMillStdD::Draft_From_Torque(
                      float torque_desired,     // [kgm_ftlb_Nm] desired torque
                      float draft_lo,           // [mm_in] low draft bracket
                      float draft_hi)           // [mm_in] high draft bracket
{
    cMathUty::status_type   cmstatus;
    float                   desired_draft;
    float                   save_draft = this->draft;
    float                   xacc;
    float                   zacc;

    //-----------------------------------------------------------------
    // Make sure that we have valid power calculations on rollbite.
    //-----------------------------------------------------------------
    if ( !pcRollbite->Pwr_Valid() )
    {
        EMSG << (const char*)objName() << ": Draft_From_Torque(): Rollbite power is invalid"
             << END_OF_MESSAGE;
        return 0.0;
    }

    //-----------------------------------------------------------------
    // Work out the X & Z accuracy that can be achieved.
    //-----------------------------------------------------------------
    xacc = pcRollbite->Precision()*draft_max/10.0;
    zacc = pcRollbite->Precision()*torque_max/10.0;

    //-----------------------------------------------------------------
    // Call X_For_YZ to solve F(X,Y) - Z = 0.
    //-----------------------------------------------------------------
    cmstatus = cMathUty::X_For_YZ_NR(       // returns calculation status
                        desired_draft,      // OUT desired X, ( draft )
                        FN_Calculate_Torque,// IN  pointer to caller's function
                        (void *)(this),     // IN  void ptr, for use by function
                        0.0,                // IN  Y, not used here
                        torque_desired,     // IN  Z, desired torque
                        draft_lo,           // IN  X low, draft low
                        draft_hi,           // IN  X high, draft high
                        xacc,               // IN  absolute accuracy for draft
                        zacc);              // IN  absolute accuracy for torque

    // restore the draft on the stand
    this->draft = save_draft;

    // restore the stand state
    this->Operate();

    //-----------------------------------------------------------------
    // Check return status and return to caller.
    //-----------------------------------------------------------------
    if ( cmstatus != cMathUty::cmuty_valid )
    {
        EMSG << "Draft_From_Torque: " << cMathUty::Image(cmstatus) << " in cMathUty::X_for_YZ_NR"
             << END_OF_MESSAGE;
        return 0.0;
    }

    return desired_draft;

} // End cBaseMillStdD::Draft_From_Torque()




//-----------------------------------------------------------------
// Draft_From_Force() ABSTRACT
//
// Calculate the draft required to obtain the given force.
//-----------------------------------------------------------------

//-----------------------------------------------------------------
// Static function to return a value of force, given a draft.
//-----------------------------------------------------------------
static  bool   FN_Calculate_Force(float &z, void *ptr, float x, float y)
{
    bool    do_limchks = false;

    cBaseMillStdD   *pcBaseMillStdD = (cBaseMillStdD *)(ptr);

    // set the new draft
    pcBaseMillStdD->draft = x;

    // operate on the stand
    bool save_do_limchks = do_limchks;
    do_limchks = false;
    pcBaseMillStdD->Operate();
    do_limchks = save_do_limchks;

    // return the force
    z = pcBaseMillStdD->force_strip;

    return true;

} // end FN_Calculate_Force()


float   cBaseMillStdD::Draft_From_Force(
                      float force_desired)
{
    bool                    do_limchks = false;

    float                   save_draft = this->draft;
    cMathUty::status_type   cmstatus;
    float                   desired_draft;
    float                   draft_lo;
    float                   draft_hi;
    float                   xacc;
    float                   zacc;

    //-----------------------------------------------------------------
    // Make sure that we have valid force calculations on rollbite.
    //-----------------------------------------------------------------
    if ( !pcRollbite->Force_Valid() )
    {
        EMSG << "Draft_From_Force(): Rollbite force is invalid"
             << END_OF_MESSAGE;
        return 0.0;
    }

    //-----------------------------------------------------------------
    // Make sure that we have a valid draft.
    //-----------------------------------------------------------------
    if ( draft <= 0.0 )
    {
        EMSG << "Draft_From_Force(): Draft is invalid"
             << END_OF_MESSAGE;
        return 0.0;
    }

    //-----------------------------------------------------------------
    // Compute draft lo & hi boundaries for X_For_YZ_NR.  Note that these
    // boundaries are not hard boundaries, but are just used to calculate
    // the initial starting guess for the draft.  Ensure however that the
    // boundaries result in a valid exit thickness.
    //-----------------------------------------------------------------
    draft_lo = draft - draft / 10.0; 
    draft_hi = draft + draft / 10.0;  

    //-----------------------------------------------------------------
    // Work out the X & Z accuracy that can be achieved.
    //-----------------------------------------------------------------
    xacc = pcRollbite->Precision()*draft_max/10.0;
    zacc = pcRollbite->Precision()*force_max/10.0;

    //-----------------------------------------------------------------
    // Call X_For_YZ to solve F(X,Y) - Z = 0.
    //-----------------------------------------------------------------
    cmstatus = cMathUty::X_For_YZ_NR(       // returns calculation status
                        desired_draft,      // OUT desired X, ( draft )
                        FN_Calculate_Force, // IN  pointer to caller's function
                        (void *)(this),     // IN  void ptr, for use by function
                        0.0,                // IN  Y, not used here
                        force_desired,      // IN  Z, desired force
                        draft_lo,           // IN  X low, draft low
                        draft_hi,           // IN  X high, draft high
                        xacc,               // IN  absolute accuracy for draft
                        zacc);              // IN  absolute accuracy for force

    // restore the draft on the stand
    this->draft = save_draft;

    // restore the stand state
    bool save_do_limchks = do_limchks;
    do_limchks = false;
    this->Operate();
    do_limchks = save_do_limchks;

    //-----------------------------------------------------------------
    // Check return status and return to caller.
    //-----------------------------------------------------------------
    if ( cmstatus != cMathUty::cmuty_valid )
    {
        EMSG << "Error " << cMathUty::Image(cmstatus) << " in cMathUty::X_for_YZ_NR"
             << END_OF_MESSAGE;
        return 0.0;
    }

    return desired_draft;

} // End cBaseMillStdD::Draft_From_Force()



