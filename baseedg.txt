//-----------------------------------------------------------------------------
//
// ABSTRACT:
//     This file defines the static and dynamic edger object methods.
//
//
//     FUNCTION/PROCEDURE/TASK  DESCRIPTION
//     -----------------------  -----------------------------------------------
//   static
//     cBaseEdg                 Edger object constructor
//     cBaseEdg                 Edger object copy constructor
//     cBaseEdg                 Edger object destructor
//     operator=        assignment operator
//     operator==               equivalence operator
//     operator<                less than operator
//     linkObj                  Method for linking edger to other objects
//     dump                     dump member data and/or composed objects
//     data                     reference of sBaseEdg struct
//     pcWRPair                 const pointer to the WRPair
//     pcMtr                    const pointer to the Mtr
//   dynamic
//     cBaseEdgD                Edger object constructor
//     cBaseEdgD                Edger object copy constructor
//     ~cBaseEdgD               Edger object destructor
//     operator=        assignment operator
//     operator==               equivalence operator
//     operator<                less than operator
//     linkObj                  Method for linking edger to other objects
//     DfDwx                    Calculate dfrc_ddraft, the strip modulus.  
//                              dfrc_ddraft is updated on the edger and also
//                              returned as a float.
//     DfDt                     Calculate dfrc_dtmp on the edger and also
//                              returned as a float.
//
//-----------------------------------------------------------------------------
//------------------------------
// C++ standard library includes
//------------------------------
#include <stdlib.h>

#include "objhash.hxx"
#include "mathuty.hxx"
#include "mill.hxx"
#include "mtr.hxx"
#include "matl.hxx"
#include "physcon.hxx"
#include "basepce.hxx"
#include "baseedg.hxx"
#include "width.hxx"
#include "rollbite.hxx"
#include "utility.hxx"
#include "stdrollpr.hxx"
#include "rapp.hxx"  //add by mjh 20150319

#ifdef WIN32
    #ifdef _DEBUG
    #define new DEBUG_NEW
    #endif
    #pragma warning(disable: 4244) // double to float conversion (NT thinks constants are doubles)
#endif

// Data schema for the cBaseEdg class.
static cSchema::schema_type cBaseEdg_schema[]=
{
    //Next  Enum  Schema details                            Fmt  Units        Comment
    //====  ====  ========================================  ==== ===========  ==================================================
    LINK_TO_SCHEMA("cBaseMillStd","cBaseMillStd")
    { NULL, NULL, SCHEMA_T(cBaseEdg,float,angle),           "",  "deg",           "taper or groove angle" },
    { NULL, NULL, SCHEMA_T(cBaseEdg,bool,tapered),          "",  "",              "tapered edger indicator" },
    { NULL, NULL, SCHEMA_T(cBaseEdg,bool,grooved),          "",  "",              "grooved edger indicator" },
    { NULL, NULL, SCHEMA_T(cBaseEdg,float,throat),          "",  "mm_in",         "working part of edger roll" },
    { NULL, NULL, SCHEMA_T(cBaseEdg,float,diam_max),        "",  "mm_in",         "max diameter of grooved-edger" },
    { NULL, NULL, SCHEMA_T(cBaseEdg,float,diam_min),        "",  "mm_in",         "min diameter of grooved-edger" },
    { NULL, NULL, SCHEMA_T(cBaseEdg,float,ddog_mlt),        "",  "",              "double dog bone multiplier" },

    { 0 }   // terminate list
};

// Link all the schema's together
cSchema::schema_name_type cBaseEdg::sSchema[]=
{
    { 
        "cBaseEdg",                         //name
        sizeof(cBaseEdg),                   // size
        cBaseEdg_schema,                    // schema
        false,                              // packed
        true,                               // allow ptr
        false,                              // Read only
        "Static BaseEdg configuration",     // comment
        0                                   // offset to config data
    },

    { 0 } // terminate list
};

// Data schema for the cBaseEdgD class.
static cSchema::schema_type cBaseEdgD_schema[]=
{
    //Next  Enum  Schema details                            Fmt  Units        Comment
    //====  ====  ========================================  ==== ===========  ==================================================
    LINK_TO_SCHEMA("cBaseMillStdD","cBaseMillStdD")

    { NULL, NULL, SCHEMA_T(cBaseEdgD,float,dfrc_ddraft),    "",  "mton/mm_eton/in_kN/mm",     "transfer function [delta force / delta edger draft" },
    { NULL, NULL, SCHEMA_T(cBaseEdgD,float,dfrc_dtmp),      "",  "mton/oC_eton/oF_kN/oC",     "transfer function [delta force / delta temperature" },
    { NULL, NULL, SCHEMA_T(cBaseEdgD,float,load_op),        "",  "",          "per unit load distribution - from operator" },
    { NULL, NULL, SCHEMA_T(cBaseEdgD,float,load_map),       "",  "",          "per unit load distribution - from map" },
    { NULL, NULL, SCHEMA_T(cBaseEdgD,float,load_eml),       "",  "",          "per unit load distribution - from early, mid, late" },
    { NULL, NULL, SCHEMA_T(cBaseEdgD,float,groov_mult),     "",  "",          "grooved edger multiplier" },
    { NULL, NULL, SCHEMA_T(cBaseEdgD,float,effi),           "",  "",          "edging efficiency" },
    { NULL, NULL, SCHEMA_T(cBaseEdgD,float,wid_err),        "",  "mm_in",     "width error [draft * effi]" },
    { NULL, cBaseEdgD::edgLimEnum_Fmt, SCHEMA_T(cBaseEdgD,edgLimEnum,oplim), "",  "", "initial (before adjusting drafts) limit reason" },
    { NULL, cBaseEdgD::edgLimEnum_Fmt, SCHEMA_T(cBaseEdgD,edgLimEnum,lim),   "",  "", "limit reason" },
    { NULL, NULL, SCHEMA_T(cBaseEdgD,float,recov),          "",  "mm_in",     "recovery due to this edging operation" },
    { NULL, NULL, SCHEMA_T(cBaseEdgD,bool,exit_side),       "",  "",          "true if this edger is on the exit side of the stand or 2nd edger in pass" },
    { NULL, NULL, SCHEMA_T(cBaseEdgD,bool,do_limchks),      "",  "",          "if true, perform limit checks as part of operate" },

    { NULL, NULL, SCHEMA_T(cBaseEdgD,float,force_meas),     "",  "mton_eton_KN","measured force" },

    { NULL, NULL, SCHEMA_PO(cBaseEdgD,cBaseEdg,pcBaseEdg),  "",  "",          "pointer to static base edger object" },

    { NULL, NULL, SCHEMA_T(cBaseEdgD,int,stdNum),           "",  "",          "Edge stand number" },

    { 0 }   // terminate list

};

// Link all the schema's together
cSchema::schema_name_type cBaseEdgD::sSchema[]=
{
    { 
        "cBaseEdgD",                        // name
        sizeof(cBaseEdgD),                  // size
        cBaseEdgD_schema,                   // schema
        false,                              // packed
        true,                               // allow ptr
        false,                              // Read only
        "Dynamic stand data",               // comment
        0                                   // offset to config data
    },

    { 0 } // terminate list
};


// Diagnostic level specific to this file
//static const cGlobal::DiagnosticCodeEnum diagLvl(cGlobal::BaseEdg);
static const int diagLvl = 0;

#define T(x) #x
    const char* edgLimImage[]   = { EDGLIM_LIST , "\0"};   // enumerated edger limit reason
#undef T

// Edger limit reasons statements.  These must be in the same order as the
// EDGLIM_LIST.  "Reasons" CANNOT be any longer than 50 characters including
// spaces and punctuation.
const char* edgLimReasons[(int)el_guardvalue+1] = {
    "NOT defined",
    "NO limits encountered",
    "At maximum roll bite angle",
    "At strip edger buckling limit",
    "At motor current limit",
    "At minimum draft limit",
    "At maximum draft limit",
    "At maximum gap limit",
    "Draft set to a fixed value",
    "At speed limit",
    "At minimum force limit",
    "At maximum force limit",
    "At minimum power limit",
    "At maximum power limit",
    "At motor torque limit",
    "At BOTH High Force and Low Draft limit",
    "At tapered edger draft limit",
    "INVALID - Guard value" };

//-----------------------------------------------------------------------------
// Function : findEdgLimEnum
// This function returns the index of the "image"
//-----------------------------------------------------------------------------
int findEdgLimEnum (const char* name)
{
    for (int i = 0; strcmp(edgLimImage[i], "\0"); ++i)
        if (!strcmp(name, edgLimImage[i]))
        {
        return i;
        }
    return (255);
} 

//----------------------------------------------------------
// cBaseEdgD::edgLimEnum_Fmt - Must be static
//    dir
//      FMT_ENCODE    encode the binary structure data to an ASCII string
//      FMT_DECODE    decode the ASCII string to binary structure data
//      FMT_MIN_ENUM  Put min value of enum into p
//      FMT_MAX_ENUM  Put max value of enum into p
//----------------------------------------------------------
void    cBaseEdgD::edgLimEnum_Fmt(
                int dir,                // 0=encode, 1=decode
                void *p,                // pointer to structure data member
                cSchema::schema_type *schema,  // pointer to schema
                char *buff)             // pointer to ASCII character buffer
{
    switch (dir)
    {
      case FMT_ENCODE:   // encode the binary structure data to an ASCII string
      {
        // encode the binary structure data to an ASCII string
        const   char    *side;

        side = Image(*((edgLimEnum *)(p)));
        strcpy(buff, side);
        break;
      }
      case FMT_DECODE:   // decode the ASCII string to binary structure data
        // decode the ASCII string to binary structure data
        *((edgLimEnum *)(p)) = (edgLimEnum)findEdgLimEnum(buff);
        break;
      case FMT_MIN_ENUM: // Put min value of enum into p
          *((edgLimEnum *)(p)) = el_undef;
        break;
      case FMT_MAX_ENUM: // Put min value of enum into p
        *((edgLimEnum *)(p)) = el_guardvalue;
        break;
      default:
          EMSG << " dir out of range " << END_OF_MESSAGE; 
    }
}

//-----------------------------------------------------------------------------
// Static Edger Object
//-----------------------------------------------------------------------------
//-----------------------------------------------------------------------------
// CBASEEDG CONSTRUCTOR ABSTRACT:
//   Edger constructor
//-----------------------------------------------------------------------------
int cBaseEdg::count   (0); // initialize static members
cBaseEdg::cBaseEdg()
    : cBaseMillStd()
{
    Set_Class_Name("cBaseEdg");
    Set_Schema("cBaseEdg",sSchema);

    // Zero out member data
    zeroData();

}

// Use this constructor if hash table support required
cBaseEdg::cBaseEdg( const MString        &objectName, 
                    const objTypEnum    objType, 
                    const objPosEnum    position, 
                    void                *pHash)
    : cBaseMillStd( objectName, objType, position, pHash )
    , num           (++count)
{
    int save_num = num;

    Set_Class_Name("cBaseEdg");
    Set_Schema("cBaseEdg",sSchema);

    // Zero out member data
    zeroData();

    num = save_num;
}

//-------------------------------------------------------------------------
// ~CBaseEdg ABSTRACT:
//   Edger deconstructor
//-------------------------------------------------------------------------
cBaseEdg::~cBaseEdg()
{

}

void cBaseEdg::zeroData(void)
{
//******** Needs a mutex (semaphore) to be thread safe ********

    // Zero out member data  to a valid initial value
    Zero_Data(this, sizeof(cBaseEdg), Get_Schema("cBaseEdg::cBaseEdg"));
}

//-------------------------------------------------------------------------
// CBaseEdg ABSTRACT:
//   Edger copy constructor
//-------------------------------------------------------------------------
cBaseEdg::cBaseEdg (const cBaseEdg& source)
    : cBaseMillStd( source )
    , num       (source.num)
{

}

//-------------------------------------------------------------------------
// OPERATOR = ABSTRACT:
//   Edger assignment operator
//-------------------------------------------------------------------------
cBaseEdg& cBaseEdg::operator= (const cBaseEdg& source)
{
    if (this != &source)
    {
        cBaseMillStd::operator=(source);

        num  = source.num;

        Copy_Data(this, (void *)&source,sizeof(cBaseEdg),cBaseEdg_schema);
    }
    return (*this);
}

//-------------------------------------------------------------------------
// LINKOBJ ABSTRACT
//   Edger link to static sub-objects
//-------------------------------------------------------------------------
bool cBaseEdg::linkObj(const void        *pVoid, 
                          const objTypEnum  objType,
                          const objPosEnum  objPos )
{
    cBase   *pcBase = (cBase *)(pVoid);
    bool retValue     (TRUE);

    if (!pcBase)
    {
        EMSG << "Passed child pointer is NULL - exiting" << END_OF_MESSAGE;
        return (retValue);
    }

    // Perform linkObj on next object up in hierarchy
    return cBaseMillStd::linkObj(pVoid, objType, objPos);

} // end cBaseEdg::linkObj()

//------------------------------------------------------------
//  getEdg() locates the static base edger object in the list
//  of lists on the cMill object, and returns a pointer to it.
//-------------------------------------------------------------
cBaseEdg* cBaseEdg::getBaseEdg( char * pEdgName, bool alarmit ) 
{   
    MString key="cEdg" ;
    cListDMDS* pcList = (cListDMDS*) mill().lChildren.find(key);
    if ( NULL == pcList )
    {   if ( alarmit )
        {
            EMSG<<"List mill().lChildren does not contain a list with key "
                <<key
                <<END_OF_MESSAGE ;
        }
        return NULL ;
    }
    char stdName[nameSize32];
    strncpy( stdName, pEdgName, (nameSize32-1)) ;
    stdName[nameSize32-1]='\0' ;
    key = stdName ;
    cBaseEdg* pcBaseEdg = NULL  ;
    pcBaseEdg = (cBaseEdg*) pcList->find(key) ;
    if ( NULL == pcBaseEdg )
    {   if ( alarmit )
        {   EMSG<<"getBaseEdg(): could not find edger "<<key
                <<END_OF_MESSAGE ;
        }
    }
    return pcBaseEdg;
}


cBaseEdg* cBaseEdg::getBaseEdg( int stdNum , bool alarmit ) 
{   MString key="cEdg" ;
    cListDMDS* pcList = (cListDMDS*) mill().lChildren.find(key);
    if ( NULL == pcList )
    {   // Should always alarm this one. It is an error.
        EMSG<<"getBaseEdg():List mill().lChildren does not contain a list with key "
            <<key
            <<END_OF_MESSAGE ;
        return NULL ;
    }
    if ( stdNum < 1 )
    {   if ( alarmit )
        {   EMSG<<"getBaseEdg() called with invalid edger number "
                <<stdNum
                <<END_OF_MESSAGE ;
        }
        return NULL ;
    }
    //  Walk the list to find a stand with matching stand number.
    cListDMDS::sListData*  qpos = pcList->setStart() ;
    cBaseEdg* pcBaseEdg = NULL ;
    while ( (pcBaseEdg=(cBaseEdg*)pcList->getNext(qpos)) != NULL )
    {   if ( stdNum == ((cBaseEdg*)pcBaseEdg)->num )
        {   return pcBaseEdg ;
        }
    }
    if ( alarmit )
    {
        EMSG<<"getBaseEdg(): could not find static cEdg number "
            <<stdNum
            <<END_OF_MESSAGE ;
    }
    return NULL ;
}

//-------------------------------------------------------------------------
// DUMP ABSTRACT:
//   BaseEdg dump contents of the struct.
// The boolean composed if true indicates that the
// object should call the dump function for the 
// objects that it contains.
//-------------------------------------------------------------------------
void cBaseEdg::dump(const bool composed)
{
    Dump_Data(stdout, "cBaseEdg", this, 0, (const char *)objName());

    if (composed)
    {
        if (pcMtr)
            pcMtr->dump(composed);
    }

} // end cBaseEdg::dump


//-----------------------------------------------------------------------------
// Dynamic Edger Object
//-----------------------------------------------------------------------------
//-----------------------------------------------------------------------------
// CBASEEDGD CONSTRUCTOR ABSTRACT:
//   Edger constructor
//-----------------------------------------------------------------------------
int cBaseEdgD::count  (0); // initialize static members
cBaseEdgD::cBaseEdgD()
    : cBaseMillStdD()
    , pcBaseEdg     (NULL)
{
    Set_Class_Name("cBaseEdgD");
    Set_Schema("cBaseEdgD",sSchema);

    // Zero out member data
    Zero_Data(this, sizeof(cBaseEdgD), Get_Schema("cBaseEdgD"));
    force_mult = 1.0;
    power_mult = 1.0;
    feedback   = false;
    slip       = 1.0;
    groov_mult = 1.0;
    do_limchks = true;

}

// Use this constructor if hash table support required
cBaseEdgD::cBaseEdgD( const MString       &objectName, 
                      const objTypEnum   BaseEdgType,
                      const objPosEnum   position,
                      void               *pHash)
    : cBaseMillStdD( objectName, BaseEdgType, position, pHash)
    , num           (++count)
    , pcBaseEdg     (NULL)
{
    int     save_num = num;
    Set_Class_Name("cBaseEdgD");
    Set_Schema("cBaseEdgD",sSchema);

    // Zero out member data
    Zero_Data(this, sizeof(cBaseEdgD), Get_Schema("cBaseEdgD"));
    num = save_num;
    force_mult = 1.0;
    power_mult = 1.0;
    feedback   = false;
    slip       = 1.0;
    groov_mult = 1.0;

    //----------------------------------------------------
    // First, Find the static edger object
    //----------------------------------------------------
    const char    *pName = strchr(objectName, '_') + 1;
    objHashType::ResultT    result;
    result = Objhash.cmnFind(pName);
    if (HashTableHlp::duplicate == result.status)
    {
        linkObj(result.data, ot_undef, op_undef);
        stdNum = pcBaseEdg->num;
    }
    else
    {
        EMSG << "cBaseEdgD(): No object named "
             << pName 
             << " exists"
             << END_OF_MESSAGE;
    }

    //-----------------------------------------------------------
    //  Second,  look in the current hash table for a stand roll
    //  data object with a name like  xyn_stdrollpr
    //  where "xyn" is the static base stand name.
    //-----------------------------------------------------------
    cStdRollPrD* pFound = cStdRollPrD::Find( (objHashType*)pHash, (char *)pName );
    if ( NULL == pFound )
    {
        EMSG<<"cBaseEdgD()  cannot find roll data object for "
            <<(const char*) objectName
            << END_OF_MESSAGE ;
    }
    else
    {
        this->linkObj( pFound, ot_undef, op_undef );
    }

} // end cBaseEdgD::cBaseEdgD

//-------------------------------------------------------------------------
// ~CBaseEdgD ABSTRACT:
//   Edger deconstructor
//-------------------------------------------------------------------------
cBaseEdgD::~cBaseEdgD()
{
    pcBaseEdg = NULL;
}
 
void cBaseEdgD::zeroData(void)
{
//******** Needs a mutex (semaphore) to be thread safe ********

    // Zero out member data  to a valid initial value
    Zero_Data(this, sizeof(cBaseEdgD), Get_Schema("cBaseEdgD::cBaseEdgD"));

    // Initialize local values;
    force_mult = 1.0;
    power_mult = 1.0;
    feedback   = false;
    slip       = 1.0;
    groov_mult = 1.0;

    // Zero cBaseCorr class data
    cBaseCorrD::zeroData();
}

//-------------------------------------------------------------------------
// LINKOBJ ABSTRACT
//   Edger link to dynamic sub-objects
//-------------------------------------------------------------------------
bool cBaseEdgD::linkObj( const void         *pVoid, 
                            const objTypEnum   objType,
                            const objPosEnum   objPos )
{
    cBase   *pcBase = (cBase *)(pVoid);
    bool retValue     (TRUE);
    bool retValueBase (TRUE);
    char    class_name[32];

    if (!pcBase)
    {
        EMSG << "Passed child pointer is NULL - exiting" << END_OF_MESSAGE;
        return (retValue);
    }

    // Perform linkObj on next object up in hierarchy
    // Specifically,  this call will link the cStdRollPrD to the
    // cBaseMillStdD object.

    retValueBase = cBaseMillStdD::linkObj(pVoid, objType, objPos);

    // Get the class name and link base stand objects to the base stand and 
    // create links for parent-child relationship
    sprintf( class_name, "%s", (const char *)(pcBase->Get_Class_Name()) );

    if ( Is_Base_Class("cBaseEdg", pcBase) )
    {
        if (pcBaseEdg)
        {
            retValue = false;
        }
        pcBaseEdg = (cBaseEdg*) pVoid;
    }

    return retValueBase && retValue;

} // end cBaseEdgD::linkObj()


//-------------------------------------------------------------------------
// CBaseEdgD ABSTRACT:
//   Edger copy constructor
//-------------------------------------------------------------------------
cBaseEdgD::cBaseEdgD (const cBaseEdgD& source)
    : cBaseMillStdD ( source )
    , num           ( source.num )
    , pcBaseEdg     ( source.pcBaseEdg ) 
    , stdNum        ( source.stdNum )
{
    if (source.pcStdRollPrD) pcStdRollPrD = new cStdRollPrD(*(source.pcStdRollPrD));
}

//---------------------------------------------------------------------------
// ASSIGN_STATE ABSTRACT:
//  Assigns state data from a source object to a destination object of
//  the same type.
//---------------------------------------------------------------------------
bool  cBaseEdgD::Assign_State(cBase * pcDest, cBase * pcSource)
{
    //-----------------------------------------------------------------------
    // Check pointers.  Alarm and abort if source or destination pointer is
    //      NULL.
    //-----------------------------------------------------------------------
    if ( pcDest == NULL )
    {
        EMSG << "NULL pointer in pcDest"
             << END_OF_MESSAGE;
        return false;
    }
    if ( pcSource == NULL )
    {
        EMSG << "NULL pointer in pcSource"
             << END_OF_MESSAGE;
        return false;
    }
    //-----------------------------------------------------------------------
    // Check object types.  Alarm and abort if the source and destination 
    //      objects are not identical.
    //-----------------------------------------------------------------------
    if ( pcDest->Get_Class_Name() != pcSource->Get_Class_Name() )
    {
        EMSG << "Cannot assign " << (const char *)pcSource->objName()
             << " to " << (const char *)pcDest->objName()
             << ".  Assignment aborted."
             << END_OF_MESSAGE;
        return false;
    }
    //-----------------------------------------------------------------------
    // Assign source to destination object.
    //-----------------------------------------------------------------------
    *((cBaseEdgD *)(pcDest)) = *((cBaseEdgD *)(pcSource));
    return true;    
    
}   // end cBaseEdgD::Assign_State()

//-------------------------------------------------------------------------
// OPERATOR = ABSTRACT:
//   Edger assignment operator
//-------------------------------------------------------------------------
cBaseEdgD& cBaseEdgD::operator= (const cBaseEdgD& source)
{
    if (this != &source)
    {
        cBaseMillStdD::operator=(source);
        Copy_Data(this,(void *)&source,sizeof(cBaseEdgD),cBaseEdgD_schema);
    }
    return (*this);
}


//-------------------------------------------------------------------------
//   BaseEdg dump contents of the struct.
// The boolean composed if true indicates that the
// object should call the dump function for the 
// objects that it contains.
//-------------------------------------------------------------------------
void cBaseEdgD::dump(const bool composed)
{
    Dump_Data(stdout, "cBaseEdgD", this, 0, (const char *)objName());

    if (composed)
    {
        if (pcStdRollPrD)
            pcStdRollPrD->dump(composed);   // roll pair(s)
    }
}// end cBaseEdgD::dump

//---------------------------------------------------------------------
// Virtual function to allow the user to carry out post processing for
// a dynamic Edger.
//---------------------------------------------------------------------
bool    cBaseEdgD::Post_Config(
                char *name,         // unused
                void *psStruct)     // unused
{
    pcEnBasePceD = (cBasePceD *)(previous_obj);
    return true;
}


//-------------------------------------------------------------------------
// cBaseEdgD::Operate ABSTRACT:						动态立棍轧机
//
// This method updates the exit piece state, given the entry state 
// information for a piece.
//
// Return "true" if the exit piece state is successfully updated.
//
// Return "false" if an error occurs.  In this case the exit piece state is
// undefined.
//
// Any exceptions are caught, alarmed and then thrown to a higher level
// exception handler.  The exit piece state is undefined if an exception 
// is thrown.
//-------------------------------------------------------------------------
bool cBaseEdgD::Operate(void)
{
    // Check consistency
    MDSVERIFYNAME(next_obj, "next_obj");
    MDSVERIFYNAME(previous_obj, "previous_obj");
    MDSVERIFYNAME(pcBaseEdg, "pcBaseEdg");
    MDSVERIFYNAME(pcEnBasePceD, "pcEnBasePceD");
    MDSVERIFYNAME(pcExBasePceD, "pcExBasePceD");
    MDSVERIFYNAME(pcEnBasePceD->pcBasePce, "pcEnBasePceD->pcBasePce");
    MDSVERIFYNAME(pcExBasePceD->pcBasePce, "pcExBasePceD->pcBasePce");
    MDSVERIFYNAME(pcEnBasePceD->pcTmpGrad, "pcEnBasePceD->pcTmpGrad");
    MDSVERIFYNAME(pcEnBasePceD->pcBasePce->pcMatl, "pcEnBasePceD->pcBasePce->pcMatl");

    // Local variables
    float   new_draft;              // new calculated draft

    try
    {
        //-------------------------------------------------------------
        // Bulk copy data from entry piece to exit piece
        // Note: this copies the base component of the piece data only.
        //-------------------------------------------------------------
        *pcExBasePceD = *pcEnBasePceD;

        // ---------------------------------------------------------------
        // Calculate initial exit dimensions
        // ---------------------------------------------------------------
        if ( !Initial_Exit_Dimensions() )
        {
            return false;
        }


        //-------------------------------------------------------------
        // Calculate force torque & power
        //-------------------------------------------------------------
        if ( !Force_Torque_Power() )
        {
            return false;
        }


        //-------------------------------------------------------------
        // Adjust entry/exit piece speeds.
        //-------------------------------------------------------------
//AEB        pcEnBasePceD->speed = pcExBasePceD->speed = this->speed;

        //-----------------------------------------------------------------
        // Update rollbite quantities for a none_dummied drafting edger.
        //-----------------------------------------------------------------
        if ( !this->dummied && (this->draft >= Physcon.tol6) )			若非虚拟立辊，即立辊工作
        {
            if ( !Update_RollBite_Quantities() )
            {
                EMSG<<"Operate() "<<(const char*)(this->objName())
                    <<"  Update_Rollbite_Quantities() returned FALSE"
                    <<END_OF_MESSAGE ;

                return false;
            }
        }

        //-----------------------------------------------------------------
        // Check for a none_dummied drafting edger.
        //-----------------------------------------------------------------
        if ( do_limchks && !this->dummied && (this->draft >= Physcon.tol6) )
        {
            //-----------------------------
            // Limit check the draft-torque
            //-----------------------------
            if ( torque > torque_max )
            {
                new_draft = Draft_From_Torque(
                      torque_max,                           // [kgm_ftlb_Nm] desired torque
                      draft*0.8*torque_max/torque,          // [mm_in] low draft bracket
                      draft*1.05);                          // [mm_in] high draft bracket

                DMSG(-diagLvl)<<"BaseEdg "<<(const char*)(this->objName())
                    <<" torque="<<torque
                    <<" exceeds torque_max="<<torque_max
                    <<"  Reduce draft from "<<draft
                    <<" to "<<new_draft
                    <<END_OF_MESSAGE ;

                draft = new_draft;
                draft_max = draft;
                lim = el_trq;

                //----------------------------------------------------------
                // Update rollbite quantities.
                //----------------------------------------------------------
                if ( !Update_RollBite_Quantities() )
                {
                    EMSG<<"Operate() "<<(const char*)(this->objName())
                        <<"  Update_Rollbite_Quantities() returned FALSE"
                        <<END_OF_MESSAGE ;

                    return false;
                }

                // ------------------
                // Modify exit width 
                // ------------------
                pcExBasePceD->width  = pcEnBasePceD->width - draft;

            }   // end if ( torque > pcEdg->pcMtr->torque_rate )

            //-----------------------------
            // Limit check the draft-force
            //-----------------------------
            if ( force_strip > force_max )
            {
                new_draft = Draft_From_Force(force_max);

                draft = new_draft;
                draft_max = draft;
                lim = el_frcmax;

                //----------------------------------------------------------
                // Update rollbite quantities.
                //----------------------------------------------------------
                if ( !Update_RollBite_Quantities() )
                {
                    return false;
                }

                // ------------------
                // Modify exit width 
                // ------------------
                pcExBasePceD->width  = pcEnBasePceD->width - draft;

            }   // end if ( force_strip > force_max )
        }

        //-----------------------------------------------------------------
        // Check for a none_dummied drafting edger.
        //-----------------------------------------------------------------
        if ( !this->dummied && (this->draft >= Physcon.tol6) )
        {
            //----------------------------------------------
            // Calculate width error to be removed by edger			计算由于立辊恢复导致的宽度误差
            //----------------------------------------------
            wid_err = draft * effi;						计算待消除的宽度误差
	
            // ----------------------------------
            //  Calculates edger recovery amount.
            // ----------------------------------
            if ( !pcWidth->Recovery(					计算宽展恢复量与效率
                                pcEnBasePceD->pcBasePce->family,
                                pcEnBasePceD->thick,
                                pcEnBasePceD->width,
                                draft,
                                pcStdRollPrD->getAvgDiam(),
                                pcBaseEdg->grooved,
                                pcBaseEdg->throat,
                                pcBaseEdg->angle,
                                pcBaseEdg->diam_max,
                                pcBaseEdg->diam_min,
                                groov_mult,
                                recov,
                                effi
								
                                                    ) )
            {
                EMSG << "INVALID Recovery  status" 
                     << END_OF_MESSAGE;

                return false;
            }
        }
        else
        {
            effi = 0.0F;					立辊非工作状态，宽度控制效率为0
        }

        //-------------------------------------------------------------
        // Recovery is cumulative, in case we have a double edging pass.			恢复量是累计的
        //-------------------------------------------------------------
        if ( pcEnBasePceD->recovery == 0.0 )
        {
            pcExBasePceD->recovery = recov;
        }
        else
        {
            pcExBasePceD->recovery = pcEnBasePceD->recovery + pcBaseEdg->ddog_mlt * recov;
        }

        //-------------------------------------------------------------
        // Calculate the exit length for a none_dummied drafting edger.			实际出口长度
        //-------------------------------------------------------------
        if ( !this->dummied && (this->draft >= Physcon.tol6) )
        {
            pcExBasePceD->length = ( pcEnBasePceD->length * pcEnBasePceD->width ) /
                                   ( pcExBasePceD->width + pcExBasePceD->recovery );

        }

        //-------------------------------------------------------------
        // Assign the temperature geometry and distribution from the
        // dynamic entry piece object to the exit piece state.
        //-------------------------------------------------------------
        if ( !pcExBasePceD->pcTmpGrad->Assign_Geometry(&pcEnBasePceD->pcTmpGrad->geometry) )
        {
            EMSG << "Class: " << ((const char*)Get_Class_Name())
                 << ", Object: " << ((const char*)objName())
                 << ", Bad status from pcExPceD->pcTmpGrad->Assign_Geometry()"
                 << END_OF_MESSAGE;

            return false;
        }

        //----------------------------------------------------------------------
        // Alter exit piece geometry for the edger width change
        // Note: this may destroy the asymmetry of the temperature distribution
        //----------------------------------------------------------------------
        pcExBasePceD->pcTmpGrad->Alter_Geometry(
                pcExBasePceD->pcTmpGrad->geometry.num_nodes, // IN number of nodes in the top half of piece
                pcExBasePceD->pcTmpGrad->geometry.symmetry,  // IN whether temperatures in top and bottom of
                                               //    piece are to be assumed symmetrical
                true,                          // IN include effect of thickness in geometry
                                               //    calculations
                pcExBasePceD->thick,           // IN [mm_in] thickness of piece
                pcExBasePceD->width,           // IN [mm_in] width of piece
                pcExBasePceD->pcTmpGrad->geometry.matl_code, // IN [-] material code
                1.0,                           // IN [-] bottom view factor
                1.0);                          // IN [-] side view factor if thickness included
        
        //-------------------------------------------------------------
        //  Set the exit piece state temperatures according to the
        //  final TmpGrad status  ( after Alter_Geometry())
        //-------------------------------------------------------------
        pcExBasePceD->temp_avg =
            pcExBasePceD->pcTmpGrad->Average( cTmpGrad::whole ) ;   // C_F
        pcExBasePceD->temp_surf =
            pcExBasePceD->pcTmpGrad->Get_Surface_Temp( cTmpGrad::top ) ; // C_F
        pcExBasePceD->temp_surf_bot =
            pcExBasePceD->pcTmpGrad->Get_Surface_Temp( cTmpGrad::bottom ) ; // C_F
    
    }
    catch(...)
    {
        // Respond to exception
        EMSG << "Class: " << ((const char*)Get_Class_Name())
             << ", Object: " << ((const char*)objName())
             << ", Exception caught" << END_OF_MESSAGE;
        // Pass exception to outer level handler
        throw;
    }

    return true;

}

//-------------------------------------------------------------------------
// FLOWSTRESS ABSTRACT:
// Calculate the mean flowstress in the rollbite.  The user must supply
// a function on the derived edger to calculate something other than zero. 
//-------------------------------------------------------------------------
float cBaseEdgD::Flowstress(void)
{
    EMSG << "Need to provide a flowstress implementation"
         << END_OF_MESSAGE;
    return 0.0;
}


//-------------------------------------------------------------------------
// SPEED ABSTRACT:
// Calculate the roll peripheral speed and motor rpm.  The user may
// override this on the derived stand to suite his specific purposes.
//-------------------------------------------------------------------------
void    cBaseEdgD::Speed(void)
{
    cBaseMillStdD::Speed();

} // End cBaseStdD::Speed()


//-------------------------------------------------------------------------
// VOLUME_FLOW ABSTRACT:
// Calculate the volume flow.  The user may override this on the derived
// stand to suite his specific purposes.
//-------------------------------------------------------------------------
float   cBaseEdgD::Volume_Flow()
{
    return cBaseMillStdD::Volume_Flow();

} // End cBaseStdD::Volume_Flow()



//-------------------------------------------------------------
// DfDwx() ABSTRACT:
//
// Calculate Dfrc_Ddraft, the strip modulus.  dfrc_ddraft is updated on the
// edger and also returned as a float.
//
// NOTE: Since dfrc_ddraft is specified to be a positive number, we
// calculate change in force wrt change in draft (entry width
// constant), rather than change in force wrt change in exit
// width.
//
// NOTE: This method is subject to review.  We may decide to
// provide change in force wrt to change in exit width.
//
// NOTE: We provide two ways to calculate this quantity, the
// original way was to use the transfer function method on
// rollbite.  However it was found that the results may be in
// error due to the fact that the perturbation of draft
// introduces a change in flowstress.  So a calculation has
// been provided which uses a edger operate to update the
// state of the edger after a perturbation.  The original state
// is restored prior to exit.  The use_operate configured
// variable on the static stand is used to determine which
// method to use.
//
//-------------------------------------------------------------
float   cBaseEdgD::DfDwx(void)   // [mton/mm_eton/in_kN/mm]
{
    float   result;


    //-----------------------------------------------------------------
    // Check for a dummied stand or no draft.  If so, dfdwx = 0.0.
    //-----------------------------------------------------------------
    if ( this->dummied || (this->draft <= Physcon.tol6) )
    {
        this->dfrc_ddraft = 0.0;
        return 0.0;
    }

    if ( pcBaseEdg->use_operate )
    {
        float   save_draft = this->draft;
        float   perturb = 0.01F;
        float   dforce;
        float   ddraft;


        // Now perturb the temporary object around the operating point
        this->draft = (1.0F + perturb/2.0F) * save_draft;
        this->Operate();
        dforce = this->force_strip;
        this->draft = (1.0F - perturb/2.0F) * save_draft;
        this->Operate();
        dforce -= this->force_strip;
        ddraft = save_draft * perturb;

        // Update the results in the real object
        this->dfrc_ddraft = dforce / ddraft;

        // Restore the original state
        this->draft = save_draft;
        this->Operate();

        result = this->dfrc_ddraft;
    }
    else
    {
        //-----------------------------------------------------
        // Ask the rollbite object to update its DForce_DDraft
        // state.  Does not include effect on flowstress as a
        // result of the perturbation.
        //-----------------------------------------------------
        if( this->pcRollbite->Calculate_DForce_DDraft() )
        {
            //-----------------------------------------------------
            // Take the per unit width result from rollbite and
            // multiply by the entry thickness.
            //-----------------------------------------------------
            result =
                this->pcRollbite->DForce_DDraft() *
                pcEnBasePceD->thick;
        }
        else
        {
            result = 0.0;
            EMSG
                << (const char*)objName()
                << ": Edger Strip Modulus INVALID"
                << END_OF_MESSAGE;
        }

        this->dfrc_ddraft = result;
    }

    return result;

} // END cBaseEdgD::DfDwx

//-------------------------------------------------------------
// DfDt() ABSTRACT:
//
// Calculate Dfrc_Dtmp.  dfrc_dtmp is updated on the
// edger and also returned as a float.
//
// NOTE: Since dfrc_ddraft is specified to be a positive number, we
// calculate change in force wrt change in draft (entry width
// constant), rather than change in force wrt change in exit
// width.
//
// NOTE: This method is subject to review.  We may decide to
// provide change in force wrt to change in exit width.
//
// NOTE: We provide two ways to calculate this quantity, the
// original way was to use the transfer function method on
// rollbite.  However it was found that the results may be in
// error due to the fact that the perturbation of draft
// introduces a change in flowstress.  So a calculation has
// been provided which uses a edger operate to update the
// state of the edger after a perturbation.  The original state
// is restored prior to exit.  The use_operate configured
// variable on the static stand is used to determine which
// method to use.
//
//-------------------------------------------------------------
float   cBaseEdgD::DfDt(void)   // [mton/oC_eton/oF_kN/oC]
{

    float   result;

    float   save_tmp      = pcEnBasePceD->temp_avg;
    float   save_tmp_surf = pcEnBasePceD->temp_surf;
    float   perturb       = 0.01F;
    float   dforce;
    float   dtmp;


    //-----------------------------------------------------------------
    // Check for a dummied stand or no draft.  If so, dfrc_dtmp = 0.0.
    //-----------------------------------------------------------------
    if ( this->dummied || (this->draft <= Physcon.tol6) )
    {
        this->dfrc_dtmp = 0.0;
        return 0.0;
    }

    // Now perturb the temporary object around the operating point
    pcEnBasePceD->temp_avg = (1.0F + perturb/2.0F) * save_tmp;
    this->Operate();
    dforce = this->force_strip;
    pcEnBasePceD->temp_avg = (1.0F - perturb/2.0F) * save_tmp;
    this->Operate();
    dforce -= this->force_strip;
    dtmp = save_tmp * perturb;

    // Update the results in the real object
    this->dfrc_dtmp = fabs( dforce / dtmp );

    // Restore the original state
    pcEnBasePceD->temp_avg = save_tmp;
    pcEnBasePceD->temp_surf = save_tmp_surf;
    this->Operate();

    result = this->dfrc_dtmp;

    return result;

}   // END cBaseEdgD::DfDt


//---------------------------------------------------------------------
// Initial_Exit_Dimensions() ABSTRACT:
//
// Calculate piece exit dimensions from entry side dimensions and
// draft.
//---------------------------------------------------------------------
bool    cBaseEdgD::Initial_Exit_Dimensions(void)				基本宽度变化：出口宽度=入口宽度-压下量
{
    //-------------------------------------------------------------
    // Calculate edger exit width
    //-------------------------------------------------------------
    pcExBasePceD->width  = pcEnBasePceD->width - draft;

    if ( pcExBasePceD->width <= 0.0 )
    {
        EMSG << "Stand " << (const char*)objName()
             << " bad exit width " << pcExBasePceD->width
             << END_OF_MESSAGE;

        return false;
    }

    //-------------------------------------------------------------
    // Calculate per-unit draft
    //-------------------------------------------------------------
    draft_pu = draft / pcEnBasePceD->width;         				计算压下率:压下量/入口宽度

    return true;

}


//-------------------------------------------------------------
// Force_Torque_Power() ABSTRACT:
//
// Calculate Force, Torque and Power.
//-------------------------------------------------------------
bool    cBaseEdgD::Force_Torque_Power(void)
{
    enum cMatl::matlEnum product = mill().data().product;

    //-------------------------------------------------------------
    // Calculate roll peripheral speed and motor RPM.
    //-------------------------------------------------------------
    this->Speed();

    //----------------------------------------------------------
    // load coeficient of friction from static to dynamic state
    //----------------------------------------------------------
    cof = pcBaseEdg->cof;

    //----------------------------------------------------------------------------
    // This function calculates the force, torque, slip, arc of contact and power.		计算力，扭矩，滑移，接触弧度和功率。
    //----------------------------------------------------------------------------
    fs = 0.0;
    //-----------------------------------------------------------------
    // Check for a none_dummied drafting edger.
    //-----------------------------------------------------------------
    if ( !this->dummied && (this->draft >= Physcon.tol6) )
    {
        // ---------------------------------------------------------------
        // Set the required piece input data for
        // the roll pressure distribution calculations.
        // ---------------------------------------------------------------
        fs = Flowstress();
        if ( !pcRollbite->Set_Input_Data(
                pcStdRollPrD->getAvgDiam(),         // IN undeformed work roll diam. [minor_length]
                pcStdRollPrD->getHitchcock(),       // IN Hitchcock's constant [pressure]
                pcEnBasePceD->width,                // IN entry thickness (width) [minor_length]
                pcExBasePceD->width,                // IN exit thickness (width)[minor_length]
                fs,                                 // IN mean flow stress (const) [pressure]
                pcEnBasePceD->temp_avg,             // IN mean temperature (const) [C_F]
                0.0F,                               // IN entry tension [pressure]
                0.0F,                               // IN exit tension [pressure]
                pcEnBasePceD->pcBasePce->pcMatl->Elasticity(
                            product,
                            pcEnBasePceD->pcBasePce->matl_code,
                            pcEnBasePceD->temp_avg ),           // strip elastic mod
                pcEnBasePceD->pcBasePce->pcMatl->Poisson(
                         product,
                         pcEnBasePceD->pcBasePce->matl_code ),  // strip poisson ratio
                cof,                                            // IN mean coeff of friction [-]
                speed/Physcon.vel_time,                         // [m/sec_ft/sec] surface velocity of roll
                pcEnBasePceD->pcBasePce->matl_code) )           // material code
        {
            EMSG << "Class: " << ((const char*)Get_Class_Name())
                 << ", Object: " << ((const char*)objName())
                 << ", Bad status from pcRollbite->Set_Input_Data()"
                 << END_OF_MESSAGE;

            return false;
        }
        if ( !pcRollbite->Calculate_Power() )
        {
            EMSG << "Class: " << (const char*)Get_Class_Name()
                 << ", Object: " << (const char*)objName()
                 << ", Bad status from pcRollbite->Calculate_Power()"
                 << END_OF_MESSAGE;

            return false;
        }

        // check for VALID status
        //if ( cRollbite::rb_valid != pcRollbite->Status() )  
        if ( 0 )  
        {
            EMSG << "INVALID force calculation due to - " 
                 << cRollbite::Image(pcRollbite->Status())
                 << END_OF_MESSAGE;

            return false;
        }
    }
    else
    {
        slip        = 1.0F;
        draft_comp  = 1.0F;
        force_strip = 0.0F;
        torque      = 0.0F;
        torque_pu   = 0.0F;
        torque_rb   = 0.0F;
        dfrc_ddraft = 0.0F;
        wid_err     = 0.0F;
        recov       = 0.0F;
        power_bear  = 0.0F;
        power_def   = 0.0F;
        power_fri   = 0.0F;
        power_red   = 0.0F;
        power_ten   = 0.0F;
        power_torque= 0.0F;
        power_shaft = 0.0F;
        power_mult  = 1.0F;
        fs          = 0.0F;
    }   // end if ( !this->dummied && (this->draft >= Physcon.tol6) )

    return true;
}


//---------------------------------------------------------------
// Update_RollBite_Quantities() ABSTRACT			
//
// Update rollbite quantities on the stand
//      Volume flow
//      Arc of contact
//      Draft compensation
//      Slip
//      Roll on roll force
//      Strip force
//      Bearing power loss
//      Deformation power
//      Friction power
//      Reduction power
//      Tension power
//      Power torque - Torque calculated from power & speed
//      Shaft power
//      Torque
//      Per unit torque
//---------------------------------------------------------------
bool    cBaseEdgD::Update_RollBite_Quantities(void)				更新辊缝几何和物理参数
{
    //---------------------------------------------------------------
    // NOTE: volume flow on the piece has units of 
    //           [minor_length**2]*[major_length/time]
    //       To get total power for deformation, friction and tension
    //       need to use volume flow in units of 
    //           [minor_length**3/minor_time]
    //       Therefore convert volume flow as indicated
    //---------------------------------------------------------------
    volume_flow = Volume_Flow();				体积流量

    //---------------------------
    // Calculate force and torque
    //---------------------------
    arcon       = pcRollbite->Arcon();                  // arc of contact length			接触弧长
    defrad      = pcRollbite->Deform_Diameter()/2.0;    // deformed roll radius		变形后有效半径
    draft_comp  = this->Draft_Comp();                   // draft comp			压下补偿系数
    slip        = pcRollbite->Slip();                   // forward slip				前滑

    force_strip = pcRollbite->Force() *				轧制力=单位宽度轧制力*轧件厚度（转换为总宽度方向上的力）*力修正系数
                  pcEnBasePceD->thick *
                  force_mult;                           // strip force
    power_bear  = pcBaseEdg->pcMtr->bear_loss *		轴承功率损耗
                  force_strip *
                  speed;                                // bearing power loss
    power_def   = pcRollbite->Deform_Pwr() *			变形功率
                  volume_flow *
                  power_mult;                           // deformation power
    power_fri   = pcRollbite->Friction_Pwr() *			摩擦功率
                  volume_flow *
                  power_mult;                           // friction power
    power_red   = power_def + power_fri;                // reduction power 	总轧制功率=变形功率+摩擦功率
    power_ten   = pcRollbite->Tension_Pwr() *				张力功率
                  volume_flow;                          // tension power
                                    
    torque_rb   = pcRollbite->Torque() * pcEnBasePceD->thick; // torque from rollbite calcs		总扭矩=单位扭矩*总厚度

    power_torque = power_def + power_fri + 						总功率
                   power_ten + power_bear;

    if (power_torque > 0.0)						power_torque表示电机轴端的实际功率
    {
        power_torque = power_torque / 
                       pcBaseEdg->pcMtr->effi_gear;  // shaft power
    }
    else
    {   // regeneration
        power_torque = power_torque *
                       pcBaseEdg->pcMtr->effi_gear;  // shaft power
    }

    // power shaft and power torque are same - represents power
    // required at the motor shaft.
    power_shaft = power_torque;

    // Convert calculated shaft power to torque
    torque = pcBaseEdg->pcMtr->Torque ( power_shaft,
                                        rpm );

    // Convert rated power (with overload factor) to rated torque
    float available_torque =
              pcBaseEdg->pcMtr->Torque ( pcBaseEdg->pcMtr->base_ovrl * 
                                         pcBaseEdg->pcMtr->power_rate, 
                                         pcBaseEdg->pcMtr->base_rpm );

    // per unit rolling torque relative to rated torque
    torque_pu = torque /
                available_torque;

    //-------------------------------------
    // calculate max torque at motor shaft
    //-------------------------------------
    torque_max = pcBaseEdg->pcMtr->Torque_Limit();

    return true;

} // end Update_RollBite_Quantities()


//-----------------------------------------------------------------
// Draft_From_Torque() ABSTRACT
// Draft_Force_From_Torque() ABSTRACT
//
// Calculate the draft required to obtain the given torque.  Makes
// use of the state of rollbite object already on the edger, plus
// the dynamic edger state itself.  Since the torque we are using
// is calculated from shaft power, we cannot directly call a method
// on rollbite to resolve the draft for the torque.  We provide
// logic here similar to that on rollbite, which resolves the
// torque calculated by integrating moments across the rollbite.
//-----------------------------------------------------------------

//-----------------------------------------------------------------
// Static function to return a value of torque, given a draft.				牛顿 - 拉夫逊迭代法
//-----------------------------------------------------------------
static  bool   FN_Calculate_Torque(float &z, void *ptr, float x, float y)			
{
    cBaseEdgD   *pcBaseEdgD = (cBaseEdgD *)(ptr);

    // set the new draft
    pcBaseEdgD->draft = x;

    // operate on the edger
    bool    save_do_limchks = pcBaseEdgD->do_limchks;
    pcBaseEdgD->do_limchks = false;
    pcBaseEdgD->Operate();
    pcBaseEdgD->do_limchks = save_do_limchks;

    // return the force
    z = pcBaseEdgD->torque;

    return true;

} // end FN_Calculate_Torque()


bool    cBaseEdgD::Draft_Force_From_Torque(
                      float& draft,             // OUT [mm_in] calculated new draft
                      float& force,             // OUT [mton_eton_KN] calculated new force
                      float torque_desired,     // IN [kgm_ftlb_Nm] desired torque
                      float draft_low,          // IN [mm_in] low draft bracket
                      float draft_hi)           // IN [mm_in] high draft bracket
{
    // get the draft to satisfy the desired torque
    draft = this->Draft_From_Torque(
                            torque_desired,
                            draft_low,
                            draft_hi);

    // calculate the force
    force = this->pcRollbite->Force() *
                  this->pcEnBasePceD->thick *
                  force_mult;

    return true;
}

float   cBaseEdgD::Draft_From_Torque(						已知扭矩，反推压下量
                      float torque_desired,     // [kgm_ftlb_Nm] desired torque
                      float draft_lo,           // [mm_in] low draft bracket
                      float draft_hi)           // [mm_in] high draft bracket
{
    cMathUty::status_type   cmstatus;
    float                   desired_draft;
    float                   save_draft = this->draft;
    float                   xacc;
    float                   zacc;

    //-----------------------------------------------------------------
    // Make sure that we have valid power calculations on rollbite.
    //-----------------------------------------------------------------
    if ( !pcRollbite->Pwr_Valid() )
    {
        EMSG << (const char*)this->objName() << ": Draft_From_Torque(): Rollbite power is invalid"
             << END_OF_MESSAGE;
        return 0.0;
    }

    //-----------------------------------------------------------------
    // Work out the X & Z accuracy that can be achieved.
    //-----------------------------------------------------------------
    xacc = pcRollbite->Precision()*draft_max/10.0;
    zacc = pcRollbite->Precision()*torque_max/10.0;

    //-----------------------------------------------------------------
    // Call X_For_YZ to solve F(X,Y) - Z = 0.
    //-----------------------------------------------------------------
    cmstatus = cMathUty::X_For_YZ_NR(       // returns calculation status
                        desired_draft,      // OUT desired X, ( draft )
                        FN_Calculate_Torque,// IN  pointer to caller's function
                        (void *)(this),     // IN  void ptr, for use by function
                        0.0,                // IN  Y, not used here
                        torque_desired,     // IN  Z, desired torque
                        draft_lo,           // IN  X low, draft low
                        draft_hi,           // IN  X high, draft high
                        xacc,               // IN  absolute accuracy for draft
                        zacc);              // IN  absolute accuracy for torque

    // restore the draft on the stand
    this->draft = save_draft;

    // restore the edger state
    bool save_do_limchks = do_limchks;
    do_limchks = false;
    this->Operate();
    do_limchks = save_do_limchks;

    //-----------------------------------------------------------------
    // Check return status and return to caller.
    //-----------------------------------------------------------------
    if ( cmstatus != cMathUty::cmuty_valid )
    {
        EMSG << "Draft_From_Torque: " << cMathUty::Image(cmstatus) << " in cMathUty::X_for_YZ_NR"
             << END_OF_MESSAGE;
        return 0.0;
    }

    return desired_draft;

} // End cBaseEdgD::Draft_From_Torque()



//-----------------------------------------------------------------
// Draft_From_Force() ABSTRACT
//
// Calculate the draft required to obtain the given force.
//-----------------------------------------------------------------

//-----------------------------------------------------------------
// Static function to return a value of force, given a draft.
//-----------------------------------------------------------------
static  bool   FN_Calculate_Force(float &z, void *ptr, float x, float y)
{
    cBaseEdgD   *pcBaseEdgD = (cBaseEdgD *)(ptr);

    // set the new draft
    pcBaseEdgD->draft = x;

    // operate on the stand
    bool save_do_limchks = pcBaseEdgD->do_limchks;
    pcBaseEdgD->do_limchks = false;
    pcBaseEdgD->Operate();
    pcBaseEdgD->do_limchks = save_do_limchks;

    // return the force
    z = pcBaseEdgD->force_strip;

    return true;

} // end FN_Calculate_Force()


float   cBaseEdgD::Draft_From_Force(				已知目标力，反推压下量
                      float force_desired)
{
    float                   save_draft = this->draft;
    cMathUty::status_type   cmstatus;
    float                   desired_draft;
    float                   draft_lo;
    float                   draft_hi;
    float                   xacc;
    float                   zacc;

    //-----------------------------------------------------------------
    // Make sure that we have valid force calculations on rollbite.
    //-----------------------------------------------------------------
    if ( !pcRollbite->Force_Valid() )
    {
        EMSG << "Draft_From_Force(): Rollbite force is invalid"
             << END_OF_MESSAGE;
        return 0.0;
    }

    //-----------------------------------------------------------------
    // Make sure that we have a valid draft.
    //-----------------------------------------------------------------
    if ( draft <= 0.0 )
    {
        EMSG << "Draft_From_Force(): Draft is invalid"
             << END_OF_MESSAGE;
        return 0.0;
    }

    //-----------------------------------------------------------------
    // Compute draft lo & hi boundaries for X_For_YZ_NR.  Note that these
    // boundaries are not hard boundaries, but are just used to calculate
    // the initial starting guess for the draft.  Ensure however that the
    // boundaries result in a valid exit thickness.
    //-----------------------------------------------------------------
    draft_lo = draft - draft / 10.0; 
    draft_hi = draft + draft / 10.0;  

    //-----------------------------------------------------------------
    // Work out the X & Z accuracy that can be achieved.
    //-----------------------------------------------------------------
    xacc = pcRollbite->Precision()*draft_max/10.0;
    zacc = pcRollbite->Precision()*force_max/10.0;

    //-----------------------------------------------------------------
    // Call X_For_YZ to solve F(X,Y) - Z = 0.
    //-----------------------------------------------------------------
    cmstatus = cMathUty::X_For_YZ_NR(       // returns calculation status
                        desired_draft,      // OUT desired X, ( draft )
                        FN_Calculate_Force, // IN  pointer to caller's function
                        (void *)(this),     // IN  void ptr, for use by function
                        0.0,                // IN  Y, not used here
                        force_desired,      // IN  Z, desired force
                        draft_lo,           // IN  X low, draft low
                        draft_hi,           // IN  X high, draft high
                        xacc,               // IN  absolute accuracy for draft
                        zacc);              // IN  absolute accuracy for force

    // restore the draft on the stand
    this->draft = save_draft;

    // restore the stand state
    bool save_do_limchks = do_limchks;
    do_limchks = false;
    this->Operate();
    do_limchks = save_do_limchks;

    //-----------------------------------------------------------------
    // Check return status and return to caller.
    //-----------------------------------------------------------------
    if ( cmstatus != cMathUty::cmuty_valid )
    {
        EMSG << "Error " << cMathUty::Image(cmstatus) << " in cMathUty::X_for_YZ_NR"
             << END_OF_MESSAGE;
        return 0.0;
    }

    return desired_draft;

} // End cBaseEdgD::Draft_From_Force()
