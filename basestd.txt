//---------------------------------------------------------------------------------
//
// ABSTRACT:
//     This file defines the base static and dynamic stand object methods.
//
//
//     FUNCTION/PROCEDURE/TASK  DESCRIPTION
//     -----------------------  -----------------------------------------------
//   static
//     cBaseStd                 Stand object constructor
//     cBaseStd                 Stand object copy constructor
//     ~cBaseStd                Stand object destructor
//     operator=                assignment operator
//     operator==               equivalence operator
//     operator<                less than operator
//     linkObj                  Method for linking stand to other objects
//     dump                     dump member data and/or composed objects
//     zeroData                 zero data for base std
//     getBaseStd               locates the static stand object in the list
//                              of lists on the cMill object, and returns a pointer to it.
//     rBCOMountEnum_Fmt        FMT_ENCODE  encode the binary structure data to an
//                                          ASCII string
//                              FMT_DECODE  decode the ASCII string to binary structure data
//                              FMT_MIN_ENUM  Put min value of enum into p
//                              FMT_MAX_ENUM  Put max value of enum into p
//   dynamic
//     cBaseStdD                Stand object constructor
//     cBaseStdD                Stand object copy constructor
//     ~cBaseStdD               Stand object destructor
//     operator=                assignment operator
//     operator==               equivalence operator
//     operator<                less than operator
//     linkObj                  Method for linking stand to other objects
//     dump                     dump member data and/or composed objects
//     Post_Config              Virtual function to allow the user to carry
//                              out post processing for a dynamic stand.
//     zeroData                 zero data for base std
//     Assign_State             Assigns state data from a source object to a
//                              destination object ofthe same type.
//     Operate                  Given entry piece data and other data supplied by external
//                              sources (ex: draft), this method calculates relevant
//                              dynamic stand values and updates the exit piece state.
//     Draft_From_PUForce       Calculate the draft required to obtain the given force.
//                              Makes use of the state of rollbite object already on
//                              the stand.
//     Draft_From_Torque
//     Draft_Force_From_Torque  Calculate the draft required to obtain the given torque.
//                              Makes use of the state of rollbite object already on
//                              the stand, plus the dynamic stand state itself.
//     Draft_From_Power
//     Draft_Force_From_Power   Calculate the draft required to obtain the given power.
//                              Makes use of the state of rollbite object already on
//                              the stand, plus the dynamic stand state itself.
//     Calculate_Torque         Static function to return a value of torque, given a draft.
//     Thrm_Wear_Crns           Function to calculate the following roll stack quantities
//                              given pointers to the dynamic roll objects, which may
//                              represent either the current (i.e. real-time) or projected
//                              state, piece width and roll shift position:
//                                  Piece to work roll stack thermal crown
//                                  Piece to work roll stack wear crown
//                                  Work roll to backup roll stack thermal crown
//                                  Work roll to backup roll stack wear crown
//                                  Backup roll stack wear crown
//     updateSKRM               Update the roll stack deflection multiplier.
//                              This function is called any time either of the work rolls
//                              or the backup rolls is changed.  On model initialization,
//                              it will be called as each roll is initialized, but will not
//                              complete calculations until the last roll at the stand is
//                              processed. Therefore, missing rolls are not an error
//                              (returns false) Returns true if calculation completed.
//     Tension                  Calculate the tension applied to the exit piece.  The user
//                              must supply a function on the derived stand to calculate
//                              something other than zero.
//     Flowstress               Calculate the mean flowstress in the rollbite.  The user
//                              must supply a function on the derived stand to calculate
//                              something other than zero.
//     Speed                    Calculate the roll peripheral speed and motor rpm.  The user
//                              may override this on the derived stand to suite his specific
//                              purposes.
//     Volume_Flow              Calculate the volume flow.  The user may override this on
//                              the derived stand to suite his specific purposes.
//   Update_RollBite_Quantities Update rollbite quantities on the stand
//                                  Volume flow
//                                  Arc of contact
//                                  Draft compensation
//                                  Slip
//                                  Roll on roll force
//                                  Strip force
//                                  Bearing power loss
//                                  Deformation power
//                                  Friction power
//                                  Reduction power
//                                  Tension power
//                                  Power torque - Torque calculated from power & speed
//                                  Shaft power
//                                  Torque
//                                  Per unit torque
//  Initial_Exit_Dimensions     Calculate piece exit dimensions from entry side
//                              dimensions and draft.
//  Length                      Calculate the exit piece length. User may supply a function on the
//                              derived stand if he chooses to override the calculation on the base stand.
//  Force_Torque_Power          Calculate Force, Torque and Power.
//  DfDhx                       Calculate DFDHX, the strip modulus.  dfdhx is updated
//                              on the stand and also returned as a float.
//  stdLimEnum_Fmt
//  ssuLimEnum_Fmt
//                              FMT_ENCODE  encode the binary structure data to an
//                                          ASCII string
//                              FMT_DECODE  decode the ASCII string to binary structure data
//                              FMT_MIN_ENUM  Put min value of enum into p
//                              FMT_MAX_ENUM  Put max value of enum into p
//
//  findRBCOMount
//  findStdLimEnum
//  findSsuLimEnum              This function returns the index of the "image"
//-----------------------------------------------------------------------------
//------------------------------
// C++ standard library includes
//------------------------------
#include <stdlib.h>

#include "basestd.hxx"

#include "objhash.hxx"
#include "mill.hxx"
#include "rollbite.hxx"
#include "stdrollpr.hxx"
#include "mtr.hxx"
#include "matl.hxx"
#include "physcon.hxx"
#include "width.hxx"
#include "mathuty.hxx"
#include "basepce.hxx"
#include "utility.hxx"
#include "flowstress.hxx"
#include "rbheat.hxx"


#ifdef WIN32
    #ifdef _DEBUG
    #define new DEBUG_NEW
    #endif
    #pragma warning(disable: 4244) // double to float conversion (NT thinks constants are doubles)
#endif

// Diagnostic level specific to this file
static const cAlarm::DiagnosticCodeEnum diagLvl(cAlarm::Std);

// Data schema for the cBaseStd class.
static cSchema::schema_type cBaseStd_schema[]=
{
    //Next  Enum  Schema details                            Fmt  Units        Comment
    //====  ====  ========================================  ==== ===========  ==================================================
    LINK_TO_SCHEMA("cBaseMillStd","cBaseMillStd")
    { NULL, NULL, SCHEMA_T(cBaseStd,float,wk2),             "",  "kg*m**2_lb*ft**2", "total stand's moment of inertia (reflected to motor shaft)" },
    { NULL, NULL, SCHEMA_T(cBaseStd,float,slscoef),         "",  "",          "stand lead speed factor coeficient" },
    { NULL, NULL, SCHEMA_T(cBaseStd,float,spostim),         "",  "sec",       "screw positioning time" },
    { NULL, NULL, SCHEMA_T(cBaseStd,float,m_cond),          "",  "pu",        "roll bite condution heat multiplier tuning" },
    { NULL, NULL, SCHEMA_T(cBaseStd,float,m_deform),        "",  "pu",        "roll bite deformation heat multiplier tuning" },
    { NULL, NULL, SCHEMA_T(cBaseStd,float,m_fric),          "",  "kW",        "roll bite friction heat multiplier tuning" },
    { NULL, cBaseStd::rBCOMountEnum_Fmt, SCHEMA_T(cBaseStd,rBCOMountEnum,rBCOMount),   "",  "",          "roll bending crown out mechanical mounting" },
    { NULL, NULL, SCHEMA_T1(cBaseStd,char,forceDlanName,nameSize32),"","","DLan signal name, std force"},
    { NULL, NULL, SCHEMA_T(cBaseStd,bool,calc_spread),      "",  "",          "calculate spread if true" },
    //{ NULL, NULL, SCHEMA_T(cBaseStd,float,skrm),            "", "",           "Roll stack deflection multiplier"},
    { NULL, NULL, SCHEMA_T(cBaseStd,bool,two_high),         "", "",           "If two_high = TRUE, only work rolls (no backup rolls)" },
    { NULL, NULL, SCHEMA_T(cBaseStd,bool,six_high),         "", "",           "If six_high = TRUE, 6-hi stand" },
	{ 0 }   // terminate list
};

// Link all the schema's together
cSchema::schema_name_type cBaseStd::sSchema[]=
{
    {
        "cBaseStd",                         // name
        sizeof(cBaseStd),                   // size
        cBaseStd_schema,                    // schema
        false,                              // packed
        false,                              // allow ptr
        false,                              // Read only
        "Base Static stand configuration",  // comment
        0                                   // offset to config data
    },

    { 0 } // terminate list
};

// Data schema for the cBaseStdD class.
static cSchema::schema_type cBaseStdD_schema[]=
{
    //Next  Enum  Schema details                            Fmt  Units        Comment
    //====  ====  ========================================  ==== ===========  ==================================================
    LINK_TO_SCHEMA("cBaseMillStdD","cBaseMillStdD")

    { NULL, NULL, SCHEMA_T(cBaseStdD,int,stdNum),           "",  "",          "horizontal stand number" },
    { NULL, NULL, SCHEMA_T(cBaseStdD,float,force_ror),      "",  "mton_eton_kN", "roll on roll force" },
    { NULL, NULL, SCHEMA_T(cBaseStdD,float,load_shift),     "",  "pu",        "per unit (Target) load distribution" },
    { NULL, NULL, SCHEMA_T(cBaseStdD,float,lcl_pwr_def),    "",  "kW",        "local deformation power" },
    { NULL, NULL, SCHEMA_T(cBaseStdD,float,lcl_pwr_fri),    "",  "kW",        "local friction power" },
    { NULL, NULL, SCHEMA_T(cBaseStdD,float,lead_speed),     "",  "mpm_fpm_mps", "lead speed required for impact compensation" },
    { NULL, cBaseStdD::stdLimEnum_Fmt, SCHEMA_T(cBaseStdD,stdLimEnum,oplim), "",  "", "initial (before adjusting drafts) limit reason" },
    { NULL, cBaseStdD::stdLimEnum_Fmt, SCHEMA_T(cBaseStdD,stdLimEnum,lim), "",  "",   "limit reason" },
    { NULL, NULL, SCHEMA_T(cBaseStdD,float,dfdhx),          "",  "eton/mm_mton/in_kN/mm", "d force / d exit gauge" },
    { NULL, NULL, SCHEMA_T(cBaseStdD,float,dtmp_avg),       "",  "C_F", "average temperature drop across stand" },
    { NULL, NULL, SCHEMA_T1(cBaseStdD,float,heat_roll,2),   "",  "btu/sec_kcal/sec_J/sec","rate of heat flow into work rolls" },
    { NULL, NULL, SCHEMA_T(cBaseStdD,float,spread),         "",  "mm_in",     "spread due to horizontal draft" },
    { NULL, NULL, SCHEMA_T(cBaseStdD,float,eff_ent_width),  "",  "mm_in", "effective entry width to rollbite (edged width + expected recovery)" },
    { NULL, NULL, SCHEMA_T(cBaseStdD,float,rbite_width),    "",  "mm_in", "width used by rollbite for force calcs" },
    { NULL, NULL, SCHEMA_T(cBaseStdD,float,lheat_mult),     "",  "",          "roll bite latent heat multiplier tuning" },

    { NULL, NULL, SCHEMA_T(cBaseStdD,float,power_meas),     "",  "kw",        "fbk measured power for temperature calcs" },
    { NULL, NULL, SCHEMA_T(cBaseStdD,float,force_meas),     "",  "mton_eton_kN", "Measured force " },

    { NULL, NULL, SCHEMA_T(cBaseStdD,float,dtemp_deform),   "",  "C_F", "Increase in average piece temperature due to deformation heat" },
    { NULL, NULL, SCHEMA_T(cBaseStdD,float,dtemp_frict),    "",  "C_F", "Increase in average piece temperature due to friction heat" },
    { NULL, NULL, SCHEMA_T(cBaseStdD,float,dtemp_cond),     "",  "C_F", "Increase in average piece temperature due to conduction heat" },

    { NULL, NULL, SCHEMA_PO(cBaseStdD,cBaseStd,pcBaseStd),  "",  "",         "pointer to base static stand" },
    //{ NULL, NULL, SCHEMA_PO(cBaseStdD,cRbheat,pcRbheat),    "",  "",         "pointer to rbheat" },

    { 0 }   // terminate list

};

// Link all the schema's together
cSchema::schema_name_type cBaseStdD::sSchema[]=
{
    {
        "cBaseStdD",                        // name
        sizeof(cBaseStdD),                  // size
        cBaseStdD_schema,                   // schema
        false,                              // packed
        false,                              // allow ptr
        false,                              // Read only
        "Base Dynamic stand data",          // comment
        0                                   // offset to config data
    },

    { 0 } // terminate list
};



#define T(x) #x
    const char* rBCOMountImage[]= { RBCOMOUNT_LIST , "\0"};
    const char* stdLimImage[]   = { STDLIM_LIST , "\0"};   // enumerated stand limit reason
    const char* ssuLimImage[]   = { SSULIM_LIST , "\0"};   // enumerated shape limit reason
#undef T

// Stand limit reasons statements.  These must be in the same order as the
// STDLIM_LIST.  "Reasons" CANNOT be any longer than 50 characters including
// spaces and punctuation.
const char* stdLimReasons[(int)sl_guardvalue+1] = {
    "NOT defined",                                      // sl_undef
    "NO limits encountered",                            // sl_nolim
    "At maximum roll bite angle",                       // sl_biteangle
    "At strip edger buckling limit",                    // sl_buckl
    "At motor current limit",                           // sl_curr
    "At minimum draft limit",                           // sl_draftmin
    "At maximum draft limit",                           // sl_draftmax
    "At maximum gap limit",                             // sl_gaplimit
    "At minimum per-unit draft limit",                  // sl_pudmin
    "At maximum per-unit draft limit",                  // sl_pudmax
    "Draft set to a fixed value",                       // sl_draftfixed
    "At speed limit",                                   // sl_spd
    "At minimum force limit",                           // sl_frcmin
    "At maximum force limit",                           // sl_frcmax
    "At minimum power limit",                           // sl_pwrmin
    "At maximum power limit",                           // sl_pwrmax
    "At motor torque limit",                            // sl_trq
    "At BOTH High Force and Low Draft limit",           // sl_hfld
    "At high soft force limit",                         // sl_frcsfthi
    "At soft power limit",                              // sl_pwrsft
    "At low soft force limit",                          // sl_frcsftlo
    "At high medium force limit",                       // sl_frcmedhi
    "At low medium force limit",                        // sl_frcmedlo
    "At BOTH Medium Force and Draft limit",             // sl_mfld
    "At high force soft fixed limit",                   // sl_frcsfthifx
    "At low force soft fixed limit",                    // sl_frcsftlofx
    "At a fixed force limit",                           // sl_frcfixed
    "At exit length limit",                             // sl_overlen
    "INVALID - Guard value" };                          // sl_guardvalue


//-----------------------------------------------------------------------------
// Shape Setup limit reasons statements.  These must be in the same order as the
// SSULIM_LIST.  "Reasons" CANNOT be any longer than 50 characters including
// spaces and punctuation.
//-----------------------------------------------------------------------------
const char* ssuLimReasons[(int)slm_guardvalue+1] = {
    "NOT defined",
    "NO limits encountered",
    "Max Force limit - cannot change FORCE as needed",
    "Draft limit - cannot change FORCE as needed",
    "SSU thickness change clamped - bending limited",
    "UNRESOLVED - bending limited; gage NOT clamped",
    "Soft Force limit",
    "Thickness changed - NOT by SSU - bending limited",
    "Strip NOT flat - NOT enough roll crown",
    "Strip NOT flat - TOO MUCH roll crown",
    "INVALID - Guard value" };


//-----------------------------------------------------------------------------
// Function : findRBCOMount
// This function returns the index of the "image"
//-----------------------------------------------------------------------------
int findRBCOMount (const char* name)
{
    for (int i = 0; strcmp(rBCOMountImage[i], "\0"); ++i)
        if (!strcmp(name, rBCOMountImage[i]))
        {
        return i;
        }
    return (255);
}
//-----------------------------------------------------------------------------
// Function : findStdLimEnum
// This function returns the index of the "image"
//-----------------------------------------------------------------------------
int findStdLimEnum (const char* name)
{
    for (int i = 0; strcmp(stdLimImage[i], "\0"); ++i)
        if (!strcmp(name, stdLimImage[i]))
        {
        return i;
        }
    return (255);
}
//-----------------------------------------------------------------------------
// Function : findSsuLimEnum
// This function returns the index of the "image"
//-----------------------------------------------------------------------------
int findSsuLimEnum (const char* name)
{
    for (int i = 0; strcmp(ssuLimImage[i], "\0"); ++i)
        if (!strcmp(name, ssuLimImage[i]))
        {
        return i;
        }
    return (255);
}

//----------------------------------------------------------
// cBaseStd::rBCOMountEnum_Fmt - Must be static
//    dir
//      FMT_ENCODE    encode the binary structure data to an ASCII string
//      FMT_DECODE    decode the ASCII string to binary structure data
//      FMT_MIN_ENUM  Put min value of enum into p
//      FMT_MAX_ENUM  Put max value of enum into p
//----------------------------------------------------------
void    cBaseStd::rBCOMountEnum_Fmt(
                int dir,                // 0=encode, 1=decode
                void *p,                // pointer to structure data member
                cSchema::schema_type *schema,  // pointer to schema
                char *buff)             // pointer to ASCII character buffer
{
    switch (dir)
    {
      case FMT_ENCODE:   // encode the binary structure data to an ASCII string
      {
        // encode the binary structure data to an ASCII string
        const   char    *side;

        side = Image(*((rBCOMountEnum *)(p)));
        strcpy(buff, side);
        break;
      }
      case FMT_DECODE:   // decode the ASCII string to binary structure data
        // decode the ASCII string to binary structure data
        *((rBCOMountEnum *)(p)) = (rBCOMountEnum)findRBCOMount(buff);
        break;
      case FMT_MIN_ENUM: // Put min value of enum into p
          *((rBCOMountEnum *)(p)) = rbc_undef;
        break;
      case FMT_MAX_ENUM: // Put min value of enum into p
        *((rBCOMountEnum *)(p)) = rbc_guardvalue;
        break;
      default:
          EMSG << " dir out of range " << END_OF_MESSAGE;
    }
}

//----------------------------------------------------------
// cBaseStdD::stdLimEnum_Fmt - Must be static
//    dir
//      FMT_ENCODE    encode the binary structure data to an ASCII string
//      FMT_DECODE    decode the ASCII string to binary structure data
//      FMT_MIN_ENUM  Put min value of enum into p
//      FMT_MAX_ENUM  Put max value of enum into p
//----------------------------------------------------------
void    cBaseStdD::stdLimEnum_Fmt(
                int dir,                // 0=encode, 1=decode
                void *p,                // pointer to structure data member
                cSchema::schema_type *schema,  // pointer to schema
                char *buff)             // pointer to ASCII character buffer
{
    switch (dir)
    {
      case FMT_ENCODE:   // encode the binary structure data to an ASCII string
      {
        // encode the binary structure data to an ASCII string
        const   char    *side;

        side = Image(*((stdLimEnum *)(p)));
        strcpy(buff, side);
        break;
      }
      case FMT_DECODE:   // decode the ASCII string to binary structure data
        // decode the ASCII string to binary structure data
        *((stdLimEnum *)(p)) = (stdLimEnum)findStdLimEnum(buff);
         break;
     case FMT_MIN_ENUM: // Put min value of enum into p
          *((stdLimEnum *)(p)) = sl_undef;
        break;
      case FMT_MAX_ENUM: // Put min value of enum into p
        *((stdLimEnum *)(p)) = sl_guardvalue;
        break;
      default:
          EMSG << " dir out of range " << END_OF_MESSAGE;
    }
}

//----------------------------------------------------------
// cBaseStdD::ssuLimEnum_Fmt - Must be static
//    dir
//      FMT_ENCODE    encode the binary structure data to an ASCII string
//      FMT_DECODE    decode the ASCII string to binary structure data
//      FMT_MIN_ENUM  Put min value of enum into p
//      FMT_MAX_ENUM  Put max value of enum into p
//----------------------------------------------------------
void    cBaseStdD::ssuLimEnum_Fmt(
                int dir,                // 0=encode, 1=decode
                void *p,                // pointer to structure data member
                cSchema::schema_type *schema,  // pointer to schema
                char *buff)             // pointer to ASCII character buffer
{
    switch (dir)
    {
      case FMT_ENCODE:   // encode the binary structure data to an ASCII string
      {
        // encode the binary structure data to an ASCII string
        const   char    *side;

        side = Image(*((ssuLimEnum *)(p)));
        strcpy(buff, side);
        break;
      }
      case FMT_DECODE:   // decode the ASCII string to binary structure data
        // decode the ASCII string to binary structure data
        *((ssuLimEnum *)(p)) = (ssuLimEnum)findSsuLimEnum(buff);
        break;
      case FMT_MIN_ENUM: // Put min value of enum into p
          *((ssuLimEnum *)(p)) = slm_undef;
        break;
      case FMT_MAX_ENUM: // Put min value of enum into p
        *((ssuLimEnum *)(p)) = slm_guardvalue;
        break;
      default:
          EMSG << " dir out of range " << END_OF_MESSAGE;
    }
}

//-----------------------------------------------------------------------------
// Static Stand Object
//-----------------------------------------------------------------------------
//-----------------------------------------------------------------------------
// cBaseStd CONSTRUCTOR ABSTRACT:
//   Stand constructor
//-----------------------------------------------------------------------------
int cBaseStd::count   (0); // initialize static members

cBaseStd::cBaseStd()                // default constructor
    : cBaseMillStd()
{
    Set_Class_Name("cBaseStd");
    Set_Schema("cBaseStd",sSchema);

    // Zero out member data
    zeroData();
}

cBaseStd::cBaseStd( const MString     &objectName,
            const objTypEnum stdType,
            const objPosEnum position,
                  void       *pHash)
    : cBaseMillStd( objectName, stdType, position, pHash )
    , num       (++count)
{
    Set_Class_Name("cBaseStd");
    Set_Schema("cBaseStd",sSchema);

    // Zero out member data
    zeroData();

    // DMSG(-diagLvl)<<"cBaseStd()  objname="
    //             << (const char*) objectName
    //             << END_OF_MESSAGE ;
}

//-------------------------------------------------------------------------
// ~cBaseStd ABSTRACT:
//   Stand deconstructor
//-------------------------------------------------------------------------
cBaseStd::~cBaseStd()
{
    pcMtr = 0;
}

void cBaseStd::zeroData(void)
{
//******** Needs a mutex (semaphore) to be thread safe ********

    // Zero out member data  to a valid initial value
    Zero_Data(this, sizeof(cBaseStd), Get_Schema("cBaseStd::cBaseStd"));

    // Initialize local values;
    m_cond = 1.0;
    m_deform = 1.0;
    m_fric = 1.0;

}

//-------------------------------------------------------------------------
// cBaseStd ABSTRACT:
//   Stand copy constructor
//-------------------------------------------------------------------------
cBaseStd::cBaseStd (const cBaseStd& source)
    : cBaseMillStd( source )
    , num       (source.num)
{
    //
    //  Data is not copied.
}

//-------------------------------------------------------------------------
// OPERATOR = ABSTRACT:
//   Stand assignment operator
//-------------------------------------------------------------------------
cBaseStd& cBaseStd::operator= (const cBaseStd& source)
{
    if (this != &source)
    {
        cBaseMillStd::operator = (source);

        num  = source.num;

        Copy_Data(this, (void *)&source,sizeof(cBaseStd),cBaseStd_schema);

    }
    return (*this);
}

//-------------------------------------------------------------------------
// LINKOBJ ABSTRACT
//   Stand link to static sub-objects
//-------------------------------------------------------------------------
bool cBaseStd::linkObj(const void        *pVoid,
                      const objTypEnum  objType,
                      const objPosEnum  objPos )
{
    cBase   *pcBase = (cBase *)(pVoid);
    bool retValue     (TRUE);
    bool retValueBase (TRUE);
    char    class_name[32];

    if (!pcBase)
    {
        EMSG << "Passed child pointer is NULL - exiting" 
             << END_OF_MESSAGE;
        return (retValue);
    }

    // Perform linkObj on next object up in hierarchy
    retValueBase = cBaseMillStd::linkObj(pVoid, objType, objPos);

    // Get the class name and link base stand objects to the base stand and
    // create links for parent-child relationship
    sprintf( class_name, "%s", (const char *)(pcBase->Get_Class_Name()) );

    if ( 0 == _stricmp("cRollPair", class_name) )
    {
        EMSG<<"Cannot link rollpair to cBaseStd"
            <<END_OF_MESSAGE ;
    }

    return retValueBase && retValue;

} // end cBaseStd::linkObj()

//------------------------------------------------------------
//  getStd() locates the static stand object in the list
//  of lists on the cMill object, and returns a pointer to it.
//-------------------------------------------------------------
cBaseStd* cBaseStd::getBaseStd( char * pStdName )
{
    // Note: although we are looking for a base stand,  the
    // list held by mill() contains "cStd" objects.
    MString key="cStd" ;
    cListDMDS* pcList = (cListDMDS*) mill().lChildren.find(key);
    if ( NULL == pcList )
    {
        EMSG<<"List mill().lChildren does not contain a list with key "
            <<(const char*) key
            <<END_OF_MESSAGE ;
        return NULL ;
    }
    char stdName[nameSize32];
    strncpy( stdName, pStdName, (nameSize32-1)) ;
    stdName[nameSize32-1]='\0' ;
    key = stdName ;
    cBaseStd* pcBaseStdObj = NULL  ;
    pcBaseStdObj = (cBaseStd*) pcList->find(key) ;
    if ( NULL == pcBaseStdObj )
    {
        EMSG<<"getStd(): could not find stand "
            <<(const char*) key
            <<END_OF_MESSAGE ;
    }
    return pcBaseStdObj;
}


cBaseStd* cBaseStd::getBaseStd( int stdNum )
{   MString key="cStd" ;
    cListDMDS* pcList = (cListDMDS*) mill().lChildren.find(key);
    if ( NULL == pcList )
    {
        EMSG<<"List mill().lChildren does not contain a list with key "
            <<(const char*) key
            <<END_OF_MESSAGE ;
        return NULL ;
    }
    if ( stdNum < 1 )
    {
        EMSG<<"getStd() called with invalid stand number "
            <<stdNum
            <<END_OF_MESSAGE ;
        return NULL ;
    }
    //  Walk the list to find a stand with matching stand number.
    cListDMDS::sListData*  qpos = pcList->setStart() ;
    cBaseStd* pcBaseStdObj = NULL ;
    while ( (pcBaseStdObj=(cBaseStd*)pcList->getNext(qpos)) != NULL )
    {
        if ( stdNum == ((cBaseStd*)pcBaseStdObj)->num )
        {
            return pcBaseStdObj ;
        }
    }
    EMSG<<"getBaseStd(): could not find static cBaseStd number "
        <<stdNum
        <<END_OF_MESSAGE ;
    return NULL ;
}

//-------------------------------------------------------------------------
// DUMP ABSTRACT:
//   Std dump contents of the struct.
// The boolean composed if true indicates that the
// object should call the dump function for the
// objects that it contains.
//-------------------------------------------------------------------------
void cBaseStd::dump(const bool composed)
{
    Dump_Data(stdout, "cBaseStd", this, 0, (const char *)objName());

    if (composed)
    {
        if (pcMtr)     pcMtr->dump(composed);
    }
} // end cBaseStd::dump


//-----------------------------------------------------------------------------
// Dynamic Stand Object
//-----------------------------------------------------------------------------
//-----------------------------------------------------------------------------
// cBaseStdD CONSTRUCTOR ABSTRACT:
//   Stand constructor
//-----------------------------------------------------------------------------
int cBaseStdD::count  (0); // initialize static members
cBaseStdD::cBaseStdD()
    : cBaseMillStdD()
    , pcBaseStd  (0)
    , pcRbheat   (0)
{
    Set_Class_Name("cBaseStdD");
    Set_Schema("cBaseStdD",sSchema);

    // Zero out member data
    Zero_Data(this, sizeof(cBaseStdD), Get_Schema("cBaseStdD"));
    force_mult = 1.0;
    power_mult = 1.0;
    feedback = false;
    slip = 1.0;
    lheat_mult = 1.0;
}

cBaseStdD::cBaseStdD( const MString     &objectName,
              const objTypEnum stdType,
              const objPosEnum position,
                    void       *pHash)
    : cBaseMillStdD( objectName, stdType, position, pHash )
    , num       (++count)
    , pcBaseStd  (0)
    , pcRbheat   (0)
{
    Set_Class_Name("cBaseStdD");
    Set_Schema("cBaseStdD",sSchema);

    // Zero out member data
    Zero_Data(this, sizeof(cBaseStdD), Get_Schema("cBaseStdD"));
    force_mult = 1.0;
    power_mult = 1.0;
    feedback = false;
    slip = 1.0;
    lheat_mult = 1.0;

    // Try to establish pointers to related objects
    // First the static base stand must be located.

    // The static base stand is expected to have a name
    // which starts after the underscore in the dynamic base
    // stand's name.

    const char    *pName = strchr(objectName, '_') + 1;
    objHashType::ResultT    result;
    result = Objhash.cmnFind(pName);
    if (HashTableHlp::duplicate == result.status)
    {
        // The linkobj() call will link the dynamic cBaseStdD
        // to the static cBaseStd,  and will also link the
        // cBaseMillStdD to the static cBaseMillStd
        linkObj(result.data, ot_undef, op_undef);

        this->stdNum = pcBaseStd->num ;

    }
    else
    {
        EMSG<<"cBaseStdD() constructor, could not find static cBaseStd "
            <<(const char*) pName
            <<END_OF_MESSAGE ;
    }

    //-----------------------------------------------------------
    //  Second,  look in the current hash table for a stand roll
    //  data object with a name like  xyn_stdrollpr
    //  where "xyn" is the static base stand name.
    //  Some shape derived stand objects use base stand  names
    //  like "fsf1".  If the first search does not work,  and
    //  the base stand name is more than 2 characters long and
    //  starts with "fs",  skip to the third characters and try
    //  again using the rest of the base stand name.
    //-----------------------------------------------------------
    char* pNameTry = (char *)pName ;  // alternative name, if required

    cStdRollPrD* pFound = cStdRollPrD::Find( (objHashType*)pHash, (char *)pName );

    if ( NULL == pFound )
    {   //--------------------------------------------------------------
        //  If the first 2 characters of the apparent static base
        //  stand name are "fs",  try looking at the characters
        //  after the "fs"
        //--------------------------------------------------------------
        if ( ( 2 < strlen(pName)) &&
             ( 0 == _strnicmp( pName, "fs", 2 )) )
        {
            pNameTry += (2*sizeof(char)) ;
            pFound = cStdRollPrD::Find( (objHashType*)pHash, pNameTry );
        }
    }

    if ( NULL == pFound )
    {
        EMSG<<"cBaseStdD()  cannot find roll data for "
              <<(const char*) objectName
              <<"  using name "<<(const char*)(pNameTry)
              <<"  or using name "<<(const char*)(pName)
              << END_OF_MESSAGE ;
    }
    else
    {
        this->linkObj( pFound, ot_undef, op_undef );

         //DMSG(-diagLvl)<<"cBaseStdD(): linked "
         //   <<(const char*)(this->objName())
         //   <<" to roll data object "
         //   <<(const char*)(pFound->objName())
         //   <<END_OF_MESSAGE ;
    }

    //  DMSG(-diagLvl)<<" create cBaseStdD named "
    //                 << (const char*) objectName
    //                 <<"  baseMillStd name="
    //                << (const char*)(pcBaseMillStd->objName())
    //                <<"  baseStd name="
    //                << (const char*)(pcBaseStd->objName())
    //                <<"  baseMillStd->num="
    //                <<  pcBaseMillStd->num
    //                << END_OF_MESSAGE ;

} // end cBaseStdD::cBaseStdD

//---------------------------------------------------------------------
// Virtual function to allow the user to carry out post processing for
// a dynamic stand.
//---------------------------------------------------------------------
bool cBaseStdD::Post_Config(
                char *name,         // unused
                void *psStruct)     // unused
{
    pcEnBasePceD = (cBasePceD *)(previous_obj);
    return true;
}


//-------------------------------------------------------------------------
// ~cBaseStdD ABSTRACT:
//   Stand deconstructor
//-------------------------------------------------------------------------
cBaseStdD::~cBaseStdD()
{
    pcBaseStd      = 0;
    pcEnBasePceD   = 0;
    pcExBasePceD   = 0;
    pcRollbite = 0;
    if ( NULL != pcRbheat )
    {
        delete pcRbheat;
    }
}

void cBaseStdD::zeroData(void)
{
//******** Needs a mutex (semaphore) to be thread safe ********

    // Zero out member data  to a valid initial value
    Zero_Data(this, sizeof(cBaseStdD), Get_Schema("cBaseStdD::cBaseStdD"));

    // Initialize local values;
    force_mult = 1.0;
    power_mult = 1.0;
    feedback = false;
    slip = 1.0;
    lheat_mult = 1.0;

    // Zero data in the cBaseCorr class
    cBaseCorrD::zeroData();
}


//-------------------------------------------------------------------------
// LINKOBJ ABSTRACT
//   Stand link to dynamic sub-objects
//   Note:  static cBaseStd is intended to be linked as a child
//          of this dynamic cBaseStdD
//-------------------------------------------------------------------------
bool cBaseStdD::linkObj( const void       *pVoid,
                        const objTypEnum objType,
                        const objPosEnum objPos )
{
    cBase   *pcBase = (cBase *)(pVoid);
    bool retValue     (TRUE);
    bool retValueBase (TRUE);

    if (!pcBase)
    {
        EMSG << "Passed child pointer is NULL - exiting" << END_OF_MESSAGE;
        return (retValue);
    }

    // Perform linkObj on next object up in hierarchy
    retValueBase = cBaseMillStdD::linkObj(pVoid, objType, objPos);

    // Link base stand objects to the base stand and
    // create links for parent-child relationship

    if ( Is_Base_Class("cRollPairD", pcBase) )
    {
        EMSG<<"Cannot link cRollPairD as child of cBaseStdD"
            << END_OF_MESSAGE ;
    }
    else if ( Is_Base_Class("cBaseStd", pcBase) )
    {
        pcBaseStd = (cBaseStd*) pVoid;
        if (pcBaseStd)
        {
            retValue =FALSE;
        }
    }

    return retValueBase && retValue;

} // end cBaseStdD::linkObj()

//---------------------------------------------------------------------------
// ASSIGN_STATE ABSTRACT:
//  Assigns state data from a source object to a destination object of
//  the same type.
//---------------------------------------------------------------------------
bool  cBaseStdD::Assign_State(cBase * pcDest, cBase * pcSource)
{
    //-----------------------------------------------------------------------
    // Check pointers.  Alarm and abort if source or destination pointer is
    //      NULL.
    //-----------------------------------------------------------------------
    if ( pcDest == NULL )
    {
        EMSG << "NULL pointer in pcDest"
             << END_OF_MESSAGE;
        return false;
    }
    if ( pcSource == NULL )
    {
        EMSG << "NULL pointer in pcSource"
             << END_OF_MESSAGE;
        return false;
    }
    //-----------------------------------------------------------------------
    // Check object types.  Alarm and abort if the source and destination
    //      objects are not identical.
    //-----------------------------------------------------------------------
    if ( pcDest->Get_Class_Name() != pcSource->Get_Class_Name() )
    {
        EMSG << "Cannot assign " << (const char *)pcSource->objName()
             << " to " << (const char *)pcDest->objName()
             << ".  Assignment aborted."
             << END_OF_MESSAGE;
        return false;
    }
    //-----------------------------------------------------------------------
    // Assign source to destination object.
    //-----------------------------------------------------------------------
    *((cBaseStdD *)(pcDest)) = *((cBaseStdD *)(pcSource));
    return true;

}   // end cBaseStdD::Assign_State()

//-------------------------------------------------------------------------
// cBaseStdD ABSTRACT:
//   Stand copy constructor
//-------------------------------------------------------------------------
cBaseStdD::cBaseStdD (const cBaseStdD& source)
    : cBaseMillStdD( source )
    , num       (source.num)
    , stdNum    (source.stdNum)
{
}

//-------------------------------------------------------------------------
// OPERATOR = ABSTRACT:
//   Stand assignment operator
//-------------------------------------------------------------------------
cBaseStdD& cBaseStdD::operator= (const cBaseStdD& source)
{
    if (this != &source)
    {
        cBaseMillStdD::operator=(source);
        Copy_Data(this,(void *)&source,sizeof(cBaseStdD),cBaseStdD_schema);
    }
    return (*this);
}

//-------------------------------------------------------------------------
//   Std dump contents of the struct.
// The boolean composed if true indicates that the
// object should call the dump function for the
// objects that it contains.
//-------------------------------------------------------------------------
void cBaseStdD::dump(const bool composed)
{
    Dump_Data(stdout, "cBaseStdD", this, 0, (const char *)objName());

    if (composed)
    {
        if (pcEnBasePceD)  pcEnBasePceD->dump(composed);    // entry piece
        if (pcExBasePceD)  pcExBasePceD->dump(composed);    // exit piece
        if (pcStdRollPrD) pcStdRollPrD->dump(composed);   // roll pair(s)
        if (pcRollbite) pcRollbite->dump(composed); // roll bite
    }
}// end cBaseStdD::dump


//-------------------------------------------------------------------------
// cBaseStdD::Operate ABSTRACT:
//
// Given entry piece data and other data supplied by external sources
// (ex: draft), this method calculates relevant dynamic stand values and
// updates the exit piece state.
//
// Return "true" if successfully completed.
//
// Return "false" if an error occurs.  In this case the exit piece state is
// undefined.
//
// Any exceptions are caught, alarmed and then thrown to a higher level
// exception handler.  The exit piece state is undefined if an exception
// is thrown.
//-------------------------------------------------------------------------
bool cBaseStdD::Operate(void)
{
    // Check consistency
    MDSVERIFYNAME(next_obj, "next_obj");
    MDSVERIFYNAME(previous_obj, "previous_obj");
    MDSVERIFYNAME(pcBaseStd, "pcBaseStd");
    MDSVERIFYNAME(pcEnBasePceD, "pcEnBasePceD");
    MDSVERIFYNAME(pcExBasePceD, "pcExBasePceD");
    MDSVERIFYNAME(pcEnBasePceD->pcBasePce, "pcEnBasePceD->pcBasePce");
    MDSVERIFYNAME(pcExBasePceD->pcBasePce, "pcExBasePceD->pcBasePce");
    MDSVERIFYNAME(pcEnBasePceD->pcTmpGrad, "pcEnBasePceD->pcTmpGrad");
    MDSVERIFYNAME(pcEnBasePceD->pcBasePce->pcMatl, "pcEnBasePceD->pcBasePce->pcMatl");

    bool rb_temp_sym = false ;  // true if exit temp symmetric

    try
    {
        //-------------------------------------------------------------
        // Bulk copy data from entry piece to exit piece
        //-------------------------------------------------------------
        *pcExBasePceD = *pcEnBasePceD;

        //-------------------------------------------------------------
        // Create an instance of the rbheat object, preserving the
        // number of nodes and nodal spacing as defined in the entry
        // piece object.  This object is created the first time that
        // the "Operate" is called.
        //-------------------------------------------------------------
        if ( NULL == pcRbheat )
        {
            pcRbheat = new cRbheat(pcEnBasePceD->pcTmpGrad->geometry.max_nodes,
                             pcEnBasePceD->pcTmpGrad->geometry.fac_a,
                             pcEnBasePceD->pcTmpGrad->geometry.fac_b);
        }

        //-------------------------------------------------------------
        // Assign the temperature geometry and distribution from the
        // entry piece state to the Rbheat object.
        //-------------------------------------------------------------
        if ( !pcRbheat->Assign_Geometry(&pcEnBasePceD->pcTmpGrad->geometry) )
        {
            EMSG << "Class: " << (const char*)Get_Class_Name() 
                 << ", Object: " << (const char*) objName() 
                 << ", Bad status from pcRbheat->Assign_Geometry()"
                 << END_OF_MESSAGE;
            return false;
        }

        //-------------------------------------------------------------
        // Adjust entry piece temperature for any offset
        //-------------------------------------------------------------
        pcRbheat->Adjust_Node_Temps( tmp_offset );

        //-------------------------------------------------------------
        // Adjust piece width
        // Note: wid_offset is a cold value
        //-------------------------------------------------------------
        pcExBasePceD->width = pcExBasePceD->width +
                              wid_offset * pcEnBasePceD->Expansion();

        //-------------------------------------------------------------
        // Calculate "effective" width at entry to rollbite
        //-------------------------------------------------------------
        eff_ent_width = pcExBasePceD->width + pcExBasePceD->recovery;

        //-------------------------------------------------------------
        // Calculate width "within" rollbite which will be
        // used by Rollbite calculations.
        // Note: This could have additional components
        //       beyond those included in eff_ent_wid
        //-------------------------------------------------------------
        rbite_width = eff_ent_width;

        //-------------------------------------------------------------
        // calculate stand exit dimensions.
        //-------------------------------------------------------------
        if ( !this->Initial_Exit_Dimensions() )
        {
            return false;
        }

        //-------------------------------------------------------------
        // Calculate roll peripheral speed and motor RPM.
        //-------------------------------------------------------------
        this->Speed();

        //-------------------------------------------------------------
        // Calculate Force, Torque and Power.
        //-------------------------------------------------------------
        if ( !this->Force_Torque_Power() )
        {
            EMSG<<"Operate() "<<(const char*)(this->objName())
                <<"  Force_Torque_Power() returned false"
                <<END_OF_MESSAGE ;

            return false;
        }
/*AEB
        //-------------------------------------------------------------
        // Adjust entry/exit piece speeds.
        //-------------------------------------------------------------
        pcEnBasePceD->speed = this->speed * this->draft_comp;
        pcExBasePceD->speed = this->speed * this->slip;
*/
        //-------------------------------------------------------------
        // Calculate rollbite exit temperature.
        //-------------------------------------------------------------
        if ( !(dummied || (draft == 0.0)) )
        {
            int     i = 0;
            double  phi;
            double  theta = pcRollbite->Entry_Angle();
            double  num_slices = 1.0;

            dtemp_deform = 0.0;
            dtemp_frict  = 0.0;
            dtemp_cond   = 0.0;
            heat_roll[0] = 0.0;
            heat_roll[1] = 0.0;

            float temp_roll_top = pcStdRollPrD->getTempCent(rpos_top);    // surface temperature of roll at centerline
            float temp_roll_bot = pcStdRollPrD->getTempCent(rpos_bottom); // surface temperature of roll at centerline
            float   ferrite = pcEnBasePceD->ferrite;

            float   arcon_save = 0.0;
            float   arcon1;

            // Slice the rollbite into a number of slices for calculating roll bite
            // heat effects.  Currently the number of slices is set to 1 to mimic
            // the operation of the original code.
            for ( i=0; i<(int)(num_slices); i++ )
            {
                // Incremental drop and heat flow components
                float   dtemp_deform_slice;
                float   dtemp_frict_slice;
                float   dtemp_cond_slice;
                float   heat_roll_top;
                float   heat_roll_bot;

                phi=theta*(1.0-((double)(i+1))/num_slices);

                // Calculate intermediate speed & thickness
                float   thick_intermed = pcRollbite->Bite_Thickness(
                                            phi,
                                            pcRollbite->Deform_Diameter()/2,
                                            pcExBasePceD->thick);
                float   speed_intermed = pcEnBasePceD->speed *
                                 pcEnBasePceD->thick / thick_intermed;
                // Calculate intermediate oxide layer thickness
                float   thick_oxide_top = pcEnBasePceD->thick_oxide_top *
                                 thick_intermed / pcEnBasePceD->thick;
                float   thick_oxide_bot = pcEnBasePceD->thick_oxide_bot *
                                 thick_intermed / pcEnBasePceD->thick;
                // Calculate length of arc of contact, TBD precise version...
                float   len_arcon = arcon/num_slices;

                arcon1 = pcRollbite->Arc_Contact(pcRollbite->Deform_Diameter()/2.0,
                            thick_intermed-pcExBasePceD->thick);

                len_arcon = arcon -  arcon_save - arcon1;
                arcon_save += len_arcon;

                // Alter geometry to the new dimensions
                pcRbheat->Alter_Geometry(
                    pcRbheat->geometry.num_nodes,  // IN number of nodes in the top half of piece
                    pcRbheat->geometry.symmetry,   // IN whether temperatures in top and bottom of
                                                   //    piece are to be assumed symmetrical
                    true,                          // IN include effect of thickness in geometry
                                                   //    calculations
                    thick_intermed,                // IN [mm_in] thickness of piece
                    pcExBasePceD->width,           // IN [mm_in] width of piece
                    pcRbheat->geometry.matl_code,  // IN [-] material code
                    1.0,                           // IN [-] bottom view factor
                    1.0);                          // IN [-] side view factor if thickness included

                //---------------------------------------------------------
                // Calculate temperature change in the rollbite
                //---------------------------------------------------------
                float   tsurf_top_orig = pcRbheat->Get_Surface_Temp(cTmpGrad::top);
                float   tsurf_bot_orig = pcRbheat->Get_Surface_Temp(cTmpGrad::bottom);
                if ( !pcRbheat->Model(
                        speed_intermed,         // intermediate speed of piece
                        lcl_pwr_def/num_slices, // deformation power in stand (setup/feedback)
                        lcl_pwr_fri/num_slices, // friction power in stand    (setup/feedback)
                        temp_roll_top,          // surface temperature of roll at centerline
                        temp_roll_bot,          // surface temperature of roll at centerline
                        speed,                  // peripheral roll speed      (setup/feedback)
                        pcStdRollPrD->getMatlShell(),  // surface material code of roll
                        len_arcon,              // length of arc of contact for slice
                        ferrite,                // ferrite fraction
                        pcEnBasePceD->pcBasePce->pcMatl,  // pointer to cMatl
                        pcBaseStd->m_deform,    // tuning multiplier on the deformation
                                                // heat into the piece
                        pcBaseStd->m_fric,      // indirect tuning multiplier on the
                                                // friction heat into the piece
                        pcBaseStd->m_cond,      // tuning multiplier on the conduction
                        lheat_mult,              // tuning multiplier on latent heat
                        thick_oxide_top,
                        thick_oxide_bot
                         ) )
                {
                    EMSG <<  "Class: " << (const char*)Get_Class_Name() 
                         << ", Object: " << (const char*) objName()
                         << ", Bad status from pcRbheat->Model()"
                         << END_OF_MESSAGE;
                    return false;
                }
                //---------------------------------------------------------
                // Save the temperature drop components.
                //---------------------------------------------------------
                pcRbheat->Get_Drop_Components(
                        dtemp_deform_slice, // increase in average piece
                                            // temperature due to deformation heat
                        dtemp_frict_slice,  // increase in average piece
                                            // temperature due to friction heat
                        dtemp_cond_slice,   // increase in average piece
                                            // temperature due to conduction heat
                        heat_roll_top,      // rate of heat flow into top work roll
                        heat_roll_bot );    //                        bottom work roll

                // Accumulate into the overall rollbite components
                dtemp_deform += dtemp_deform_slice;
                dtemp_frict  += dtemp_frict_slice;
                dtemp_cond   += dtemp_cond_slice;
                heat_roll[0] += heat_roll_top;
                heat_roll[1] += heat_roll_bot;

                // Recalculate ferrite fraction
                ferrite   =
                    cMathUty::Max(ferrite,
                                  pcEnBasePceD->pcBasePce->pcMatl->Ferrite_Fraction(
                                    mill().data().product,
                                    pcRbheat->Average(cTmpGrad::whole)) );

                // Estimate change in roll temperature
                temp_roll_top += tsurf_top_orig - pcRbheat->Get_Surface_Temp(cTmpGrad::top);
                temp_roll_bot += tsurf_bot_orig - pcRbheat->Get_Surface_Temp(cTmpGrad::bottom);
            }

            // Update strip speed
            pcExBasePceD->speed = pcEnBasePceD->speed *
                             pcEnBasePceD->thick / pcExBasePceD->thick;

            pcExBasePceD->pce_offset = pcEnBasePceD->pce_offset *
                             pcEnBasePceD->thick / pcExBasePceD->thick;

            //-------------------------------------------------------------
            // Update the exit piece temperature state.
            // Note that these temperatures are obtained from the
            // Rbheat object.  When the Rbheat object is transformed back
            // into a cTmpgrad object,  these temperatures may change
            // and are therefore recalculated again below.
            //-------------------------------------------------------------
            pcExBasePceD->temp_avg  = pcRbheat->Average(cTmpGrad::whole);
            pcExBasePceD->temp_surf = pcRbheat->Get_Surface_Temp(cTmpGrad::top);
            pcExBasePceD->temp_surf_bot = pcRbheat->Get_Surface_Temp(cTmpGrad::bottom);
            pcExBasePceD->ferrite   =
                cMathUty::Max( pcEnBasePceD->ferrite,
                               pcEnBasePceD->pcBasePce->pcMatl->Ferrite_Fraction(
                                                     mill().data().product,
                                                     pcExBasePceD->temp_avg) );
            pcExBasePceD->austenite = 1 - pcExBasePceD->ferrite;

            if ( fabs( (pcExBasePceD->temp_surf) - (pcExBasePceD->temp_surf_bot) ) < 1.0F )
            {
                rb_temp_sym = true ;
            }

            //-------------------------------------------------------------
            // Calculate average temperature change across stand
            //-------------------------------------------------------------
            dtmp_avg = pcExBasePceD->temp_avg - pcEnBasePceD->temp_avg;

            //-------------------------------------------------------------
            // Calculate duration of piece point to traverse roll bite
            //-------------------------------------------------------------
            duration = pcRollbite->Entry_Angle() /
                     ( rpm * Physcon.pi * 2.0F /
                     ( pcBaseStd->gearat * Physcon.secpmin ) );

        } // end else (check for dummy)
        else
        {
            dtemp_deform = 0.0F;
            dtemp_frict  = 0.0F;
            dtemp_cond   = 0.0F;
            heat_roll[0] = 0.0F;
            heat_roll[1] = 0.0F;
            dtmp_avg     = 0.0F;
        }

        //----------------------------------------------
        // Apply linear contraction to exit piece width
        //----------------------------------------------
        pcExBasePceD->width = pcExBasePceD->width *
                              pcExBasePceD->Expansion() /
                              pcEnBasePceD->Expansion();

        //----------------------------------------------
        // Determine the optimal number of nodes
        //----------------------------------------------
        int new_nodes;

        if ( pcTmpGradCfg != NULL )
        {
            new_nodes = pcTmpGradCfg->Num_Nodes(
                                    pcRbheat->geometry.num_nodes,
                                    pcExBasePceD->thick);
        }
        else
        {
            new_nodes = pcRbheat->geometry.num_nodes;
        }

        //-------------------------------------------------------------
        // Alter geometry for the width change and potential change in
        // the number of nodes.
        //-------------------------------------------------------------
        pcRbheat->Alter_Geometry(
            new_nodes,                     // IN number of nodes in the top half of piece
            pcRbheat->geometry.symmetry,   // IN whether temperatures in top and bottom of
                                           //    piece are to be assumed symmetrical
            true,                          // IN include effect of thickness in geometry
                                           //    calculations
            pcExBasePceD->thick,           // IN [mm_in] thickness of piece
            pcExBasePceD->width,           // IN [mm_in] width of piece
            pcRbheat->geometry.matl_code,  // IN [-] material code
            1.0,                           // IN [-] bottom view factor
            1.0);                          // IN [-] side view factor if thickness included

        if ( !pcExBasePceD->pcTmpGrad->Assign_Geometry(&pcRbheat->geometry) )
        {
            EMSG << "Class: " << (const char*)Get_Class_Name() 
                 << ", Object: " << (const char*) objName()
                 << ", Bad status from pcExPceD->pcTmpGrad->Assign_Geometry()"
                 << END_OF_MESSAGE;

            return false;
        }

        //------------------------------------------------------------
        //  Set exit base pce temperatures based on the final contents
        //  of the cTmpGrad object
        //------------------------------------------------------------
        pcExBasePceD->temp_avg  
            = pcExBasePceD->pcTmpGrad->Average(cTmpGrad::whole);
        
        pcExBasePceD->temp_surf 
            = pcExBasePceD->pcTmpGrad->Get_Surface_Temp(cTmpGrad::top);
        
        pcExBasePceD->temp_surf_bot 
            = pcExBasePceD->pcTmpGrad->Get_Surface_Temp(cTmpGrad::bottom);

        bool final_temp_sym = (((fabs((pcExBasePceD->temp_surf)-(pcExBasePceD->temp_surf_bot)))<1.0F)?(true):(false));

        if ( final_temp_sym && !rb_temp_sym )
        {
            DMSG(diagLvl)
                <<"Std "<<(const char*)(this->objName())
                <<" temperature asymmetry lost"
                <<END_OF_MESSAGE ;
        }
    }
    catch(...)
    {
        // Respond to exception
        EMSG << "Class: " << (const char*)Get_Class_Name() 
             << ", Object: " << (const char*) objName()
             << ", Exception caught" << END_OF_MESSAGE;
        // Pass exception to outer level handler
        throw;
    }

    return true;

}


//-----------------------------------------------------------------
// Draft_From_Force() ABSTRACT
//
// Calculate the draft required to obtain the given force.
//-----------------------------------------------------------------

//-----------------------------------------------------------------
// Static function to return a value of force, given a draft.
//-----------------------------------------------------------------
static  bool   FN_Calculate_Force(float &z, void *ptr, float x, float y)
{
    cBaseStdD   *pcBaseStdD = (cBaseStdD *)(ptr);

    // set the new draft
    pcBaseStdD->draft = x;

    // operate on the stand
    pcBaseStdD->Operate();

    // return the force
    z = pcBaseStdD->force_strip;

    return true;

} // end FN_Calculate_Force()


float   cBaseStdD::Draft_From_Force(
                      float force_desired)
{
    float                   save_draft = this->draft;
    cMathUty::status_type   cmstatus;
    float                   desired_draft;
    float                   draft_lo;
    float                   draft_hi;
    float                   xacc;
    float                   zacc;

    //-----------------------------------------------------------------
    // Make sure that we have valid force calculations on rollbite.
    //-----------------------------------------------------------------
    if ( !pcRollbite->Force_Valid() )
    {
        EMSG << "Draft_From_Force(): Rollbite force is invalid"
             << END_OF_MESSAGE;
        return 0.0;
    }

    //-----------------------------------------------------------------
    // Make sure that we have a valid draft.
    //-----------------------------------------------------------------
    if ( draft <= 0.0 )
    {
        EMSG << "Draft_From_Force(): Draft is invalid"
             << END_OF_MESSAGE;
        return 0.0;
    }

    //-----------------------------------------------------------------
    // Compute draft lo & hi boundaries for X_For_YZ_NR.  Note that these
    // boundaries are not hard boundaries, but are just used to calculate
    // the initial starting guess for the draft.  Ensure however that the
    // boundaries result in a valid exit thickness.
    //-----------------------------------------------------------------
    draft_lo = draft - draft / 10.0;
    draft_hi = draft + draft / 10.0;

    //-----------------------------------------------------------------
    // Work out the X & Z accuracy that can be achieved.
    //-----------------------------------------------------------------
    xacc = pcRollbite->Precision()*draft_max;
    zacc = pcRollbite->Precision()*force_desired*2.0;

    //-----------------------------------------------------------------
    // Call X_For_YZ to solve F(X,Y) - Z = 0.
    //-----------------------------------------------------------------
    cmstatus = cMathUty::X_For_YZ_NR(       // returns calculation status
                        desired_draft,      // OUT desired X, ( draft )
                        FN_Calculate_Force, // IN  pointer to caller's function
                        (void *)(this),     // IN  void ptr, for use by function
                        0.0,                // IN  Y, not used here
                        force_desired,      // IN  Z, desired force
                        draft_lo,           // IN  X low, draft low
                        draft_hi,           // IN  X high, draft high
                        xacc,               // IN  absolute accuracy for draft
                        zacc);              // IN  absolute accuracy for force

    // restore the draft on the stand
    this->draft = save_draft;

    // restore the stand state
    this->Operate();

    //-----------------------------------------------------------------
    // Check return status and return to caller.
    //-----------------------------------------------------------------
    if ( cmstatus != cMathUty::cmuty_valid )
    {
        EMSG << "Error " << (const char*)cMathUty::Image(cmstatus) 
             << " in cMathUty::X_for_YZ_NR"
             << END_OF_MESSAGE;
        return 0.0;
    }

    return desired_draft;

} // End cBaseStdD::Draft_From_Force()


//-----------------------------------------------------------------
// Draft_From_Torque() ABSTRACT
// Draft_Force_From_Torque() ABSTRACT
//
// Calculate the draft required to obtain the given torque.  Makes
// use of the state of rollbite object already on the stand, plus
// the dynamic stand state itself.  Since the torque we are using
// is calculated from shaft power, we cannot directly call a method
// on rollbite to resolve the draft for the torque.  We provide
// logic here similar to that on rollbite, which resolves the
// torque calculated by integrating moments across the rollbite.
//-----------------------------------------------------------------

//-----------------------------------------------------------------
// Static function to return a value of torque, given a draft.
//-----------------------------------------------------------------
static  bool   FN_Calculate_Torque(float &z, void *ptr, float x, float y)
{
    cBaseStdD   *pcBaseStdD = (cBaseStdD *)(ptr);

    // set the new draft
    pcBaseStdD->draft = x;

    // operate on the stand
    pcBaseStdD->Operate();

    // return the force
    z = pcBaseStdD->torque;

    return true;

} // end FN_Calculate_Torque()


bool    cBaseStdD::Draft_Force_From_Torque(
                      float& draft,             // OUT [mm_in] calculated new draft
                      float& force,             // OUT [mton_eton_KN] calculated new force
                      float torque_desired,     // IN [kgm_ftlb_Nm] desired torque
                      float draft_low,          // IN [mm_in] low draft bracket
                      float draft_hi)           // IN [mm_in] high draft bracket
{
    // get the draft to satisfy the desired torque
    draft = this->Draft_From_Torque(
                            torque_desired,
                            draft_low,
                            draft_hi);

    // calculate the force
    force = this->pcRollbite->Force() *
                  rbite_width *
                  force_mult;

    return true;
}

float   cBaseStdD::Draft_From_Torque(
                      float torque_desired,     // [kgm_ftlb_Nm] desired torque
                      float draft_lo,           // [mm_in] low draft bracket
                      float draft_hi)           // [mm_in] high draft bracket
{
    cMathUty::status_type   cmstatus;
    float                   desired_draft;
    float                   save_draft = this->draft;
    float                   xacc;
    float                   zacc;

    //-----------------------------------------------------------------
    // Make sure that we have valid power calculations on rollbite.
    //-----------------------------------------------------------------
    if ( !pcRollbite->Pwr_Valid() )
    {
        EMSG << "Draft_From_Torque(): Rollbite power is invalid"
             << END_OF_MESSAGE;
        return 0.0;
    }

    //-----------------------------------------------------------------
    // Work out the X & Z accuracy that can be achieved.
    //-----------------------------------------------------------------
    xacc = pcRollbite->Precision()*draft_max;
    zacc = pcRollbite->Precision()*torque_desired*2;

    //-----------------------------------------------------------------
    // Call X_For_YZ to solve F(X,Y) - Z = 0.
    //-----------------------------------------------------------------
    cmstatus = cMathUty::X_For_YZ_NR(       // returns calculation status
                        desired_draft,      // OUT desired X, ( draft )
                        FN_Calculate_Torque,// IN  pointer to caller's function
                        (void *)(this),     // IN  void ptr, for use by function
                        0.0,                // IN  Y, not used here
                        torque_desired,     // IN  Z, desired torque
                        draft_lo,           // IN  X low, draft low
                        draft_hi,           // IN  X high, draft high
                        xacc,               // IN  absolute accuracy for draft
                        zacc);              // IN  absolute accuracy for torque

    // restore the draft on the stand
    this->draft = save_draft;

    // restore the stand state
    this->Operate();

    //-----------------------------------------------------------------
    // Check return status and return to caller.
    //-----------------------------------------------------------------
    if ( cmstatus != cMathUty::cmuty_valid )
    {
        EMSG << "Draft_From_Torque: " << (const char*)cMathUty::Image(cmstatus) 
             << " in cMathUty::X_for_YZ_NR"
             << END_OF_MESSAGE;
        return 0.0;
    }

    return desired_draft;

} // End cBaseStdD::Draft_From_Torque()



//-----------------------------------------------------------------
// Draft_From_Power() ABSTRACT
// Draft_Force_From_Power() ABSTRACT
//
// Calculate the draft required to obtain the given power.  Makes
// use of the state of rollbite object already on the stand, plus
// the dynamic stand state itself.
//-----------------------------------------------------------------

//-----------------------------------------------------------------
// Static function to return a value of power, given a draft.
//-----------------------------------------------------------------
static  bool   FN_Calculate_Power(float &z, void *ptr, float x, float y)
{
    cBaseStdD   *pcBaseStdD = (cBaseStdD *)(ptr);

    // set the new draft
    pcBaseStdD->draft = x;

    // operate on the stand
    pcBaseStdD->Operate();

    // return the shaft power
    z = pcBaseStdD->power_shaft;

    return true;

} // end Calculate_Power()

bool    cBaseStdD::Draft_Force_From_Power(
                      float& draft,             // OUT [mm_in] calculated new draft
                      float& force,             // OUT [mton_eton_KN] calculated new force
                      float power_desired,      // IN [KW] desired power
                      float draft_low,          // IN [mm_in] low draft bracket
                      float draft_hi)           // IN [mm_in] high draft bracket
{

    // get the draft to satisfy the desired torque
    draft = this->Draft_From_Power(
                            power_desired,
                            draft_low,
                            draft_hi);

    // calculate the force
    force = this->pcRollbite->Force() *
                  rbite_width *
                  force_mult;

    return true;
}

float   cBaseStdD::Draft_From_Power(
                      float power_desired,      // [KW] desired power
                      float draft_lo,           // [mm_in] low draft bracket
                      float draft_hi)           // [mm_in] high draft bracket
{
    float                   save_draft = this->draft;
    cMathUty::status_type   cmstatus;
    float                   desired_draft;
    float                   xacc;
    float                   zacc;

    //-----------------------------------------------------------------
    // Make sure that we have valid power calculations on rollbite.
    //-----------------------------------------------------------------
    if ( !pcRollbite->Pwr_Valid() )
    {
        EMSG << "Draft_From_Torque(): Rollbite power is invalid"
             << END_OF_MESSAGE;
        return 0.0;
    }

    //-----------------------------------------------------------------
    // Work out the X & Z accuracy that can be achieved.
    //-----------------------------------------------------------------
    xacc = pcRollbite->Precision()*draft_max;
    zacc = pcRollbite->Precision()*power_desired*2.0;

    //-----------------------------------------------------------------
    // Call X_For_YZ to solve F(X,Y) - Z = 0.
    //-----------------------------------------------------------------
    cmstatus = cMathUty::X_For_YZ_NR(       // returns calculation status
                        desired_draft,      // OUT desired X, ( draft )
                        FN_Calculate_Power, // IN  pointer to caller's function
                        (void *)(this),     // IN  void ptr, for use by function
                        0.0,                // IN  Y, not used here
                        power_desired,      // IN  Z, desired power
                        draft_lo,           // IN  X low, draft low
                        draft_hi,           // IN  X high, draft high
                        xacc,               // IN  absolute accuracy for draft
                        zacc);              // IN  absolute accuracy for power

    // restore the draft on the stand
    this->draft = save_draft;

    // restore the stand state
    this->Operate();

    //-----------------------------------------------------------------
    // Check return status and return to caller.
    //-----------------------------------------------------------------
    if ( cmstatus != cMathUty::cmuty_valid )
    {
        EMSG << "Error " << (const char*)cMathUty::Image(cmstatus) 
             << " in cMathUty::X_for_YZ"
             << END_OF_MESSAGE;
        return 0.0;
    }

    return desired_draft;

} // End cBaseStdD::Draft_From_Power()


//-------------------------------------------------------------------------
// TENSION ABSTRACT:
// Calculate the tension applied to the exit piece.  The user must supply
// a function on the derived stand to calculate something other than zero.
//-------------------------------------------------------------------------
float cBaseStdD::Tension(void)
{
    return 0.0;
}

//-------------------------------------------------------------------------
// FLOWSTRESS ABSTRACT:
// Calculate the mean flowstress in the rollbite.  The user must supply
// a function on the derived stand to calculate something other than zero.
//-------------------------------------------------------------------------
float cBaseStdD::Flowstress(void)
{
    EMSG << "Need to provide a flowstress implementation"
         << END_OF_MESSAGE;
    return 0.0;
}

//-------------------------------------------------------------------------
// SPEED ABSTRACT:
// Calculate the roll peripheral speed and motor rpm.  The user may
// override this on the derived stand to suite his specific purposes.
//-------------------------------------------------------------------------
void    cBaseStdD::Speed(void)
{
    cBaseMillStdD::Speed();

} // End cBaseStdD::Speed()


//-------------------------------------------------------------------------
// VOLUME_FLOW ABSTRACT:
// Calculate the volume flow.  The user may override this on the derived
// stand to suite his specific purposes.
//-------------------------------------------------------------------------
float   cBaseStdD::Volume_Flow()
{
    float volume_flow = pcEnBasePceD->pcBasePce->mass_flow *
                        rbite_width *
                        Physcon.mmpm_inpft / Physcon.vel_time;

    return volume_flow;


} // End cBaseStdD::Volume_Flow()


//---------------------------------------------------------------
// Update_RollBite_Quantities() ABSTRACT
//
// Update rollbite quantities on the stand
//      Volume flow
//      Arc of contact
//      Draft compensation
//      Slip
//      Roll on roll force
//      Strip force
//      Bearing power loss
//      Deformation power
//      Friction power
//      Reduction power
//      Tension power
//      Power torque - Torque calculated from power & speed
//      Shaft power
//      Torque
//      Per unit torque
//---------------------------------------------------------------
bool    cBaseStdD::Update_RollBite_Quantities()
{
    //---------------------------------------------------------------
    // NOTE: volume flow on the piece has units of
    //           [minor_length**2]*[major_length/time]
    //       To get total power for deformation, friction and tension
    //       need to use volume flow in units of
    //           [minor_length**3/minor_time]
    //       Therefore convert volume flow as indicated
    //---------------------------------------------------------------
    volume_flow = Volume_Flow();

    //-----------------------------------
    // Calculate force related quantities
    //-----------------------------------
    arcon       = pcRollbite->Arcon();                  // arc of contact length
    defrad      = pcRollbite->Deform_Diameter()/2.0;    // deformed roll radius
    draft_comp  = this->Draft_Comp();                   // draft comp
    slip        = pcRollbite->Slip();                   // forward slip

    force_ror   = 0.0;                                  // roll on roll force
    force_strip = pcRollbite->Force() *
                  rbite_width *
                  force_mult;                           // strip force
    power_bear  = pcBaseStd->pcMtr->bear_loss *
                  force_strip *
                  speed;                                // bearing power loss
    power_def   = pcRollbite->Deform_Pwr() *
                  volume_flow *
                  power_mult;                           // deformation power
    power_fri   = pcRollbite->Friction_Pwr() *
                  volume_flow *
                  power_mult;                           // friction power
    power_red   = power_def + power_fri;                // reduction power
    power_ten   = pcRollbite->Tension_Pwr() *
                  volume_flow;                          // tension power

    torque_rb   = pcRollbite->Torque() * rbite_width;   // torque from rollbite calcs


    power_torque = power_def + power_fri +
                   power_ten + power_bear;

    if (power_torque > 0.0)
    {
        power_torque = power_torque /
                       pcBaseStd->pcMtr->effi_gear;  // shaft power
    }
    else
    {   // regeneration
        power_torque = power_torque *
                       pcBaseStd->pcMtr->effi_gear;  // shaft power
    }

    // power shaft and power torque are same - represents power
    // required at the motor shaft.
    power_shaft = power_torque;

    // Convert calculated shaft power to torque
    torque = pcBaseStd->pcMtr->Torque ( power_shaft,
                                        rpm );

    // Convert rated power (with overload factor) to rated torque
    float available_torque =
              pcBaseStd->pcMtr->Torque ( pcBaseStd->pcMtr->thd_ovrl *
                                         pcBaseStd->pcMtr->power_rate,
                                         pcBaseStd->pcMtr->base_rpm );

    // per unit rolling torque relative to rated torque
    torque_pu = torque /
                available_torque;

    //-----------------------------------------
    // calculate max torque at motor shaft
    //-----------------------------------------
    torque_max = pcBaseStd->pcMtr->Torque_Limit();

    return true;

} // end Update_RollBite_Quantities()

//---------------------------------------------------------------------------
// Length() ABSTRACT:
//
// Calculate the exit piece length.  User may supply a function on the
// derived stand if he chooses to override the calculation on the base stand.
//---------------------------------------------------------------------------
float cBaseStdD::Length(void)
{
    return  (pcEnBasePceD->thick * pcEnBasePceD->length * eff_ent_width) /
            (pcExBasePceD->thick * pcExBasePceD->width);
}

//---------------------------------------------------------------------
// Initial_Exit_Dimensions() ABSTRACT:
//
// Calculate piece exit dimensions from entry side dimensions and
// draft.
//---------------------------------------------------------------------
bool    cBaseStdD::Initial_Exit_Dimensions()
{
    //-------------------------------------------------------------
    // Calculate stand exit piece thickness and oxide thickness
    //-------------------------------------------------------------
    pcExBasePceD->thick        = pcEnBasePceD->thick - draft;
    pcExBasePceD->thick_oxide_top  = pcEnBasePceD->thick_oxide_top *
                                     pcExBasePceD->thick/pcEnBasePceD->thick;
    pcExBasePceD->thick_oxide_bot  = pcEnBasePceD->thick_oxide_bot *
                                     pcExBasePceD->thick/pcEnBasePceD->thick;

    if ( pcExBasePceD->thick <= 0.0 )
    {
        EMSG << "Stand " << (const char*)objName()
             << " bad exit thickness " << pcExBasePceD->thick
             << END_OF_MESSAGE;

        return false;
    }

    //-------------------------------------------------------------
    // Calculate per-unit draft
    //-------------------------------------------------------------
    draft_pu = draft / pcEnBasePceD->thick;

    //-------------------------------------------------------------
    // Calculate increase in width (spread) due to
    // horizontal draft.
    // Calculate exit piece width.
    //-------------------------------------------------------------
    // If the horizontal stand is dummied
    if( dummied || (draft == 0.0) )
    {
        // Set spread to zero
        spread = 0.0F;

    }
    else // stand is NOT dummied
    {
        // Calculate spread if required
        if ( pcBaseStd->calc_spread )
        {
            spread = pcWidth->Spread(
                        pcEnBasePceD->pcBasePce->family,    // family
                        pcEnBasePceD->thick,                // entry thickness
                        eff_ent_width,                      // entry width
                        draft,                              // horizontal draft
                        pcStdRollPrD->getAvgDiam() );       // avg wr diameter
        }

        // Modify width for spread and recovery
        pcExBasePceD->width = eff_ent_width + spread;

        // Set recovery to be realized by "next" stand to zero
        pcExBasePceD->recovery = 0.0;
    }

    //-----------------------------------------------------------------
    // Calculate stand exit length (based upon preservation of volume)
    //-----------------------------------------------------------------
    pcExBasePceD->length = Length();

    return true;

} // end Initial_Exit_Dimensions()


//-------------------------------------------------------------
// Force_Torque_Power() ABSTRACT:
//
// Calculate Force, Torque and Power.
//-------------------------------------------------------------
bool    cBaseStdD::Force_Torque_Power()
{
    enum  cMatl::matlEnum product     = mill().data().product ;
    float                 temperature = pcRbheat->Average(cTmpGrad::whole);

    if ( dummied || (draft == 0.0) )
    {
        fs = 0.0;
        arcon = 0.0;
        draft_comp = 1.0;
        force_ror = 0.0;
        force_strip = 0.0;
        force_mult = 1.0;
        torque = 0.0;
        torque_pu = 0.0;
        torque_rb = 0.0;
        power_bear = 0.0;
        power_def = 0.0;
        power_fri = 0.0;
        power_mult = 1.0;
        power_red = 0.0;
        power_ten = 0.0;
        power_shaft = 0.0;
        power_torque = 0.0;
        slip = 1.0;
        dtmp_avg = 0.0;
        heat_roll[0] = 0.0F;
        heat_roll[1] = 0.0F;
        duration = 0.0;

        return true;

    }

    // Calculate the mean flowstress in the rollbite.
    fs = Flowstress();

    // Calculate the exit piece tension.  It is assumed that the
    // exit piece dimensions have been determined before this call.
    pcExBasePceD->tension = Tension();

//RWH
    if ( speed <= Physcon.tol4 )
    {
        EMSG<<"Force_Torque_Power():  stand "
            <<(const char*)(this->objName())
            <<"  speed="<<speed
            <<END_OF_MESSAGE ;
    }
//RWH


    // Set data to calculate force, torque and power
    if ( !pcRollbite->Set_Input_Data(
        pcStdRollPrD->getAvgDiam(),               // roll diameter
        pcStdRollPrD->getHitchcock(),             // Hitchcock's constant
        pcEnBasePceD->thick,                      // entry thickness
        pcExBasePceD->thick,                      // exit thickness
        fs,                                       // mean flow stress
        temperature,                              // mean temperature
        pcEnBasePceD->tension,                    // entry tension
        pcExBasePceD->tension,                    // exit tension
        pcEnBasePceD->pcBasePce->pcMatl->Elasticity(
                        product,
                        pcEnBasePceD->pcBasePce->matl_code,
                        temperature ),            // strip elastic mod
        pcEnBasePceD->pcBasePce->pcMatl->Poisson(
                     product,
                     pcEnBasePceD->pcBasePce->matl_code ), // strip poisson ratio
        cof,                                      // mean coeff of friction
        speed/Physcon.vel_time,                   // surface velocity of roll
        pcEnBasePceD->pcBasePce->matl_code) )     // material code
    {
        EMSG << "Class: " << (const char*)Get_Class_Name() 
             << ", Object: " << (const char*)objName()
             << ", Bad status from pcRollbite->Set_Input_Data()"
             << END_OF_MESSAGE;

        return false;
    }

    //-------------------------------------------
    // Calculate force, torque, power, slip, etc.
    //-------------------------------------------
    if( !pcRollbite->Calculate_Power() )
    {
        EMSG << "Class: " << (const char*)Get_Class_Name() 
             << ", Object: " << (const char*)objName()
             << ", Bad status from pcRollbite->Calc_Power()"
             << END_OF_MESSAGE;

        return false;
    }
    else
    {
        //---------------------------------------------------------------
        // Update rollbite quantities on the stand
        //      Volume flow
        //      Arc of contact
        //      Draft compensation
        //      Slip
        //      Roll on roll force
        //      Strip force
        //      Bearing power loss
        //      Deformation power
        //      Friction power
        //      Reduction power
        //      Tension power
        //      Power torque - Torque calculated from power & speed
        //      Shaft power
        //      Torque
        //      Per unit torque
        //---------------------------------------------------------------
        Update_RollBite_Quantities();
    }

    //--------------------------------------------------------
    // if (feedback) then
    // Use measured power to calculate power deformation
    // and power friction components for roll bite
    // temperature calculations. The measured power is
    // divided into power deform and power friction
    // components in the same proportions as the predicted
    // power components.
    //--------------------------------------------------------
    // Note:: Typical power_meas term represents measured
    //        shaft power at motor which is identical to
    //        power shaft term calculated above.
    // -------------------------------------------------------
    if ( ( true == feedback ) && ( power_shaft > 0.0 ) )
    {
        lcl_pwr_def = power_def *
                          ( power_meas / power_shaft );
        lcl_pwr_fri = power_fri *
                          ( power_meas / power_shaft );
    }
    else
    {
        lcl_pwr_def = power_def;
        lcl_pwr_fri = power_fri;
    }

    return true;

}

//-------------------------------------------------------------
// DfDhx() ABSTRACT:
//
// Calculate DFDHX, the strip modulus.  dfdhx is updated on the
// stand and also returned as a float.
//
// NOTE: Since dfdhx is specified to be a positive number, we
// calculate change in force wrt change in draft (entry thickness
// constant), rather than change in force wrt change in exit
// thickness.
//
// NOTE: This method is subject to review.  We may decide to
// provide change in force wrt to change in exit thickness.
//
// NOTE: We provide two ways to calculate this quantity, the
// original way was to use the transfer function method on
// rollbite.  However it was found that the results may be in
// error due to the fact that the perturbation of draft
// introduces a change in flowstress.  So a calculation has
// been provided which uses a stand operate to update the
// state of the stand after a perturbation.  The original state
// is restored prior to exit.  The use_operate configured
// variable on the static stand is used to determine which
// method to use.
//
//-------------------------------------------------------------
float   cBaseStdD::DfDhx(void)   // [mton/mm_eton/in_kN/mm]
{
    float   result;


    //-----------------------------------------------------------------
    // Check for a dummied stand or no draft.  If so, dfdhx = 0.0.
    //-----------------------------------------------------------------
    if ( this->dummied || (this->draft <= Physcon.tol6) )
    {
        this->dfdhx = 0.0;
        return 0.0;
    }

    if ( pcBaseStd->use_operate )
    {
        float   save_draft = this->draft;
        float   perturb = 0.01F;
        float   dforce;
        float   ddraft;


        // Now perturb the temporary object around the operating point
        this->draft = (1.0 + perturb/2.0) * save_draft;
        this->Operate();
        dforce = this->force_strip;
        this->draft = (1.0 - perturb/2.0) * save_draft;
        this->Operate();
        dforce -= this->force_strip;
        ddraft = save_draft * perturb;

        // Update the results in the real object
        this->dfdhx = dforce / ddraft;

        // Restore the original state
        this->draft = save_draft;
        this->Operate();

        result = this->dfdhx;
    }
    else
    {
        //-----------------------------------------------------
        // Ask the rollbite object to update its DForce_DDraft
        // state.  Does not include effect on flowstress as a
        // result of the perturbation.
        //-----------------------------------------------------
        if( this->pcRollbite->Calculate_DForce_DDraft() )
        {
            //-----------------------------------------------------
            // Take the per unit width result from rollbite and
            // multiply by the effective rollbite entry width.
            //-----------------------------------------------------
            result =
                this->pcRollbite->DForce_DDraft() *
                this->rbite_width;
        }
        else
        {
            result = 0.0;
            EMSG
                << (const char*)(this->objName())
                << ": Strip Modulus INVALID"
                << END_OF_MESSAGE;
        }

        this->dfdhx = result;
    }

    return result;

} // END cBaseStd::DfDhx


//-------------------------------------------------------------
// DPwrDd() ABSTRACT:
//
// Calculate DPwrDd.  DPwrDd is returned as a float.
//
// NOTE: We provide two ways to calculate this quantity, the
// original way was to use the transfer function method on
// rollbite.  However it was found that the results may be in
// error due to the fact that the perturbation of draft
// introduces a change in flowstress.  So a calculation has
// been provided which uses a stand operate to update the
// state of the stand after a perturbation.  The original state
// is restored prior to exit.  The use_operate configured
// variable on the static stand is used to determine which
// method to use.
//
//-------------------------------------------------------------
float   cBaseStdD::DPwrDd(void)   // [mton/mm_eton/in_kN/mm]
{
    float   result;


    //-----------------------------------------------------------------
    // Check for a dummied stand or no draft.  If so, dpwrdd = 0.0.
    //-----------------------------------------------------------------
    if ( this->dummied || (this->draft <= Physcon.tol6) )
    {
        return 0.0;
    }

    if ( pcBaseStd->use_operate )
    {
        float   save_draft = this->draft;
        float   perturb = 0.01F;
        float   dpower;
        float   ddraft;


        // Now perturb the temporary object around the operating point
        this->draft = (1.0 + perturb/2.0) * save_draft;
        this->Operate();
        dpower = this->power_shaft;
        this->draft = (1.0 - perturb/2.0) * save_draft;
        this->Operate();
        dpower -= this->power_shaft;
        ddraft = save_draft * perturb;

        // Update the results in the real object
        result = dpower / ddraft;

        // Restore the original state
        this->draft = save_draft;
        this->Operate();

    }
    else
    {
        //-----------------------------------------------------
        // Ask the rollbite object to update its DPower_DDraft
        // state.  Does not include effect on flowstress as a
        // result of the perturbation.
        //-----------------------------------------------------
        if( this->pcRollbite->Calculate_DTorquepwr_DDraft() )
        {
            //-----------------------------------------------------
            // Take the per unit volume flow result from rollbite and
            // multiply by the volume flow.
            //-----------------------------------------------------
            result =
                this->pcRollbite->DTorquepwr_DDraft() *
                this->Volume_Flow();
        }
        else
        {
            result = 0.0;
            EMSG
                << (const char*)(this->objName())
                << ": Strip Modulus INVALID"
                << END_OF_MESSAGE;
        }
    }

    return result;

} // END cBaseStd::DPwrDd()
