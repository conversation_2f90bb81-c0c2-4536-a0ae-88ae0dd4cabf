       !!---------------------------------------------------------------
       !! Copyright (c) 2006 by
       !! Toshiba Mitsubishi-Electric Industrial Systems Corp. 
       !! TMGE Automation Systems LLC, U.S.A.
       !! Published in only a limited, copyright sense, and all
       !! rights, including trade secret rights are reserved.
       !!---------------------------------------------------------------
!!-----------------------------------------------------------------------------
!!-----------------------------------------------------------------------------
!!
!! ABSTRACT:
!!     Configure Static Width (cWidth) object.
!!
!!-----------------------------------------------------------------------------


!----------------------------------------------------------
! Create the static width object
!----------------------------------------------------------
class = cWidth;
    create_object;
        objname    = width;
        parentname = mill;
    end;
    cWidth = width;
        recov_sumit      = false;           ![-] if TRUE, use Sumitomo Recovery model
                                            !    if FALSE, use Simplified Recovery model
        sprd_sumit       = false;           ![-] if TRUE, use Sumitomo Spread model
                                            !    if FALSE, use Sedlaczek Spread model
        recov_dslope     = 1.0;             ![-] slope of recovery modifier
        grvmult_min      = 0.5;             ![-] minimum amount of groove multiplier
        grvmult_max      = 1.1;             ![-] maximum amount of groove multiplier
        grv_m            = 0.6;             ![-] slope of correction
        grv_b            = -0.05;           ![-] offset of correction
        bkl_mult         = 1.3;             ![-] buckling multiplier  from 1.2 to 1.5  to 1.2 20150504   from 1.2 to 1.3 by gxm 20150906
        max_wid_red_pts  = 10;              ![-] number of points for array
        wid_thk_rat_x    = 10,              ![-] width to thickness ratio array
!                    1     2     3     4     5     6     7     8     9    10
                     4.0, 6.6,  8.5,  10.0, 12.0, 15.0, 20.0, 25.0, 30.0, 50.0;   !changed by niu 20150504
        max_wid_red_y    = 10,              ![%] maximum width reduction array
!                    1     2     3     4     5     6     7     8     9    10
                    18.0,  11.0, 7.0,  5.5,  4.3,  2.9,  2.4,  1.8,  1.5,  0.8 ;  !changed by niu 20150921

        fm_sprd_min      = 37,              ![mm] minimum amount of FM spread for this steel family 
!                    0     1     2     3     4     5     6     7     8     9    10    11    12    13    14    15    16    17    18    19    20     21    22    23   24     25    26    27    28    29    30    31    32    33   34    35    36   
                   -7.5, -7.5, -7.5, -7.5, -7.5, -7.5, -7.5, -7.5, -7.5, -7.5, -7.5, -7.5, -7.5, -7.5, -7.5, -7.5, -7.5, -7.5, -7.5, -7.5, -7.5, -7.5, -7.5, -7.5, -7.5, -7.5 , -7.5, -7.5, -7.5, -7.5, -7.5, -7.5, -7.5, -7.5, -7.5, -7.5, -7.5;
        fm_sprd_max      = 37,              ![mm] maximum amount of FM spread for this steel family
!                    0     1     2     3     4     5     6     7     8     9    10    11    12    13    14    15    16    17    18     19    20     21    22    23   24     25    26    27    28    29    30    31    32    33    34    35    36   
                   12.5, 12.5, 12.5, 12.5, 12.5, 12.5, 12.5, 12.5, 12.5, 12.5, 12.5, 12.5, 12.5, 12.5, 12.5, 12.5, 12.5, 12.5, 12.5,  12.5, 12.5, 12.5, 12.5, 12.5, 12.5, 12.5 , 12.5, 12.5, 12.5, 12.5, 12.5, 12.5, 12.5, 12.5, 12.5, 12.5, 12.5;
        fm_sprd_coeff    = 148,              ![-] FM spread regression coefficients for this steel family
!                            const     sqrt(thick)     width        draft       family
                           23.77,      -3.18032,    -0.00806,    -0.11854,    !  0 
                           22.77,      -3.18032,    -0.00806,    -0.11854,    !  1 
                           23.77,      -3.18032,    -0.00806,    -0.11854,    !  2  
                           23.77,      -3.18032,    -0.00806,    -0.11854,    !  3 
                           24.77,      -3.18032,    -0.00806,    -0.11854,    !  4 
                           23.77,      -3.18032,    -0.00806,    -0.11854,    !  5 
                           26.77,      -3.18032,    -0.00806,    -0.11854,    !  6 
                           24.77,      -3.18032,    -0.00806,    -0.11854,    !  7 
                           25.77,      -3.18032,    -0.00806,    -0.11854,    !  8 
                           15.77,      -3.18032,    -0.00806,    -0.11854,    !  9 
                           23.77,      -3.18032,    -0.00806,    -0.11854,    ! 10 
                           23.77,      -3.18032,    -0.00806,    -0.11854,    ! 11 
                           23.77,      -3.18032,    -0.00806,    -0.11854,    ! 12 
                           25.77,      -3.18032,    -0.00806,    -0.11854,    ! 13 
	           23.77,      -3.18032,    -0.00806,    -0.11854,    ! 14 
	           26.77,      -3.18032,    -0.00806,    -0.11854,    ! 15 
	           26.77,      -3.18032,    -0.00806,    -0.11854,    ! 16 
	           19.77,      -3.18032,    -0.00806,    -0.11854,    ! 17 
                           26.77,      -3.18032,    -0.00806,    -0.11854;    ! 18 
	           23.77,      -3.18032,    -0.00806,    -0.11854,    ! 19 
	           24.77,      -3.18032,    -0.00806,    -0.11854,    ! 20 
	           23.77,      -3.18032,    -0.00806,    -0.11854,    ! 21 
                           22.77,      -3.18032,    -0.00806,    -0.11854;    ! 22                            
	           26.77,      -3.18032,    -0.00806,    -0.11854,    ! 23 
	           20.77,      -3.18032,    -0.00806,    -0.11854,    ! 24 
	           22.77,      -3.18032,    -0.00806,    -0.11854,    ! 25 
	           19.77,      -3.18032,    -0.00806,    -0.11854,    ! 26  added 2013.12.11
                           26.77,      -3.18032,    -0.00806,    -0.11854;    ! 27  added 2013.12.11
	           23.77,      -3.18032,    -0.00806,    -0.11854,    ! 28  added 2013.12.11
	           24.77,      -3.18032,    -0.00806,    -0.11854,    ! 29  added 2013.12.11
	           23.77,      -3.18032,    -0.00806,    -0.11854,    ! 30  added 2013.12.11
			   
                           22.77,      -3.18032,    -0.00806,    -0.11854;    ! 31  added 2013.12.11                         
	           26.77,      -3.18032,    -0.00806,    -0.11854,    ! 32  added 2013.12.11
	           20.77,      -3.18032,    -0.00806,    -0.11854,    ! 33  added 2013.12.11
	           22.77,      -3.18032,    -0.00806,    -0.11854,    ! 34  added 2013.12.11    
                           22.77,      -3.18032,    -0.00806,    -0.11854;    ! 35  added 2013.12.11                         
	           26.77,      -3.18032,    -0.00806,    -0.11854,    ! 36  added 2013.12.11
                        
        recov_mod       = 37,              ![-] recovery modifier
!                    0     1     2     3     4     5     6     7     8     9    10    11    12    13    14    15    16    17    18   19   20   21   22   23   24   25    26   27   28   29   30   31   32   33  34   35   36   
                   0.83, 0.87, 0.85, 0.87, 0.85, 0.87, 0.83, 0.83, 0.83, 0.82, 0.85, 0.87, 0.83, 0.85, 0.83, 0.83, 0.83, 0.83, 0.86,0.83,0.83,0.86,0.85,0.83,0.83,0.83,0.83,0.83,0.83,0.83,0.85,0.83,0.83,0.83,0.83,0.83,0.83 ;
        sprd_mod        = 37,              ![-] spread modifier
!                    0     1     2     3     4     5     6     7     8     9    10    11    12    13    14    15    16    17    18   19   20   21   22   23   24   25    26   27   28   29   30   31   32   33  34   35   36   
                   0.32, 0.32, 0.32, 0.32, 0.32, 0.32, 0.32, 0.32, 0.32, 0.26, 0.32, 0.33, 0.32, 0.32, 0.32, 0.32, 0.32, 0.32, 0.32,0.32,0.32,0.32,0.32,0.32,0.32,0.32,0.32,0.32,0.32,0.32,0.32,0.32,0.32,0.32,0.32,0.32,0.32;
    end;
end;
