//-----------------------------------------------------------------------------
//
// ABSTRACT:
//      This class contains methods that calculates Edger related data.
//
//
//     FUNCTION                 DESCRIPTION
//     -----------------------  -----------------------------------------------
//
//-----------------------------------------------------------------------------

#define EDG_CXX

//---------------------
// system include files
//---------------------

//---------------------
// records include files
//---------------------
#include "pdi.hxx"
#include "pdichem.hxx"
#include "ragp.hxx"
#include "sgp.hxx"
#include "rsu_features.hxx"

//---------------------
// mds include files
//---------------------
#include "alarm.hxx"
#include "flowstress.hxx"
#include "mathuty.hxx"
#include "motion.hxx"
#include "objhash.hxx"
#include "physcon.hxx"
#include "rollbite.hxx"
#include "stdrollpr.hxx"
#if INCLUDE_GAP
    #include "stretch.hxx"
#endif
#include "utility.hxx"

//---------------------
// shared include files
//---------------------
#include "pce.hxx"

//---------------------
// rsu include files
//---------------------
#include "edg.hxx"
#include "feedback.hxx"
#include "pass.hxx"
#include "sched.hxx"
#include "setup.hxx"

#ifdef WIN32
    #ifdef _DEBUG
    #define new DEBUG_NEW
    #endif
    #pragma warning(disable: 4244)  // double to float conversion (NT thinks constants are doubles)
#endif

// Diagnostic level specific to this file
//static const cGlobal::DiagnosticCodeEnum diagLvl(cGlobal::Setup);
static const int diagLvl = 0;

// Data schema for the cEdg class.
static cSchema::schema_type cEdg_schema[]=
{
    //Next  Enum  Schema details                            Fmt  Units        Comment
    //====  ====  ========================================  ==== ===========  ==================================================
#if  INCLUDE_AWC
    LINK_TO_SCHEMA("cAWC","cAWC")
#else
    LINK_TO_SCHEMA("cBaseEdg","cBaseEdg")
#endif
    // derived edger
    { NULL, NULL, SCHEMA_T(cEdg,float,chock_max_lmt),       "",  "mm_in",     "Max chock openning - max center line openning of rolls" },
    { NULL, NULL, SCHEMA_T(cEdg,int,max_ps_edg),            "",  "",          "Maximum number of passes to be run on this standalone edger" },

    { 0 }   // terminate list
};

// Link all the schema's together
cSchema::schema_name_type cEdg::sSchema[]=
{
    {
        "cEdg",                             // name
        sizeof(cEdg),                       // size
        cEdg_schema,                        // schema
        false,                              // packed
        true,                               // allow ptr
        false,                              // Read only
        "Static Edg configuration",         // comment
        0                                   // offset to config data
    },

    { 0 } // terminate list
};

// Data schema for the cEdgD class.
static cSchema::schema_type cEdgD_schema[]=
{
    //Next  Enum  Schema details                            Fmt  Units        Comment
    //====  ====  ========================================  ==== ===========  ==================================================
#if  INCLUDE_AWC
    LINK_TO_SCHEMA("cAWCD","cAWCD")
#else
    LINK_TO_SCHEMA("cBaseEdgD","cBaseEdgD")
#endif

    { NULL, NULL, SCHEMA_T(cEdgD,float,fs_mult),            "",  "",                  "Flow stress multiplier" },
    { NULL, NULL, SCHEMA_T(cEdgD,float,recovery_mlt),       "",  "",                  "Edging recovery multiplier"},
    { NULL, NULL, SCHEMA_T(cEdgD,bool, force_meas_vld),     "",  "",                  "Measured force valid" },
    { NULL, NULL, SCHEMA_T(cEdgD,float,wid_meas),           "",  "mm_in",             "Measured exit width" },
    { NULL, NULL, SCHEMA_T(cEdgD,bool, wid_meas_vld),       "",  "",                  "Measured width valid" },
    { NULL, NULL, SCHEMA_T(cEdgD,float,max_gap),            "",  "mm_in",             "maximum gap opening"},
    { NULL, NULL, SCHEMA_T(cEdgD,float,power_max_hd),       "",  "kw",                "maximum power limit on the head end - Impact"},
    { NULL, NULL, SCHEMA_T(cEdgD,float,mtr_volts),          "",  "V",                 "measured motor volts"},
    { NULL, NULL, SCHEMA_T(cEdgD,float,mtr_current),        "",  "amps",              "measured motor current"},
    { NULL, NULL, SCHEMA_T(cEdgD,float,power_ratio),        "",  "",                  "Power ratio (measured/repredicted)" },
    { NULL, NULL, SCHEMA_T(cEdgD,float,power_meas),         "",  "kw",                "measured power"},
    { NULL, NULL, SCHEMA_T(cEdgD,bool, power_meas_vld),     "",  "",                  "Measured power valid" },
    { NULL, NULL, SCHEMA_T(cEdgD,int,  pm_status),          "",  "",                  "Measured power status" },
    { NULL, NULL, SCHEMA_T(cEdgD,float,stack_exp),          "",  "mm_in",             "Stack centerline expansion" },
    { NULL, NULL, SCHEMA_T(cEdgD,float,wroll_wear),         "",  "mm_in",             "Work roll wear, top + bottom" },
    { NULL, NULL, SCHEMA_T(cEdgD,float,res_dft),            "",  "mm_in",             "Residula draft" },

#if INCLUDE_GAP
    { NULL, NULL, SCHEMA_T(cEdgD,float,stretch),            "",  "mm_in",             "Total stretch (housing + stack)" },
    { NULL, NULL, SCHEMA_T(cEdgD,float,gap),                "",  "mm_in",             "Screw gap opening" },
    { NULL, NULL, SCHEMA_T(cEdgD,bool, gap_vld),            "",  "",                  "Measured gap valid" },
    { NULL, NULL, SCHEMA_T(cEdgD,float,hmod),               "",  "mm/mton_in/eton",   "Housing modulus" },
    { NULL, NULL, SCHEMA_T(cEdgD,float,mmod),               "",  "mm/mton_in/eton",   "Mill modulus" },
    { NULL, NULL, SCHEMA_T(cEdgD,float,hs_stretch),         "",  "mm_in",             "Housing stretch" },
    { NULL, NULL, SCHEMA_T(cEdgD,float,st_deflection),      "",  "mm_in",             "Stack deflection" },
#endif
    { NULL, NULL, SCHEMA_T (cEdgD,float,dia_avg_er),        "",  "mm_in",             "edger roll average diameter" },
    { NULL, NULL, SCHEMA_T (cEdgD,float,mod_eqv_er),        "",  "kg/mm2_psi_mpa",    "edger roll equivalent Young's modulus" },
    { NULL, NULL, SCHEMA_T (cEdgD,float,wear_avg_er),       "",  "mm_in",             "edger roll mill centerline avgerage wear" },
    { NULL, NULL, SCHEMA_T1(cEdgD,float,len_er, 2),         "",  "m_ft",              "edger roll piece length rolled: 0 = opr, 1 = drv" },
    { NULL, NULL, SCHEMA_T1(cEdgD,int,  num_er, 2),         "",  "",                  "edger roll number pieces rolled: 0 = opr, 1 = drv" },
    { NULL, NULL, SCHEMA_T2(cEdgD,char, ser_num_er, 2, nameSize12),   "",  "",        "edger roll serial number: 0 = opr, 1 = drv" },
    { NULL, NULL, SCHEMA_T2(cEdgD,char, matl_typ_er,2, nameSize12),   "",  "",        "edger roll material type: 0 = opr, 1 = drv" },

#ifdef ALUMINUM_HSM
    { NULL, NULL, SCHEMA_T(cEdgD,bool, hard_dummied),            "",  "",                  "hard_dummied" },
#endif

    { NULL, NULL, SCHEMA_PO(cEdgD,cEdg,pcEdg),                          "",  "",         "Pointer to static derived edger" },
    { NULL, NULL, SCHEMA_PO(cEdgD,cPceD,pcEnPceD),                      "",  "",         "Pointer to entry piece state" },
    { NULL, NULL, SCHEMA_PO(cEdgD,cPceD,pcExPceD),                      "",  "",         "Pointer to exit piece state" },

    { 0 }   // terminate list
};

// Link all the schema's together
cSchema::schema_name_type cEdgD::sSchema[]=
{
    {
        "cEdgD",                            // name
        sizeof(cEdgD),                      // size
        cEdgD_schema,                       // schema
        false,                              // packed
        true,                               // allow ptr
        false,                              // Read only
        "Dynamic Edg configuration",        // comment
        0                                   // offset to config data
    },

    { 0 } // terminate list
};

//-----------------------------------------------------------------------------
// Static Edg Object
//-----------------------------------------------------------------------------
//-----------------------------------------------------------------------------
// cEdg CONSTRUCTOR ABSTRACT:
//   Edg constructor
//-----------------------------------------------------------------------------
cEdg::cEdg()
#if  INCLUDE_AWC
    : cAWC()
#else
    : cBaseEdg()
#endif
{
    Set_Class_Name("cEdg");
    Set_Schema("cEdg",sSchema);

    // Zero out member data
    Zero_Data(this, sizeof(cEdg), Get_Schema("cEdg"));

}

// Use this constructor if hash table support required
cEdg::cEdg( const MString        &objectName,
            const objTypEnum    objType,
            const objPosEnum    position,
            void                *pHash)
#if  INCLUDE_AWC
    : cAWC( objectName, objType, position, pHash )
#else
    : cBaseEdg( objectName, objType, position, pHash )
#endif
{
    Set_Class_Name("cEdg");
    Set_Schema("cEdg",sSchema);

    // Zero out member data
    Zero_Data(this, sizeof(cEdg), Get_Schema("cEdg"));

}

//-------------------------------------------------------------------------
// ~cEdg ABSTRACT:
//   Edg deconstructor
//-------------------------------------------------------------------------
cEdg::~cEdg()
{
}

//-------------------------------------------------------------------------
// cEdg ABSTRACT:
//   Edg copy constructor
//-------------------------------------------------------------------------
cEdg::cEdg (const cEdg& source)
#if  INCLUDE_AWC
    : cAWC( source )
#else
    : cBaseEdg( source )
#endif
{
}

//-------------------------------------------------------------------------
// OPERATOR = ABSTRACT:
//   Edg assignment operator
//-------------------------------------------------------------------------
cEdg& cEdg::operator= (const cEdg& source)
{
    if (this != &source)
    {
#if  INCLUDE_AWC
        cAWC::operator=(source);
#else
        cBaseEdg::operator=(source);
#endif
        num  = source.num;

        Copy_Data(this, (void *)&source,sizeof(cEdg),cEdg_schema);
    }
    return (*this);
}

//-------------------------------------------------------------------------
// LINKOBJ ABSTRACT
//   Edger link to static sub-objects
//-------------------------------------------------------------------------
bool cEdg::linkObj(const void        *pVoid,
                   const objTypEnum  objType,
                   const objPosEnum  objPos )
{
#if  INCLUDE_AWC
    return cAWC::linkObj(pVoid, objType, objPos);
#else
    return cBaseEdg::linkObj(pVoid, objType, objPos);
#endif
} // end cEdg::linkObj()


//-------------------------------------------------------------------------
// DUMP ABSTRACT:
//   Edg dump contents of the struct.
// The boolean composed if true indicates that the
// object should call the dump function for the
// objects that it contains.
//-------------------------------------------------------------------------
void cEdg::dump(const bool composed)
{
    char    obj_name[32];

    strcpy(obj_name, (const char *)(objName()));
    Dump_Data(stdout, "cEdg", this, 0, obj_name);

    if (composed)
    {
        DMSG(cAlarm::Dump) << " START DUMP OF Edg COMPOSED OBJECTS" << END_OF_MESSAGE;
        DMSG(cAlarm::Dump) << " END DUMP OF Edg COMPOSED OBJECTS" << END_OF_MESSAGE;
    }

} // end cEdg::dump

//-------------------------------------------------------------------------
// Create a new dynamic object.  Intended to be used
// where we have a static/dynamic pair so that the
// static object may create its dynamic equivalent
//-------------------------------------------------------------------------
cBase*  cEdg::Create_Dynamic_Object()
{
    cEdgD   EdgD;

    return EdgD.Create_Object();
}

cBase*  cEdg::Create_Dynamic_Object(
                    const MString&     objName,
                    const objTypEnum  objType,
                    const objPosEnum  objPosition,
                    void              *pHash,
                    const int         size,
                    void              *pVoid,
                    void              *pFbkVoid,
                    void              *pRaw,
                    void              *pAdapted)
{
    cEdgD   EdgD;

    return EdgD.Create_Object(
                objName,
                objType,
                objPosition,
                pHash, size, pVoid);
}

//-----------------------------------------------------------------------------
// Dynamic Edg Object
//-----------------------------------------------------------------------------
//-----------------------------------------------------------------------------
// cEdgD CONSTRUCTOR ABSTRACT:
//   Edg constructor
//-----------------------------------------------------------------------------
cEdgD::cEdgD()
#if  INCLUDE_AWC
    : cAWCD()
#else
    : cBaseEdgD()
#endif
    , pcEdg (NULL)
    , pcEnPceD (NULL)
    , pcExPceD (NULL)
#if INCLUDE_GAP
    , pcStretch (NULL)
#endif
{
    Set_Class_Name("cEdgD");
    Set_Schema("cEdgD",sSchema);

    // Zero out member data
    Zero_Data(this, sizeof(cEdgD), Get_Schema("cEdgD"));
    fs_mult = 1.0;
    groov_mult = 1.0;
    do_limchks = true;

}

// Use this constructor if hash table support required
cEdgD::cEdgD( const MString        &objectName,
              const objTypEnum    objType,
              const objPosEnum    position,
              void                *pHash)
#if  INCLUDE_AWC
    : cAWCD( objectName, objType, position, pHash )
#else
    : cBaseEdgD( objectName, objType, position, pHash )
#endif
    , pcEdg (NULL)
    , pcEnPceD (NULL)
    , pcExPceD (NULL)
#if INCLUDE_GAP
    , pcStretch (NULL)
#endif
{
    Set_Class_Name("cEdgD");
    Set_Schema("cEdgD",sSchema);

    int stdNum_save = stdNum;   // Preserve stdNum from Zero_Data

    // Zero out member data
    Zero_Data(this, sizeof(cEdgD), Get_Schema("cEdgD"));

    stdNum = stdNum_save;
    fs_mult = 1.0;
    groov_mult = 1.0;
    do_limchks = true;

    // Create the exit piece state associated with this edger and link it
    // up to the edger
    pcExPceD = new cPceD (objectName + (MString)"_pce", ot_undef, op_undef, pHash);
    linkObj(pcExPceD, ot_undef, op_exit);

    next_obj = pcExPceD;
    pcExPceD->previous_obj = this;

    // Find the static edger object
    char    *pName = strchr(objectName, '_') + 1;
    objHashType::ResultT    result;
    result = Objhash.cmnFind(pName);
    if (HashTableHlp::duplicate == result.status)
    {
        linkObj(result.data, ot_undef, op_undef);
    }
    else
    {
        EMSG << "No object named "
             << pName << " exists"
             << END_OF_MESSAGE;
    }

#if INCLUDE_GAP
    // Find the static stretch object
    // Expects name of the format xx_stretch where
    // xx is the name of the static edger object.
    char    buff[64];
    sprintf(buff, "%s_stretch", pName);
    result = Objhash.cmnFind((const char *)(buff));
    if (HashTableHlp::duplicate == result.status)
    {
        pcStretch = (cStretch *)(result.data);
    }
    else
    {
        EMSG << "No stretch object named "
             << buff << " exists"
             << END_OF_MESSAGE;
    }
#endif


}   // end cEdgD::cEdgD


//-------------------------------------------------------------------------
// ~cEdgD ABSTRACT:
//   EdgD deconstructor
//-------------------------------------------------------------------------
cEdgD::~cEdgD()
{
}

//-------------------------------------------------------------------------
// cEdgD ABSTRACT:
//   EdgD copy constructor
//-------------------------------------------------------------------------
cEdgD::cEdgD (const cEdgD& source)
#if  INCLUDE_AWC
    : cAWCD ( source )
#else
    : cBaseEdgD ( source )
#endif
    , pcEdg ( source.pcEdg)     // point to same static object
{
}

//---------------------------------------------------------------------------
// ASSIGN_STATE ABSTRACT:
//  Assigns state data from a source object to a destination object of
//  the same type.
//---------------------------------------------------------------------------
bool  cEdgD::Assign_State(cBase * pcDest, cBase * pcSource)
{
    //-----------------------------------------------------------------------
    // Check pointers.  Alarm and abort if source or destination pointer is
    //      NULL.
    //-----------------------------------------------------------------------
    if ( pcDest == NULL )
    {
        EMSG << "NULL pointer in pcDest"
             << END_OF_MESSAGE;
        return false;
    }
    if ( pcSource == NULL )
    {
        EMSG << "NULL pointer in pcSource"
             << END_OF_MESSAGE;
        return false;
    }
    //-----------------------------------------------------------------------
    // Check object types.  Alarm and abort if the source and destination
    //      objects are not identical.
    //-----------------------------------------------------------------------
    if ( pcDest->Get_Class_Name() != pcSource->Get_Class_Name() )
    {
        EMSG << "Cannot assign " << (const char *)pcSource->objName()
             << " to " << (const char *)pcDest->objName()
             << ".  Assignment aborted."
             << END_OF_MESSAGE;
        return false;
    }
    //-----------------------------------------------------------------------
    // Assign source to destination object.
    //-----------------------------------------------------------------------
    *((cEdgD *)(pcDest)) = *((cEdgD *)(pcSource));
    return true;

}   // end cEdgD::Assign_State()

//-------------------------------------------------------------------------
// OPERATOR = ABSTRACT:
//   EdgD assignment operator
//-------------------------------------------------------------------------
cEdgD& cEdgD::operator= (const cEdgD& source)
{
    if (this != &source)
    {
#if  INCLUDE_AWC
        cAWCD::operator=(source);
#else
        cBaseEdgD::operator=(source);
#endif
        Copy_Data(this, (void *)&source,sizeof(cEdgD),cEdgD_schema);
    }
    return (*this);
}

void cEdgD::zeroData(void)
{
    // Zero out member data

    Zero_Data(this, sizeof(cEdgD), Get_Schema("cEdgD"));
    fs_mult = 1.0;
    groov_mult = 1.0;
    do_limchks = true;

#if  INCLUDE_AWC
    cAWCD::zeroData();
#else
    cBaseEdgD::zeroData();
#endif
}

//-------------------------------------------------------------------------
// LINKOBJ ABSTRACT
//   AEdgD link to static sub-objects
//-------------------------------------------------------------------------
bool cEdgD::linkObj(const void        *pVoid,
                    const objTypEnum  objType,
                    const objPosEnum  objPos )
{
    cBase   *pcBase = (cBase *)(pVoid);
    bool     retValue (TRUE);
    if (!pVoid)
    {
        EMSG << "Passed child pointer is NULL - exiting" << END_OF_MESSAGE;
        return (retValue);
    }

    if ( Is_Base_Class("cEdg", pcBase) )
    {
        if (pcEdg)
        {
            pcEdg = (cEdg*) pVoid;
            retValue = false;
        }
        else
            pcEdg = (cEdg*) pVoid;
    }

#if  INCLUDE_AWC
    return cAWCD::linkObj(pVoid, objType, objPos) && retValue;
#else
    return cBaseEdgD::linkObj(pVoid, objType, objPos) && retValue;
#endif

} // end cEdgD::linkObj()


//-------------------------------------------------------------------------
// DUMP ABSTRACT:
//   EdgD dump contents of the struct.
// The boolean composed if true indicates that the
// object should call the dump function for the
// objects that it contains.
//-------------------------------------------------------------------------
void cEdgD::dump(const bool composed)
{
    char    obj_name[32];

    strcpy(obj_name, (const char *)(objName()));
    Dump_Data(stdout, "cEdgD", this, 0, obj_name);

    if (composed)
    {
    }
} // end cEdgD::dump

//---------------------------------------------------------------------
// Virtual function to allow the user to carry out post processing for
// a dynamic Edger.
//---------------------------------------------------------------------
bool        cEdgD::Post_Config(
                char *name,         // unused
                void *psStruct)     // unused
{
    pcEnPceD = (cPceD *)(previous_obj);

#if  INCLUDE_AWC
    return cAWCD::Post_Config( name, psStruct );
#else
    return cBaseEdgD::Post_Config( name, psStruct );
#endif
}

//-------------------------------------------------------------------------
// cEdgD::Operate ABSTRACT:
//
// This method updates the exit piece state, given the entry state
// information for a piece.
//
// Return "true" if the exit piece state is successfully updated.
//
// Return "false" if an error occurs.  In this case the exit piece state is
// undefined.
//
// Any exceptions are caught, alarmed and then thrown to a higher level
// exception handler.  The exit piece state is undefined if an exception
// is thrown.
//-------------------------------------------------------------------------
bool cEdgD::Operate(void)
{
#if  INCLUDE_AWC
    return cAWCD::Operate();
#else
    return cBaseEdgD::Operate();
#endif
}



//-------------------------------------------------------------------------
// FLOWSTRESS ABSTRACT:
// Calculate the mean flowstress in the rollbite.
//-------------------------------------------------------------------------
float cEdgD::Flowstress(void)
{
    cSched  *pcSched;                           // Sched pointer
    cPassD  *pcPassD;                           // Dynamic pass pointer
    cFlowstress::status_type  status;           // status from flow stress calcs
    double                    sigma = 0.0;      // [kgpmm2_lbpin2_MPa] calculated mean flow stress
                                                // The following mean flow stress values contain a
                                                // factor of 2/sqrt(3) to convert to plane stain
                                                // stress values
    float                     base_flows  = 0.0; // [kgpmm2_lbpin2_MPa] base flow stress
    float                     radius      = pcStdRollPrD->getAvgDiam(op_edger)/2.0; // [mm_in] work roll radius
    float                     temperature = pcEnPceD->temp_avg;

    //---------------------------------------------------------
    // Get a pointer to the schedule, which is a parent of the
    // pass, which is a parent of the dynamic stand.
    //---------------------------------------------------------
    if ( this->parent_obj == NULL )
    {
        EMSG << "Dynamic stand has no parent pass"
             << END_OF_MESSAGE;
        return 0.0;
    }
    pcPassD = (cPassD *)(this->parent_obj);
    if ( pcPassD->parent_obj == NULL )
    {
        EMSG << "Dynamic stand has no schedule parent of its pass parent"
             << END_OF_MESSAGE;
        return 0.0;
    }
    if ( pcPassD->parent_obj->Get_Class_Name() != "cSched" )
    {
        EMSG << "Top level parent is not of type cSched"
             << END_OF_MESSAGE;
        return 0.0;
    }
    pcSched = (cSched *)(pcPassD->parent_obj);

    //-------------------------------------------------------------------------
    // If input data is available, calculate the flow stress
    //-------------------------------------------------------------------------
    if ( (pcSched->pcPDI     != NULL) &&
         (pcSched->pcPDIChem != NULL) &&
         (pcSched->pcRAGP    != NULL) &&
         (pcSched->pcSGP     != NULL) &&
         (pcFlowstress       != NULL) )
    {
        float   ent_width = pcEnPceD->width;
        float   arcon = pcRollbite->Arc_Contact(radius, draft);
        float   influence;

        //---------------------------------------------------------------------
        // With the edger, we have a situation where the effect of edging does
        // not penetrate through the strip completely.  If we disregard this
        // we get values for flowstress that are too small.  Hence we try and
        // estimate the depth of influence and use this as the piece width
        // input to the flow stress calculation.
        //---------------------------------------------------------------------
        influence = 1.0*arcon;

        if ( ent_width > influence )
        {
            ent_width = influence;
        }

        //---------------------------------------------------------------------
        // If using the curve method, use the model table flow stress curve
        //---------------------------------------------------------------------
        if (pcSched->pcSGP->state.rm_use_curve)
        {
            //-----------------------------------------------------------------
            // Calculate sigma, the mean flow stress using curve method.
            //-----------------------------------------------------------------
            base_flows = pcSched->pcSGP->state.base_fs *
                         cMathUty::rlnint(
                                  &temperature,
                                  &pcSched->pcRAGP->state.fs_tmp[0],
                                  &pcSched->pcRAGP->state.fs_tmp_mult[0],
                                  (int *)&num_fs_pts );

            sigma = pcFlowstress->Mean_Strain_Flow (  // OUT flow stress adj for strain rate [pressure]
                        radius,                       // IN undeformed roll radius [minor_length]
                        radius,                       // IN deformed roll radius [minor_length]
                        ent_width,                    // IN strip entry thickness [minor_length]
                        ent_width-draft,              // IN strip exit thickness [minor_length]
                        draft/pcEnPceD->width,        // IN per unit draft [-]
                        speed,                        // IN roll surface speed [linear_speed]
                        base_flows,                   // IN base flow stress [pressure]
                        temperature );                // IN strip entry [temperature]
            sigma *= pcSched->pcPDI->state.rm_str_ratio;
        }
        else
        //---------------------------------------------------------------------
        // Use the theoretical calculation of flow stress
        //---------------------------------------------------------------------
        {
            //-----------------------------------------------------------------
            // Calculate the flow stress
            //-----------------------------------------------------------------
            double  xdyn, xstat, eps;    // throw away variables
            if ( !pcFlowstress->Mean_Flow_Stress(
                        status,                     // OUT [-] calculation status
                        sigma,                      // OUT [pressure] calculated mean flow stress
                        xdyn,                       // OUT [-] fraction dynamic recrystallized
                        xstat,                      // OUT [-] fraction static recrystallized
                        eps,                        // OUT [-] exit strain
                        pcExPceD->d_aust,           // OUT [micron] austenitic grain size this stand
                        mstrate,                    // OUT [1/sec] mean strain rate
                        radius,                     // IN  [mm_in] work roll radius
                        ent_width,                  // IN  [mm_in] strip entry thickness
                        ent_width-draft,            // IN  [mm_in] strip exit thickness
                        pcEnPceD->speed,            // IN  [mpmin_ftpmin_mpsec] exit piece speed
                        temperature,                // IN  [C_F] average strip temperature
                        pcEnPceD->eps,              // IN  [-] entry strain
                        pcEnPceD->xdyn,             // IN  [-] fraction dynamic recrystallized
                        pcEnPceD->xstat,            // IN  [-] fraction static recrystallized
                        pcFlowstress->d_aust[pcEdg->num-1], // IN  [micron] austenitic grain size this stand
                        pcEnPceD->d_aust,           // IN  [micron] austenitic grain size previous stand
                        0.0,                        // IN  [sec] incubation time
                        pcSched->pcPDIChem->state.percnt_c,  // IN  [-] percent carbon
                        pcSched->pcPDIChem->state.percnt_mn, // IN  [-] percent manganese
                        pcEnPceD->ferrite,          // IN  [-] ferrite fraction
                        pcSched->pcSGP->state.sigma_fer_mult ) ) // IN [-] ferrite flowstress modifier
            {
                EMSG << objName() << " Error in calculation of flow stress for edger "
                     << pcEdg->num
                     << END_OF_MESSAGE;
            }
        }
    }
    else
    {
        EMSG << "cEdgD::Flowstress() - Input data records not available for flowstress calcs"
             << END_OF_MESSAGE;
        return 0.0;
    }

    //---------------------------------------------------
    // Account for in-bar pass hardness multipliers.
    // Use pass hardness multiplier at setup time only -
    // NOT used for feedback requests.
    //---------------------------------------------------
    int pass_index = ((cPassD *)(this->parent_obj))->ps_idx;
    if ( !this->feedback &&
         pcSched->pcSetupD->fstpas > 1 )
    {
        sigma *= pcSched->pcFeedbackD->hardness_mlt[pass_index];
    }

    float   ret_fs;

    ret_fs = fs_mult * sigma;

    if ( ret_fs < Physcon.tol2 )
    {
        EMSG << "cEdgD::Flowstress() - Flowstress is too small"
             << END_OF_MESSAGE;
    }

    return ret_fs;

} // End cEdgD::Flowstress()


//-------------------------------------------------------------------------
// SPEED ABSTRACT:
// Calculate the roll peripheral speed and motor rpm.  The user may
// override this on the derived stand to suite his specific purposes.
//-------------------------------------------------------------------------
void    cEdgD::Speed(void)
{
    float   approx_slip = 0.0;

    // ---------------------------------------------------------------
    // Set the required piece input data for
    // the roll pressure distribution calculations.
    // ---------------------------------------------------------------
    if ( pcRollbite->Force_Valid() && (slip != 0.0) )
    {
        // use last calculated slip
        approx_slip = slip;
    }
    else
    {
        // first time, use approximation for slip
        approx_slip =
            pcRollbite->Approximate_Slip(
            pcStdRollPrD->getAvgDiam(op_edger),      // roll diameter
            pcExBasePceD->width,             // exit thickness
            pcEnBasePceD->width);            // entry thickness
    }

    // Calculate roll peripheral speed
    // only in setup mode. In feedback mode
    // speed will be initialized to measured
    // roll speed
    if ( !this->feedback )
    {
        speed = pcEnPceD->speed * pcEnPceD->width /
                ((pcEnPceD->width - draft) * approx_slip);
    }

    // Calculate motor rpm for power to torque conversions
    rpm = Physcon.mmpm_inpft * speed *
              pcBaseEdg->gearat * Physcon.secpmin /
              (Physcon.pi *
               pcStdRollPrD->getAvgDiam(op_edger) *
               Physcon.vel_time);

} // End cEdgD::Speed()



//-------------------------------------------------------------------------
// VOLUME_FLOW ABSTRACT:
// Calculate the volume flow.  The user may override this on the derived
// stand to suite his specific purposes.
//-------------------------------------------------------------------------
float   cEdgD::Volume_Flow()
{
    float volume_flow = pcEnPceD->thick * pcEnPceD->speed *
                        pcEnPceD->width *
                        Physcon.mmpm_inpft / Physcon.vel_time;

    return volume_flow;

} // End cStdD::Volume_Flow()


//-------------------------------------------------------------------------
// Entry_Linear_Speed() ABSTRACT:
// Exit_Linear_Speed()  ABSTRACT:
//
// Convert motor rpm to linear strip speed. [m/min_ft/min_m/sec]
//-------------------------------------------------------------------------
float           cEdgD::Entry_Linear_Speed(float rpm)
{
    return Motion.Linear_Speed(
                    this->pcStdRollPrD->getAvgDiam(op_edger),
                    this->draft_comp * rpm /
                    this->pcEdg->gearat);
}

float           cEdgD::Exit_Linear_Speed(float rpm)
{
    return Motion.Linear_Speed(
                    this->pcStdRollPrD->getAvgDiam(op_edger),
                    this->slip * rpm /
                    this->pcEdg->gearat);
}


//-------------------------------------------------------------------------
// Circ() ABSTRACT:
//
// Calculate the edger work roll circumference corresponding to one
// revolution of the motor in major length units. [m_ft]
// vel_time is added to ensure correct speed calculations in all unit
// systems - metric, modified metric, english and SI.
//-------------------------------------------------------------------------
float           cEdgD::Circ(void)
{
    return Physcon.pi *
           this->pcStdRollPrD->getAvgDiam(op_edger) * Physcon.vel_time /
           this->pcEdg->gearat / Physcon.secpmin / Physcon.mmpm_inpft;
}


//-------------------------------------------------------------------------
// GapOfs ABSTRACT:
// Calculate the roll thermal and wear and gap offset.
//-------------------------------------------------------------------------
bool   cEdgD::GapOfs(void)
{
    //-----------------------------------------------------
    // Calculate horizontal stand stretch and screw offsets
    // and position.
    //-----------------------------------------------------
    if( !this->dummied )
    {
        //------------------------------------------------------------------
        // Get Edger Roll stack expansion.
        // Note: Edger Roll Heating is considered to be zero and RTWM model
        //       does not support Edger thermal expansion.
        //------------------------------------------------------------------
        this->stack_exp = 0.0F;

        //-----------------------------------------------------------------
        // Get Edger Roll stack wear.
        //   getWearCent() returns roll wear since last zeroing.
        //   getWearCentTot() returns total roll wear
        //   wroll_wear is average wear for top and bottom roll.
        //-----------------------------------------------------------------
        /*
        this->wroll_wear = ( this->pcWRPairD->getWearCentTot(rpos_top)
                            -this->pcWRPairD->getTopRollD()->rollD.wearZero +
                             this->pcWRPairD->getWearCentTot(rpos_bottom)
                            -this->pcWRPairD->getBotRollD()->rollD.wearZero ) / 2.0F;
        */
        this->wroll_wear = ( this->pcStdRollPrD->getWearCent(rpos_op_side)
                            + this->pcStdRollPrD->getWearCent(rpos_drive_side) ) / 2.0F ;

        //------------------------------------------------------------
        // Calc. the screw offset for this stand  =
        //                  +   Roll stack expansion
        //                  +   (Work roll wear - zeroing offset)
        //                  +   (BU roll wear - zeroing offset)
        //-------------------------------------------------------------
        this->gapoff = this->stack_exp +
                       this->wroll_wear;
    }
    return true;
}   // end  cEdgD::GapOfs(void)


#if INCLUDE_GAP
//-------------------------------------------------------------------------
// GAP ABSTRACT:
// Calculate the roll gap.  The user must supply a function on the derived
// stand to calculate something other than zero.
//-------------------------------------------------------------------------
float   cEdgD::Gap(void)
{

    if ( pcStretch == NULL )
    {
        EMSG << "Stretch object is NULL for object " << this->objName()
             << END_OF_MESSAGE;
        return 0.0;
    }

    //-----------------------------------------------------
    // Calculate horizontal stand stretch and screw offsets
    // and position.
    //-----------------------------------------------------
    if( !this->dummied && this->draft > 0.0F )
    {
        //-----------------------------------------------------
        // Calculate stack expansion, roll wear and gap offset.
        //-----------------------------------------------------
        if ( !this->GapOfs() )
        {
            ;
        }

        //---------------------------------------
        // Calculate the housing and mill stretch
        //---------------------------------------
        this->pcStretch->Calc_Stretch(
                             this->hs_stretch,
                             this->st_deflection,
                             this->hmod,
                             this->mmod,
                             this->force_strip,
                             this->pcEnPceD->thick,
                             1.0);

        this->stretch = hs_stretch + st_deflection;

        //--------------------------------------------
        // Calc. screw reference = stand_exit_gauge
        //                        - stand stretch
        //                        + stand screw offset
        //--------------------------------------------
        this->gap = this->pcExPceD->width -
                    this->stretch +
                    this->gapoff;

    }
    else
    {
         // (stand is dummied)
        this->gap = this->pcExPceD->width +
                    this->pcEdg->dmy_ofs;
        this->stretch = 0.0;
        // If draft is zero, set the dummied flag
        if ( this->draft <= 0.0F)
        {
            this->dummied = true;
        }
    }

    return this->gap;

}

//-------------------------------------------------------------------------
// Max_Gap ABSTRACT:
// Calculate the maximum roll gap openning.  The user must supply a function
// on the derivedstand to calculate something other than zero.
//-------------------------------------------------------------------------
float   cEdgD::Max_Gap(void)
{

    //-----------------------------------------------------
    // Calculate the maximum roll gap openning.
    //-----------------------------------------------------
    this->max_gap = this->pcEdg->chock_max_lmt - this->pcStdRollPrD->getAvgDiam(op_edger);

    if ( this->max_gap < 0.0F )
    {
        this->max_gap = 0.0F;
    }

    return this->max_gap;

}   // end cEdgD::Max_Gap(void)
#endif


//-------------------------------------------------------------
// DPwrDd() ABSTRACT:
//
// Calculate DPwrDd.  DPwrDd is returned as a float.
//
// NOTE: We provide two ways to calculate this quantity, the
// original way was to use the transfer function method on
// rollbite.  However it was found that the results may be in
// error due to the fact that the perturbation of draft
// introduces a change in flowstress.  So a calculation has
// been provided which uses a stand operate to update the
// state of the stand after a perturbation.  The original state
// is restored prior to exit.  The use_operate configured
// variable on the static stand is used to determine which
// method to use.
//
//-------------------------------------------------------------
float   cEdgD::DPwrDd(void)   // [mton/mm_eton/in_kN/mm]
{
    float   result;


    //-----------------------------------------------------------------
    // Check for a dummied stand or no draft.  If so, dpwrdd = 0.0.
    //-----------------------------------------------------------------
    if ( this->dummied || (this->draft <= Physcon.tol6) )
    {
        return 0.0;
    }

    if ( pcBaseEdg->use_operate )
    {
        float   save_draft = this->draft;
        float   perturb = 0.01F;
        float   dpower;
        float   ddraft;


        // Now perturb the temporary object around the operating point
        this->draft = (1.0 + perturb/2.0) * save_draft;
        this->Operate();
        dpower = this->power_shaft;
        this->draft = (1.0 - perturb/2.0) * save_draft;
        this->Operate();
        dpower -= this->power_shaft;
        ddraft = save_draft * perturb;

        // Update the results in the real object
        result = dpower / ddraft;

        // Restore the original state
        this->draft = save_draft;
        this->Operate();

    }
    else
    {
        //-----------------------------------------------------
        // Ask the rollbite object to update its DPower_DDraft
        // state.  Does not include effect on flowstress as a
        // result of the perturbation.
        //-----------------------------------------------------
        if( this->pcRollbite->Calculate_DTorquepwr_DDraft() )
        {
            //-----------------------------------------------------
            // Take the per unit volume flow result from rollbite and
            // multiply by the volume flow.
            //-----------------------------------------------------
            result =
                this->pcRollbite->DTorquepwr_DDraft() *
                this->Volume_Flow();
        }
        else
        {
            result = 0.0;
            EMSG
                << this->objName()
                << ": Strip Modulus INVALID"
                << END_OF_MESSAGE;
        }
    }

    return result;

} // END cBaseStd::DPwrDd()



//-----------------------------------------------------------------------------
// COPY_ROLL_DATA ABSTRACT:
//    Function to copy static / dynamic roll data to dynamic STD object.
//-----------------------------------------------------------------------------
void cEdgD::Copy_Roll_Data( void )

{   // Begin of COPY_ROLL_DATA function

    return;

}   // End of COPY_ROLL_DATA function

