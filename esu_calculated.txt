//-----------------------------------------------------------------------------
//
// ABSTRACT:
//     This file implements the Calculate() method for generating an edger
//     schedule.
//
//
//     FUNCTION/PROCEDURE/TASK  DESCRIPTION
//     -----------------------  -----------------------------------------------
//   dynamic
//     Calculate                Generate the edger setup.    													生成立辊设置 
//     Dist_RMWid_Vern          Distribute RM width vernier into RM drafting stands						将粗轧宽度修正量分配到各机架
//     Distribute_Dft_Error     Distribute the width error to edger drafts									将宽度误差分配到立辊轧机
//     Executive                Main edger drafting executive module.										主要立辊轧机执行模块
//     Get_Fm_Spread            Calculate both total and individual FM spreads								计算精轧展宽和总体展宽
//     Initialize               Initialize edger drafts and other related data.										初始化轧边机道次（Edger Drafts）及相关数据（如初始宽度、机架参数等），为后续计算提供基础数据。
//     Setup                    Calculate edger setup															设置计算：具体计算轧边机的各项设置参数（如各道次的压下量、速度等），生成最终的轧制方案。
//     Set_Draft_Limits          Assign min/max edger drafts for all passes									道次限制设定：为每个轧制道次分配最小和最大压下量（Draft），确保工艺参数在设备能力和质量要求范围内。
//     Set_Esu_Status           Set Edger Setup status														状态设置：设置轧边机设置（Esu, Edger Setup）的状态（如就绪、运行、完成等），用于系统监控和流程同步。
//     Set_Tgt_Width            Calculate the RM exit target width											目标宽度计算：根据工艺要求和轧件特性，计算轧件出口的目标宽度（RM Exit Target Width），作为各道次调整的基准。
//     Total_Edg_Draft          Calculate total edger drafts in RM											总压下量计算：计算轧件在轧边机中的总压下量（Total Draft），用于评估整体变形程度和工艺合理性。
//
//-----------------------------------------------------------------------------

//----------------------
// system include files
//----------------------
#include <stdlib.h>

//----------------------
// mds include files
//----------------------
#include "alarm.hxx"
#include "objchain.hxx"
#include "objhash.hxx"
#include "rollpair.hxx"
#include "utility.hxx"
#include "width.hxx"
#include "mathuty.hxx"

//----------------------
// shared include files
//----------------------
#include "pce.hxx"
#include "pyrosensor.hxx"
#include "thicknesssensor.hxx"
#include "widthsensor.hxx"

//----------------------
// rsu include files
//----------------------
#include "sensorcfg.hxx"
#include "edg.hxx"
#include "esu.hxx"
#include "pass.hxx"
#include "rmload.hxx"
#include "sched.hxx"
#include "setup.hxx"
#include "stand.hxx"

//----------------------
// records include files
//----------------------
#include "esys.hxx"
#include "pdi.hxx"
#include "ramp.hxx"
#include "rapp.hxx"                 //add by xubin 2012.10.9
#include "samp.hxx"
#include "rsu_features.hxx"

#ifdef WIN32
    #ifdef _DEBUG
    #define new DEBUG_NEW
    #endif
    #pragma warning(disable: 4244) // double to float conversion (NT thinks constants are doubles)
#endif


// Diagnostic level specific to this file
static const cAlarm::DiagnosticCodeEnum diagLvl(cAlarm::ESU);


//-----------------------------------------------------------------
// Draft_Callback() ABSTRACT:
//
// Called from Setup() to update calculated quantities.
//
//-----------------------------------------------------------------
static  bool    Draft_Callback(void *ptr)
{
    int     iseg     = pcObjChain->Body_Index();
    cSched  *pcSched = (cSched *)(ptr);

    //---------------------------------------------------------------------
    // Walk objects from first to last pass.
    //---------------------------------------------------------------------
    pcSched->pcStandD->Schedule(
                            pcSched->pcSetupD,
                            pcSched,
                            pcSched->pcSupPassD,
                            iseg,
                            pcSched->pcSetupD->fstpas,
                            pcSched->pcSetupD->lstpas);

    return true;
}


//-----------------------------------------------------------------
// Schedule() ABSTRACT:
//
// Generate the edger setup schedule
//
//-----------------------------------------------------------------
bool cESUD::Calculate(cSched *pcSched, cPassD **pcPassD)
{

    //return false;

    return this->Initialize(pcSched);

}


//-----------------------------------------------------------------------------
// cESUD::Distribute_Dft_Error - distribute draft error to specified number of  passes.
//
// Abstract:
//   This function distributes the draft error  supplied over the passes specified using the supplied draft load distribution.
//   Note-1:  Draft will be added to existing pass drafts using draft loading  values.
//   Note-2:  Draft error to be distributed can be positive or negative.
//   Note-3:  If draft is to be added, include only passes which are NOT already at high draft limit.
//   Note-4:  If draft is to be removed, include only passes which are NOT  already at low draft limit.
//   Note-5:  If no drafting capacity is available, a valid status is returned to the caller. This condition will be flaged as  overdraft or underdraft later on.
//
// Argument list:
//    pcSupPassD      - OUT/IN  array of passes
//    fstpas          -     IN  first pass
//    lstpas          -     IN  last pass
//    dft_error       -     IN  draft error to be distributed
//
// Return:
//    bool            - status
//-----------------------------------------------------------------------------
bool cESUD::Distribute_Dft_Error( cPassD** pcSupPassD,
                                 int      fstpas,
                                 int      lstpas,
                                 float    dft_error )
{
    //-------------------------------------------------------------------------
    // Local variable definition and initialization											局部变量初始化
    //-------------------------------------------------------------------------
    int     iseg   = pcObjChain->Body_Index();
    float   dfterr = dft_error;     // width error to distribute                                                           	用于存储待分配的轧制误差，初始化为输入的 dft_error
                                    // (initialized to input value)

    float   totload;                // sum of all pass loads											用于存储所有符合条件的道次的负载总和。
    float   dft;                    // local draft (fcn of load)											根据负载计算的局部轧制量。
    float   draft;																		当前道次的轧制量。
    float   ddraft;                 // local draft to distribute										当前待分配的局部轧制误差。

    int     ps;                     // pass index
    int     iter;                   // loop iteration index											

    //-------------------------------------------------------------
    // Iteratively distribute the draft input between the specified passes according to the loading pattern provided.
    //-------------------------------------------------------------
    for( iter=0; iter<=20; iter++ )														迭代分配轧制误差
    {
        //---------------------------------------------------------------------
        // Determine total pass loading for all passes in the specified range.
        //  If draft error is to be added, include only passes which are NOT already at high draft limit.
        //  If draft error is to be removed, include only passes which are NOT already at low draft limit.
        //---------------------------------------------------------------------
        totload = 0.0F;																	计算总负载

        cEdgD   *pcEdgD;

        for( ps=fstpas; ps<=lstpas; ps++)
        {
            if( (pcEdgD=pcSupPassD[ps]->Drafting_EdgD(iseg)) != NULL )
            {
                // If draft error to be added is positive (value input)
                if( dft_error > 0.0F )
                {

                    // if draft = draft_min and draft error (dfterr) is less
                    // than zero, then this edger can not reduce the draft
                    // search for the next one
                    if( pcEdgD->draft_min == pcEdgD->draft &&
                        dfterr < 0.0F )
                    {
                        ;
                    }
                    else if( pcEdgD->draft_max == pcEdgD->draft &&
                        dfterr > 0.0F )
                    {
                        ;
                    }
                    else if( pcEdgD->draft < pcEdgD->draft_max )
                    {
                        totload = totload + pcEdgD->load_act;
                    } // end if

                } // end if
                else if( pcEdgD->draft > pcEdgD->draft_min )
                {
                    totload = totload + pcEdgD->load_act;
                } // end else if
                else
                {
                    // set draft to draft_min
                    pcEdgD->draft = pcEdgD->draft_min;
                }

            } // end if

        } //end loop

        // If no load available, return to calling routine						检查总负载，如果总负载为 0，表示所有道次都已达到最大或最小轧制量限制，输出警告信息并返回 true。
        if( totload <= 0.0F )
        {
            // Edger UNDERDRAFT or OVERDRAFT condition
            // Output alarm that no load available
            DMSG(diagLvl)
                << pcSupPassD[0]->pcExPceD[iseg]->pcPce->prod_id
                << " All Edging passes at limits, "
                << " ESU status= " << cEdgD::Image(this->esu_status)
                << END_OF_MESSAGE;

            return true;
        } // end if( totload <= 0.0F )

        // Distribute draft error proportionally to pass loading				按负载比例分配轧制误差
        ddraft = dfterr;

//AEB        for( ps=lstpas; ps>=fstpas; ps-- )
        for( ps=fstpas; ps<=lstpas; ps++ )
        {

            if( (pcEdgD=pcSupPassD[ps]->Drafting_EdgD(iseg)) != NULL  &&
                ((pcEdgD->draft != pcEdgD->draft_max && ddraft > 0.0F) ||
                 (pcEdgD->draft != pcEdgD->draft_min && ddraft < 0.0F)) )
            {
                // Determine draft for this pass
                // Note: dft and ddraft are the width_error type
                dft = ddraft * pcEdgD->load_act/totload;

                // Check draft against the residual draft from the previous iteration
                if ( (dft< 0.0F && pcEdgD->res_dft> 0.0F) ||
                     (dft> 0.0F && pcEdgD->res_dft< 0.0F) )
                {
                    DMSG(diagLvl)
                        << pcSupPassD[0]->pcExPceD[iseg]->pcPce->prod_id
                        << ", dft= " << dft
                        << ", res_dft= " << pcEdgD->res_dft
                        << ", ps= " << ps
                        << END_OF_MESSAGE;

                    if ( fabs(dft) > fabs(pcEdgD->res_dft) )
                    {
                        dft += pcEdgD->res_dft;
                        pcEdgD->res_dft = 0.0F;
                    }
                    else
                    {
                        pcEdgD->res_dft += dft;
                    }
                }

                //-----------------------------------------------------------
                // NOTE: at initialization time, draft should be set to zero  and efficiency to 1.0
                //-----------------------------------------------------------
                draft = dft + pcEdgD->draft;

                if( draft > pcEdgD->draft_max )
                {
                    pcEdgD->res_dft = draft - pcEdgD->draft_max;
                    dft   -= pcEdgD->res_dft;
                    draft = pcEdgD->draft_max;
                }
                else if ( draft < pcEdgD->draft_min )
                {
                    pcEdgD->res_dft = draft - pcEdgD->draft_min;
                    dft   -= pcEdgD->res_dft;
                    draft = pcEdgD->draft_min;
                }

                pcEdgD->draft = draft;

                dfterr = dfterr - dft;

            } // end if
        } //end loop

        DMSG(diagLvl)
            << pcSupPassD[0]->pcExPceD[iseg]->pcPce->prod_id
            << ", dft_error= " << dft_error
            << ", dfterr= " << dfterr
            << ", iter= " << iter
            << END_OF_MESSAGE;

        // If draft distributed successfully            									检查分配结果，如果剩余待分配的轧制误差小于指定的精度，则认为分配成功，返回 true。
        if ( fabs(dfterr) < this->pcESU->accuracy )
        {
            return true;   // NORMAL return path
        } // end if

    } //end loop

    // Alarm
    cEdgD   *pcEdgD;														迭代失败处理，如果迭代 20 次后仍未成功分配轧制误差，输出错误信息并返回 false。
    for( ps=fstpas; ps<=lstpas; ps++ )
    {
        if( (pcEdgD=pcSupPassD[ps]->Drafting_EdgD(iseg)) != NULL )
        {
            DMSG(-diagLvl)
                << pcSupPassD[0]->pcExPceD[iseg]->pcPce->prod_id
                << "ps= " << ps << ", dMin= " << pcEdgD->draft_min
                << ", draft= " << pcEdgD->draft
                << ", dMax= " << pcEdgD->draft_max
                << ", res_dft= " << pcEdgD->res_dft
                << END_OF_MESSAGE;
        }
    }

    // Draft could not be successfully distributed
    EMSG << pcSupPassD[0]->pcExPceD[iseg]->pcPce->prod_id
         << "  ESU::Distribute_Dft_Error: unable to distribute draft "
         << END_OF_MESSAGE;
    EMSG << pcSupPassD[0]->pcExPceD[iseg]->pcPce->prod_id
         << "  ESU::Distribute_Dft_Error: dfterr=" << dfterr
         << ", accuracy= " << this->pcESU->accuracy
         << END_OF_MESSAGE;

    return false;

} // end Distribute_Dft_Error


//-----------------------------------------------------------------------------
// cESUD::Total_Edg_Draft - This method returns total edger drafts in RM			遍历指定范围内的轧制道次，累加每个道次的实际轧制量、最小轧制量和最大轧制量，最终将结果存储在引用参数中，方便调用者获取轧边机的总轧制量相关信息
//
// Argument list:
//    pcSupPassD      - OUT/IN  array of passes
//    fstpas          -     IN  first pass
//    lstpas          -     IN  last pass
//    drafts          - OUT     total edger drafts
//    drafts_min      - OUT     total edger min drafts
//    drafts_max      - OUT     total edger max drafts
//
// Return:
//    void            - void
//-----------------------------------------------------------------------------
void cESUD::Total_Edg_Draft( cPassD** pcSupPassD,
                             int      fstpas,
                             int      lstpas,
                             float&   drafts,
                             float&   drafts_min,
                             float&   drafts_max )
{
    int     iseg    = pcObjChain->Body_Index();
    cEdgD   *pcEdgD;

    drafts     = 0.0F;
    drafts_min = 0.0F;
    drafts_max = 0.0F;
    for ( int ps = fstpas; ps <= lstpas; ps++ )
    {
        if( (pcEdgD=pcSupPassD[ps]->Drafting_EdgD(iseg)) != NULL )
        {
            drafts     += pcEdgD->draft;
            drafts_min += pcEdgD->draft_min;
            drafts_max += pcEdgD->draft_max;
        }
    }

    return;

}   // end cESUD::Total_Edg_Draft()


//-----------------------------------------------------------------------------
// cESUD::Set_Esu_Status - This method assigns ESU status.
//
// Argument list:
//    pcSched         - OUT/IN  pointer to Sched
//    wid_error       -     IN  width error to be removed
//
// Return:
//    void            - void
//-----------------------------------------------------------------------------
void cESUD::Set_Esu_Status( cSched *pcSched,
                            float   wid_error )
{
    //---------------
    // Set esu status
    //---------------
    int     ps;
    int     iseg        = pcObjChain->Body_Index();
    float   draft       = 0.0F;
    float   draft_hi    = 0.0F;
    float   draft_lo    = 0.0F;
    cEdgD   *pcEdgD;

    //------------------------------
    // Check for all edgers dummied
    //------------------------------
    if ( pcSched->pcSetupD->edgs_dummied )         								处理虚拟轧边机状态（Edgers Dummied），当所有轧边机被标记为 “虚拟”（不参与实际轧制）时，
																			直接根据宽度误差绝对值与精度阈值（pcESU->accuracy）的关系设置状态，无需计算道次参数。
    {
        // All edgers are dummied, return without calling setup
        if ( wid_error <= (-2.0F * this->pcESU->accuracy) )
        {
            // ESU underdraft
            this->esu_status = el_underdraft;
        }
        else if ( wid_error >= (2.0F * this->pcESU->accuracy) )
        {
            // ESU overdraft
            this->esu_status = el_overdraft;
        }
        else
        {
            // ESU no limit
            this->esu_status = el_nolim;
        }

        return;
    }

    for ( ps = pcSched->pcSetupD->fstpas; ps <= pcSched->pcSetupD->lstpas; ps++ )					遍历道次并检查限制条件
    {
        if( (pcEdgD=pcSched->pcSupPassD[ps]->Drafting_EdgD(iseg)) != NULL )
        {
            draft    += pcEdgD->draft;
            draft_hi += pcEdgD->draft_max;
            draft_lo += pcEdgD->draft_min;

            if (  pcEdgD->draft > pcSched->pcSupPassD[ps]->pcPass->vdft_max )
            {
                pcEdgD->lim = el_draftmax;
            }
            else if ( pcEdgD->draft < pcSched->pcSupPassD[ps]->pcPass->vdft_min  )
            {
                pcEdgD->lim = el_draftmin;
            }
            else
            {
                if ( (el_draftmax == pcEdgD->lim) || (el_draftmin == pcEdgD->lim) )
                {
                    pcEdgD->lim = el_nolim;
                }
            }

            if ( pcEdgD->force_strip >= pcEdgD->pcEdg->force_max )
            {
                pcEdgD->lim = el_frcmax;
            }

        }
    }   // end for ( ps = fstpas; ps <= lstpas; ps++ )

    if ( draft >= draft_hi )													设置全局 ESU 状态
    {
        //--------------------------------------------------------
        // overdraft condition, not able to make the target width.
        // actual width will be wider than the target width.
        //--------------------------------------------------------
        this->esu_status = el_overdraft;
    }
    else if ( draft <= draft_lo )
    {
        //--------------------------------------------------------
        // underdraft condition, not able to make the target width.
        // actual width will be narrower than the target width.
        //--------------------------------------------------------
        this->esu_status = el_underdraft;
    }
    else
    {
        this->esu_status = el_nolim;
    }

    return;

}   // end Set_Esu_Status


//-----------------------------------------------------------------------------
// cESUD::Set_Draft_Limit - this function assigns the min and max drafts
// Argument list:
//    pcSupPassD      - OUT/IN  pass
//
// Return:
//    bool            - status
//-----------------------------------------------------------------------------
bool cESUD::Set_Draft_Limit( cPassD* pcSupPassD)
{
    // Local variables
    int     iseg        = pcObjChain->Body_Index();
    float   buckl_lim   = 0.0F; // Buckling draft limit
    float   taper_lim   = 0.0F; // Tapered edger draft limit
    cEdgD   *pcEdgD;

        // Establish dummy pattern - based on load distribution
        // Note: a zero entry in load distribution dummies pass!
        if( (pcEdgD=pcSupPassD->Drafting_EdgD(iseg)) != NULL )
        {
            // initialize dynamic draft
            pcEdgD->draft = 0.0F;

            if ( pcEdgD->dummied || 0.0F == pcEdgD->load_act )
            {
                ;
            }
            else
            {
                //-------------------------------------------------------------
                // Calculate the buckling draft limit of the edger,
                // and modifie edger draft_max if necessary.
                //-------------------------------------------------------------
                buckl_lim = pcWidth->Bkl_Lmt(										计算屈曲限制并调整最大轧制量
                                            pcEdgD->pcEnPceD->thick,
                                            pcEdgD->pcEnPceD->width );
                if ( pcEdgD->draft_max >= buckl_lim )
                {
                    pcEdgD->draft_max = buckl_lim;

                    // Set the draft_max limit to Buckling limit
                    pcEdgD->lim = el_buckl;
                    DMSG(diagLvl) << "Set_Draft_Limit() el_buckl,buckl_lim=" << buckl_lim
                             << END_OF_MESSAGE;
                }

                //-------------------------------------------------------------
                // Check for tapered edger. If edger is tapered, then calculate
                // the tapered edger max draft limit "taper_lim", and modify
                // draft_max accordingly
                //-------------------------------------------------------------
                if ( pcEdgD->pcEdg->tapered )
                {
                    taper_lim = pcSupPassD->pcPass->vdft_max							处理锥形轧边机并调整最大轧制量
                              - pcWidth->Tpr_Cor(
                                pcEdgD->pcEnPceD->thick,
                                pcEdgD->pcEdg->angle );

                    if ( pcEdgD->draft_max >= taper_lim
                       && taper_lim > 0.0F )
                    {
                        pcEdgD->draft_max = taper_lim;

                        // Set the draft_max limit to tapered edger draft limt
                        pcEdgD->lim = el_tapered;
                    }
                }   // end pcEdgD->pcEdg->tapered

                // Check low limit consistency											检查最小和最大轧制量的一致性
                if( pcEdgD->draft_max <=  pcEdgD->draft_min )
                {
                    pcEdgD->draft_max = pcEdgD->draft_min;

                    pcEdgD->lim = el_draftmin;
                } // end if

            } // end if ( pcEdgD->dummied ||

        }   // end pcEdgD != NULL


    return true;

}   //  end cESUD::Set_Draft_Limit

//-----------------------------------------------------------------------------
// cESUD::Set_Draft_Limit - this function assigns the min and max drafts for
//                         all passes.
//
// Argument list:
//    pcSupPassD      - OUT/IN  array of passes
//    fstpas          -     IN  first pass
//    lstpas          -     IN  last pass
//
// Return:
//    bool            - status
//-----------------------------------------------------------------------------
bool cESUD::Set_Draft_Limits( cPassD** pcSupPassD, int fstpas, int lstpas )
{
    int     ps;
    for ( ps = fstpas; ps <= lstpas; ps++ )
    {
        Set_Draft_Limit(pcSupPassD[ps]);
    }
    return true;
}
//-----------------------------------------------------------------------------
// cESUD::Assign_Load - this function assigns edger load for						为立辊各道次分配负载比例（load_act），作为压下量分配的权重依据
//                         all passes.
//
// Argument list:
//    pcSupPassD      - OUT/IN  array of passes
//    fstpas          -     IN  first pass
//    lstpas          -     IN  last pass
//
// Return:
//    bool            - status
//-----------------------------------------------------------------------------
bool cESUD::Assign_Load( cPassD** pcSupPassD, int fstpas, int lstpas )
{
    // Local variables
    int     iseg    = pcObjChain->Body_Index();
    int     ps;
    cEdgD   *pcEdgD;

    cSched  *pcSched = (cSched *)(this->parent_obj);

    if ( pcSched == NULL )
    {
        EMSG << "Error, no parent to cESUD class"
             << END_OF_MESSAGE;
        return false;
    }
    if ( _stricmp(pcSched->Get_Class_Name(), "cSched") != 0 )
    {
        EMSG << "Error, cSched is not the parent of cESUD"
             << END_OF_MESSAGE;
        return false;
    }

    float   *load_fac = new float [lstpas+1];								1. 初始化负载系数数组

    //-----------------------------------------------------------------
    // Translate the EML load distribution from ESys.						2.转换EML负载分布（eload_eml）
    //-----------------------------------------------------------------
    cRMLoad::Translate_Load(
                    3,                                  // IN  number of load points
                    lstpas-fstpas+1,                    // IN  number of passes
                    pcSched->pcSetupD->eload_eml,       // IN  input npoint load distribution
                    load_fac);                          // OUT translated load distribution

    //-----------------------------------------------------------------
    // Include map load and individual stand load.							 3. 结合Map负载和立辊负载
    //-----------------------------------------------------------------
    for ( ps = fstpas; ps <= lstpas; ps++ )
    {
        //-------------------------------------------------------------
        // Include map load.
        //-------------------------------------------------------------
        load_fac[ps-fstpas] *= pcSupPassD[ps]->pcPass->vload_dft;

        //-------------------------------------------------------------
        // Include edger load.
        //-------------------------------------------------------------
        if( (pcEdgD=pcSupPassD[ps]->Drafting_EdgD(iseg)) != NULL )
        {
            int     idx = pcEdgD->pcEdg->num-1;
            load_fac[ps-fstpas] *= pcSched->pcESys->state.load_op[idx];
        }
    }

    //-----------------------------------------------------------------
    // Normalize the load distribution.									4. 归一化负载系数
    //-----------------------------------------------------------------
    cRMLoad::Normalize_Load(
                    lstpas-fstpas+1,    // IN  number of passes
                    load_fac);          // IN/OUT normalized load distribution

    //-----------------------------------------------------------------
    // Preset load_init & load_act on the edgers.
    //-----------------------------------------------------------------
    for ( ps = fstpas; ps <= lstpas; ps++ )
    {
        if( (pcEdgD=pcSupPassD[ps]->Drafting_EdgD(iseg)) != NULL )
        {
            pcEdgD->load_init =
            pcEdgD->load_act = load_fac[ps-fstpas];
        }
    }

    delete [] load_fac;

    return true;

}   //  end cESUD::Assign_Load


//-----------------------------------------------------------------------------
// cESUD::Get_Fm_Spread - this function calculates the total FM spread at RMX
//                       temperature and proportions cold inividual FM spread
//                       amounts on drafting FM stands. This function could be
//                       used in Setup and Feedback mode.
//
// Argument list:
//    pcSupPassD      - OUT/IN  array of passes
//    pcSensorCfgD    - OUT/IN  pointer to dynamic common config
//
// Return:
//    float           - fm_spread
//-----------------------------------------------------------------------------
float cESUD::Get_Fm_Spread( cSensorCfgD* pcSensorCfgD ,
						    cSched*      pcSched)     // added by mjh 20150319
{
    //------------------------
    // Define local variables
    //------------------------
    float   fx_hot_thick;
    float   fx_hot_width;

    //-----------------------------------------------------
    // If this is a feedback call, use measured quantities	获取热态厚度和宽度（反馈模式优先）当 pcFMXWidthD->feedback 为真时，直接使用传感器测量的实时厚度和宽度，用于动态调整轧制参数。
    //-----------------------------------------------------
    if ( pcSensorCfgD->pcFMXWidthD->feedback )
    {
        fx_hot_thick = pcSensorCfgD->pcFMXWidthTargtD->thick;
        fx_hot_width = pcSensorCfgD->pcFMXWidthTargtD->width;
    }
    else
    {
        fx_hot_thick = pcSensorCfgD->pcFMXThickD->targ;
        fx_hot_width = pcSensorCfgD->pcFMXWidthD->targ;
    }

    //-------------------------------------------------------------------------
    // Translate all dimensions to RMX temperature												
    // Note: fxhaim, fxwaim and tgt_wid_ofs are cold and htwofc  is hot value.
    //  thick = fxhaim * rmx_exp;
    //  width = ( fxwaim * fmx_exp + tgt_wid_ofs + htwofc ) * rmx_exp/fmx_exp;
    //  fm_draft = rmx_thick - thick;
    //-------------------------------------------------------------------------
    float   lin_exp_rat =  pcSensorCfgD->pcRMXWidthTargtD->Expansion()	温度膨胀补偿（转换为 RMX 温度尺寸）
                         / pcSensorCfgD->pcFMXWidthTargtD->Expansion();

    float   thick    = fx_hot_thick * lin_exp_rat;
    float   width    = fx_hot_width * lin_exp_rat;
    float   fm_draft = pcSensorCfgD->pcRMXWidthTargtD->thick - thick;	计算 FM 压下量（Draft），RMX 目标厚度（pcRMXWidthTargtD->thick）与转换后的热态厚度（thick）之差

    // Predict FM spread amount
    float   fm_spread = pcWidth->Fm_Spread( pcSensorCfgD->pcRMXWidthTargtD->pcPce->family,
                                            thick,
                                            width,
                                            fm_draft,
		            pcSched->pcRAPP->state.fm_sprd_coeff[0],      //Added by mjh 20150319
		            pcSched->pcRAPP->state.fm_sprd_coeff[1],      //Added by mjh 20150319
		            pcSched->pcRAPP->state.fm_sprd_coeff[2],      //Added by mjh 20150319
		            pcSched->pcRAPP->state.fm_sprd_coeff[3]       //Added by mjh 20150319
											);
    return  fm_spread;

}   // cESUD::Get_Fm_Spread


//-----------------------------------------------------------------------------
// cESUD::Set_Tgt_Width - this function calculates the RM exit target width
//                       based on the FMX target width translated to RMX
//                       temperature and amount of FM spread.
//
// Argument list:
//    pcSensorCfgD    - OUT/IN  pointer to dynamic common config
//    fm_spread       -     IN  FM spread amount
//
// Return:
//    bool            - status
//-----------------------------------------------------------------------------
bool cESUD::Set_Tgt_Width(cSched *pcSched, cSensorCfgD* pcSensorCfgD, float fm_spread )
{

    // Check for exit feedback - for case of FM entry edger
    if( pcSched->wrk[cXlevt::wrk_exit_fbk]  > 0 )
    {
        DMSG(-diagLvl) << (const char *)(pcSched->objName())
                        << " bypass Set_Tgt_Width, call is after RMX for F0 Edger FFWD"
                        << END_OF_MESSAGE;

        return true;
    }

    //-------------------------------------------------------------------------
    if ( prd_plate != pcSched->pcPDI->state.product )	换钢种轧制
    {
//======================================================================================================start add by xubin 2012.10.9
		
		DMSG(-diagLvl) 
			<< "pcSensorCfgD->pcRMXWidthD->targ= " 													
			<< pcSensorCfgD->pcRMXWidthD->targ
            << END_OF_MESSAGE;

		if ( pcSched->pcRAMP->adapted_state.family_prv != pcSched->pcRAPP->state.family )	通过fwid_off_mult参数动态调整宽度偏移量，避免因材料特性差异导致的目标宽度偏差。
		{
		     pcSensorCfgD->pcRMXWidthD->targ =					targ 是实际应用于控制系统的目标值，用于设定粗轧机的宽度控制参数，在后续的轧制过程中，														
                      pcSensorCfgD->pcFMXWidthTargtD->pcPce->fx_hot_width *				控制系统会依据这个 targ 值来对轧机进行调整，从而保证轧件在粗轧出口达到预期的宽度。
                      pcSensorCfgD->pcRMXWidthTargtD->Expansion() /					在代码里，targ 的计算是在考虑各种因素之后，对 rmx_width 进行细化和修正的结果。
                      pcSensorCfgD->pcFMXWidthTargtD->Expansion()
                    - fm_spread
                    + ( pcSched->pcRAMP->state.fmx_wid_vern * ( 1 - pcSched->pcRAPP->state.fwid_off_mult ) + 
					pcSched->pcRAPP->state.fmx_wid_off * pcSched->pcRAPP->state.fwid_off_mult ) *
                      pcSensorCfgD->pcRMXWidthTargtD->Expansion() /
                      pcSensorCfgD->pcFMXWidthTargtD->Expansion();
		}
		else
		{
		 	 pcSensorCfgD->pcRMXWidthD->targ =
                      pcSensorCfgD->pcFMXWidthTargtD->pcPce->fx_hot_width *
                      pcSensorCfgD->pcRMXWidthTargtD->Expansion() /
                      pcSensorCfgD->pcFMXWidthTargtD->Expansion()
                    - fm_spread
                    + pcSched->pcRAMP->state.fmx_wid_vern *
                      pcSensorCfgD->pcRMXWidthTargtD->Expansion() /
                      pcSensorCfgD->pcFMXWidthTargtD->Expansion();
		}
//======================================================================================================end add by xubin 2012.10.9
#if INCLUDE_FME_EDGER				考虑精轧立辊
        int iseg   = pcObjChain->Body_Index();
        int lstpas = pcSched->pcSetupD->lstpas;
        //----------------------------------------------------------
        // Calculate new RMX target width, considering a F0 Edger.
        // Convert the (draft - recov) to RMX target temperature.
        // RXW = FXW - fm_spread + F0_edger net effect
        // FEW = RXW - F0_edger net effect
        //----------------------------------------------------------
        if ( pcSched->pcSupPassD[lstpas]->pcExPceD[iseg]->Expansion() > Physcon.tol2 )
        {
            float   rx_fe_exp_ratio =
                pcSensorCfgD->pcRMXWidthTargtD->Expansion()
              / pcSched->pcSupPassD[lstpas]->pcExPceD[iseg]->Expansion();

            if ( pcSched->pcSupPassD[lstpas]->pcEnEdgD[iseg]->draft > Physcon.tol2 )
            {
                float   net_edg_effect =
                    pcSched->pcSupPassD[lstpas]->pcEnEdgD[iseg]->draft
                    - pcSched->pcSupPassD[lstpas]->pcEnEdgD[iseg]->pcExPceD->recovery;

                // RMX target width
                pcSensorCfgD->pcRMXWidthD->targ += net_edg_effect * rx_fe_exp_ratio;

                // FME target width
                pcSched->pcSetupD->fme_tgt_wid =
                    pcSensorCfgD->pcRMXWidthD->targ / rx_fe_exp_ratio
                    - net_edg_effect;
            }
            else
            {
                // FME target width
                pcSched->pcSetupD->fme_tgt_wid =
                    pcSensorCfgD->pcRMXWidthD->targ / rx_fe_exp_ratio;
            }
        }
#endif
    }
    else						不换钢种轧制
    {
        pcSensorCfgD->pcRMXWidthD->targ =
            pcSched->pcSuPce->rx_cold_width *
            pcSensorCfgD->pcRMXWidthTargtD->Expansion();
    }

    //-------------------------------------------------------------------------
    // Note: By taking out "rmx_wid_vern", "targ" will
    //       become the model and actual width - in other words we can
    //       use "targ" as width gauge reference.
    //       rmx_wid_vern is being distributed in pcESU->Initialize() function
    //       amongst the StdD->wid_offset
    //-------------------------------------------------------------------------

    return  true;

}   // end cESUD::Set_Tgt_Width


//-----------------------------------------------------------------------------
// cESUD::Dist_RMWid_Vern - Distribute RM width vernier into RM drafting stands.		将粗轧（RM）的宽度游标（Vernier）分配到粗轧各道次（Drafting Stands）的宽度传感器和机架，确保各道次的宽度控制参数符合工艺要求
//
// Argument list:
//    pcSched         - OUT/IN  pointer to sched
//
// Return:
//    bool            - status
//-----------------------------------------------------------------------------
bool cESUD::Dist_RMWid_Vern( cSched* pcSched )   // sched pointer
{
    //-----------------
    // Local variables
    //-----------------
    int iseg    = pcObjChain->Body_Index();
    int ps      = 0;
    bool entry_side = true;

    // Check for exit feedback - for case of FM entry edger
    if( pcSched->wrk[cXlevt::wrk_exit_fbk]  > 0 )
    {
        DMSG(-diagLvl) << (const char *)(pcSched->objName())
                        << " bypass Dist_RMWid_Vern, call is after RMX for F0 Edger FFWD"
                        << END_OF_MESSAGE;

        return true;
    }

    //-------------------------------------------------------------------
    // Initialize width offset for stand and width gauge sensors with
    // RM width vernier.
    //-------------------------------------------------------------------
    for ( ps=0; ps <= pcSched->pcSetupD->lstpas; ps++ )
    {
        // Stand
        if ( pcSched->pcSupPassD[ps]->pcStdD[iseg] != NULL )
        {
            pcSched->pcSupPassD[ps]->pcStdD[iseg]->wid_offset = 0.0F;
        }

        // Width gauge Sensors
        try
        {
            cBase   *pcObj = pcSched->pcSupPassD[ps]->pcEnPceD[iseg];
            entry_side = true;
            do
            {
                if ( _stricmp(pcObj->Get_Class_Name(), "cWidthSensorD") == 0 )
                {
                    cWidthSensorD    *pcWidthSensorD = (cWidthSensorD *)(pcObj);

                    if ( entry_side )
                    {
                        if ( ps <= 1 )
                        {
                            pcWidthSensorD->wid_offset = 0.0F;
                        }
                        else
                        {
//							pcWidthSensorD->wid_offset = pcSched->pcRAMP->state.rmx_wid_vern; //delete by xubin 2012.10.9
//======================================================================================================start add by xubin 2012.10.9
							if ( pcSched->pcRAMP->adapted_state.family_prv != pcSched->pcRAPP->state.family )
							{
                                pcWidthSensorD->wid_offset = pcSched->pcRAMP->state.rmx_wid_vern * ( 1 - pcSched->pcRAPP->state.rwid_off_mult ) + 
									pcSched->pcRAPP->state.rmx_wid_off * pcSched->pcRAPP->state.rwid_off_mult;
							}
							else
							{
								pcWidthSensorD->wid_offset = pcSched->pcRAMP->state.rmx_wid_vern;
							}
//======================================================================================================end add by xubin 2012.10.9
                        }
                    }
                    else
                    {
//							pcWidthSensorD->wid_offset = pcSched->pcRAMP->state.rmx_wid_vern; //delete by xubin 2012.10.9
//======================================================================================================start add by xubin 2012.10.9
							if ( pcSched->pcRAMP->adapted_state.family_prv != pcSched->pcRAPP->state.family )
							{
                                pcWidthSensorD->wid_offset = pcSched->pcRAMP->state.rmx_wid_vern * ( 1 - pcSched->pcRAPP->state.rwid_off_mult ) + 
									pcSched->pcRAPP->state.rmx_wid_off * pcSched->pcRAPP->state.rwid_off_mult;
							}
							else
							{
								pcWidthSensorD->wid_offset = pcSched->pcRAMP->state.rmx_wid_vern;
							}
//======================================================================================================end add by xubin 2012.10.9
                    }
                }
                else if ( _stricmp(pcObj->Get_Class_Name(), "cStdD") == 0 )
                {
                    entry_side = false;
                }
                pcObj = pcObj->next_obj;
            } while( pcObj != NULL && pcObj != pcSched->pcSupPassD[ps]->pcExPceD[iseg] );
        }
        catch(...)
        {
            EMSG << "Exception processing ESUD::Dist_RMWid_Vern(), ps= "
                 << ps
                 << END_OF_MESSAGE;
            return false;
        }

    }

    return true;

}   // end cESUD::Dist_RMWid_Vern


//-------------------------------------------------------------------------
// cESUD::Initialize - Initialize drafts and other related product data					
// Argument list:
//    pcSched         - OUT/IN  pointer to sched
//
// Return:
//    bool            - status
//-------------------------------------------------------------------------
bool cESUD::Initialize( cSched* pcSched )
{
    //-----------------
    // Local variables
    //-----------------
    int     iseg = pcObjChain->Body_Index();
    int     ps;                     // pass number
    float   wid_error    = 0.0F;    // Remaining width error

    //-----------------------------------------
    // Initialize average efficiency if needed.		初始化平均效率（avg_effi），平均效率用于评估材料在轧制过程中的宽度变化效率，影响后续的轧制量分配和误差修正
    //-----------------------------------------
    if ( this->avg_effi == 0.0F )
    {
        this->avg_effi = this->pcESU->effi_minw +
                         (pcSched->pcPDI->state.slabw - this->pcESU->minw) *
                         (this->pcESU->effi_maxw - this->pcESU->effi_minw)/
                         (this->pcESU->maxw - this->pcESU->minw);
        this->avg_effi = cMathUty::Clamp(
                            this->avg_effi,
                            this->pcESU->effi_maxw,
                            this->pcESU->effi_minw);
    }

#ifdef ALUMINUM_HSM
    //-----------------------------------------
    // ALUMINUM HSM uses edgers mostly to control edge cracking
    // Find minimum edge drafting pattern to satisfy edge cracking
    // dummy edgers on other passes
    //-----------------------------------------
    int     hseg = pcObjChain->Head_Index();
    int     bseg = pcObjChain->Body_Index();
    int     tseg = pcObjChain->Tail_Index();

    int j;
    int undummy_index_start=0;

    if (pcSched->pcSetupD->fstpas <= 1)
    {
        this->undummy_order[0] = pcSched->pcSetupD->fstpas;
        this->undummy_index=1;
        float width = pcSched->pcSupPassD[1]->pcEnPceD[bseg]->width;
        for ( ps = pcSched->pcSetupD->fstpas; ps <= pcSched->pcSetupD->lstpas; ps++ )
        {
            //-----------------------------------------
            // has enough width spread occurred since last edging pass to want to clean edges
            //-----------------------------------------
            float dw = pcSched->pcSupPassD[ps]->pcEnPceD[bseg]->width - width;
            if (dw > pcESU->dwidth_edging)
            {
                // If entry edge present and not hard dummied
                if (pcSched->pcSupPassD[ps]->EnEdgD_Present())
                {
                    if (!pcSched->pcSupPassD[ps]->pcEnEdgD[bseg]->hard_dummied)
                    {
                        // Undummy this edger and remember the entry width
                        pcSched->pcSupPassD[ps]->pcEnEdgD[hseg]->dummied =
                        pcSched->pcSupPassD[ps]->pcEnEdgD[bseg]->dummied =
                        pcSched->pcSupPassD[ps]->pcEnEdgD[tseg]->dummied = false;
                        width = pcSched->pcSupPassD[ps]->pcEnPceD[bseg]->width;

                        // Save it in the list of undummied edger passes
                        this->undummy_order[this->undummy_index++] = ps;
                    }
                }
                // Else If exit edge present and not hard dummied
                else if (pcSched->pcSupPassD[ps]->ExEdgD_Present())
                {
                    if (!pcSched->pcSupPassD[ps]->pcExEdgD[bseg]->hard_dummied)
                    {
                        // Undummy this edger and remember the entry width
                        pcSched->pcSupPassD[ps]->pcExEdgD[hseg]->dummied =
                        pcSched->pcSupPassD[ps]->pcExEdgD[bseg]->dummied =
                        pcSched->pcSupPassD[ps]->pcExEdgD[tseg]->dummied = false;
                        width = pcSched->pcSupPassD[ps]->pcEnPceD[bseg]->width;

                        // Save it in the list of undummied edger passes
                        this->undummy_order[this->undummy_index++] = ps;
                    }
                }
            }
            //-----------------------------------------
            // Always try to edge last pass
            //-----------------------------------------
            else if (ps==pcSched->pcSetupD->lstpas)
            {
                // If entry edger present and not hard dummied
                if (pcSched->pcSupPassD[ps]->EnEdgD_Present())
                {
                        pcSched->pcSupPassD[ps]->pcEnEdgD[hseg]->dummied =
                        pcSched->pcSupPassD[ps]->pcEnEdgD[bseg]->dummied =
                        pcSched->pcSupPassD[ps]->pcEnEdgD[tseg]->dummied = false;

                        //  Get pass # of previous undummied edger pass
                        j = this->undummy_order[this->undummy_index-1];

                        //  If less than 3 passes ago
                        if ( (ps - j) < 3)
                        {
                            // Dummy it again as this last pass edging will clean the edges
                            if (pcSched->pcSupPassD[j]->EnEdgD_Present())
                            {
                                pcSched->pcSupPassD[j]->pcEnEdgD[hseg]->dummied =
                                pcSched->pcSupPassD[j]->pcEnEdgD[bseg]->dummied =
                                pcSched->pcSupPassD[j]->pcEnEdgD[tseg]->dummied = true;
                            }
                            else if (pcSched->pcSupPassD[j]->ExEdgD_Present())
                            {
                                pcSched->pcSupPassD[j]->pcExEdgD[hseg]->dummied =
                                pcSched->pcSupPassD[j]->pcExEdgD[bseg]->dummied =
                                pcSched->pcSupPassD[j]->pcExEdgD[tseg]->dummied = true;
                            }
                            Set_Draft_Limit(pcSched->pcSupPassD[j]);
                            // Forget that this edger was undummied
                            this->undummy_index--;
                        }
                        this->undummy_order[this->undummy_index++] = ps;
                }
            }

            // Dummy unneeded edgers
            else
            {
                if (pcSched->pcSupPassD[ps]->EnEdgD_Present())
                {

                        pcSched->pcSupPassD[ps]->pcEnEdgD[hseg]->dummied =
                        pcSched->pcSupPassD[ps]->pcEnEdgD[bseg]->dummied =
                        pcSched->pcSupPassD[ps]->pcEnEdgD[tseg]->dummied = true;

                }
                if (pcSched->pcSupPassD[ps]->ExEdgD_Present())
                {
                        pcSched->pcSupPassD[ps]->pcExEdgD[hseg]->dummied =
                        pcSched->pcSupPassD[ps]->pcExEdgD[bseg]->dummied =
                        pcSched->pcSupPassD[ps]->pcExEdgD[tseg]->dummied = true;
                }
            }
            Set_Draft_Limit(pcSched->pcSupPassD[ps]);
        }

        // Augment undummied edger list with preferred order of edgers to be undummied if needed
        // Remember position in undummied list
        undummy_index_start = this->undummy_index;

        //  For all entries in the list look for edging passes between entries
        for (j=0; j<this->undummy_index-1; j++)
        {

            //  find the next edging pass in order
            int k1 = this->undummy_order[j];
            ps=this->undummy_order[j]+1;
            int k2 = 0;
            while ((k2 == 0) && (ps <= pcSched->pcSetupD->lstpas))
            {
                // if entry edger present
                if (pcSched->pcSupPassD[ps]->EnEdgD_Present())
                {
                    // if not dummied, skip to next entry in list
                    if (!pcSched->pcSupPassD[ps]->pcEnEdgD[bseg]->dummied)
                        k2 = ps;
                }
                // if exit edger present
                else if (pcSched->pcSupPassD[ps]->ExEdgD_Present())
                {
                    // if not dummied, skip to next entry in list
                    if (!pcSched->pcSupPassD[ps]->pcExEdgD[bseg]->dummied)
                        k2 = ps;
                }
                ps++;
            }

            //  if no edging pass found, use last pass
            if (k2==0) k2 = pcSched->pcSetupD->lstpas;

            //  find midpoint between edging passes
            int k3 = (k1+k2)/2;
            int k4 = pcSched->pcSetupD->lstpas;
            int k5=0;

            //  find edging pass closest to midpoint
            //  start with undummied edger pass + 1
            for (ps=this->undummy_order[j]+1; ps<k2; ps++)
            {
                // if entry edger present
                if (pcSched->pcSupPassD[ps]->EnEdgD_Present())
                {
                    // if not hard dummied
                    if (!pcSched->pcSupPassD[ps]->pcEnEdgD[bseg]->hard_dummied
                        && pcSched->pcSupPassD[ps]->pcEnEdgD[bseg]->dummied)
                    {
                        //  calculate distance from midpoint
                        k1 = abs(k3-ps);
                        //  remember if it is the closest
                        if (k1<k4)
                        {
                            k4 = k1;
                            k5 = ps;
                        }
                    }
                }
                // if entry edger present
                else if (pcSched->pcSupPassD[ps]->ExEdgD_Present())
                {
                    // if not hard dummied
                    if (!pcSched->pcSupPassD[ps]->pcExEdgD[bseg]->hard_dummied
                        && pcSched->pcSupPassD[ps]->pcExEdgD[bseg]->dummied)
                    {
                        //  calculate distance from midpoint
                        k1 = abs(k3-ps);
                        //  remember if it is the closest
                        if (k1<k4)
                        {
                            k4 = k1;
                            k5 = ps;
                        }
                    }
                }
            }
            //  if a candidate edging pass was found then
            if (k5 > 0)
            {
                //  save this edger as next to be undummied if needed
                this->undummy_order[this->undummy_index++] = k5;
            }
        }
    }
    //  now have minimum undummied edger pattern
    //  and list of edgers to undummy if needed
    //  set up a loop to find minimum undummy edger pattern
    bool go_back=true;
    while (go_back)
    {
#endif

        //-----------------------------------------
        // Reset edger status.  Needed so that we
        // can get a setup on 2nd or subsequent
        // calls.  KGM 990527.
        //-----------------------------------------

        this->esu_status = el_undef;			重置立辊状态，确保多次调用初始化时，状态不会残留历史数据，避免逻辑冲突


        //------------------------------------
        // Assign loads					分配负载与道次限制，根据材料特性和道次顺序分配轧制负载，确保各道次负荷均衡
        //------------------------------------
        if ( !Assign_Load( pcSched->pcSupPassD,
                            1, //pcSched->pcSetupD->fstpas,
                            pcSched->pcSetupD->lstpas ) )
        {
            //---------------------------------------------------------
            // INVALID status for Set_Draft_Limit
            //---------------------------------------------------------
            EMSG << "  ESU::Initialize: INVALID status return Assign_Load() "
                << "  prod_id= " << pcSched->objName()
                << END_OF_MESSAGE;

            return false;
        }

        //------------------------------------
        // Assign draft limits for all passes											
        //------------------------------------
        if ( !Set_Draft_Limits( pcSched->pcSupPassD,		调用之前解析的函数，设置单道次轧制量限制，防止设备过载或板材缺陷
                            pcSched->pcSetupD->fstpas,
                            pcSched->pcSetupD->lstpas ) )
        {
            //---------------------------------------------------------
            // INVALID status for Set_Draft_Limit
            //---------------------------------------------------------
            EMSG << "  ESU::Initialize: INVALID status return Set_Draft_Limits() "
                << "  prod_id= " << pcSched->objName()
                << END_OF_MESSAGE;

            return false;
        }

        //----------------------------------------------------------
        // set the target width and thickness on the sensor objects		设置传感器目标值与 FM 展宽预测
        //----------------------------------------------------------
        if ( prd_plate != pcSched->pcPDI->state.product )
        {
            pcSched->pcSuSensorCfgD[iseg]->pcFMXThickD->targ = pcSched->pcSuPce->fx_hot_thick; 			设置精轧（FM）传感器的目标厚度、宽度、温度
            pcSched->pcSuSensorCfgD[iseg]->pcFMXWidthD->targ = pcSched->pcSuPce->fx_hot_width;
            pcSched->pcSuSensorCfgD[iseg]->pcFMXPyroD->targ  = pcSched->pcSuPce->fmx_tgt_tmp;
        }

        //-------------------------
        // Predict FM spread amount					预测FM展宽量（调用之前解析的Get_Fm_Spread函数
        //-------------------------
        if ( prd_plate != pcSched->pcPDI->state.product )
        {
            pcSched->pcSetupD->fm_spread = Get_Fm_Spread( pcSched->pcSuSensorCfgD[iseg], pcSched ); //add  pcSched 20150319
        }
        else
        {
            pcSched->pcSetupD->fm_spread = 0.0F;
        }

        //-----------------------------
        // Predict RM exit target width
        //-----------------------------
        if ( !Set_Tgt_Width( pcSched, pcSched->pcSuSensorCfgD[iseg],			计算粗轧出口目标宽度，将精轧目标宽度转换为粗轧热态目标宽度，扣除 FM 展宽量，并考虑边部轧机效应		
                            pcSched->pcSetupD->fm_spread ) )
        {
            //---------------------------------------------------------
            // INVALID status for Set_Tgt_Width
            //---------------------------------------------------------
            EMSG << "  ESU::Initialize: INVALID status return Set_Tgt_Width() "
                << "  prod_id= " << pcSched->objName()
                << END_OF_MESSAGE;

            return false;
        }

        //-----------------------------------------------------
        // Distribute RM width vernier into drafting RM stands.
        //---------------------------------------------------
        if ( !Dist_RMWid_Vern( pcSched ) )						分配宽度游标到各道次，通过微调各道次的宽度传感器偏移量（wid_offset），实现对轧制宽度的精细控制
        {
            //---------------------------------------------------------
            // INVALID status for Dist_RMWid_Vern
            //---------------------------------------------------------
            EMSG << "  ESU::Initialize: INVALID status return Dist_RMWid_Vern() "
                << "  prod_id= " << pcSched->objName()
                << END_OF_MESSAGE;

            return false;
        }

        //------------------------------------------------------------------------
        // Calculate total estimated width error to be removed
        // NOTE: all components of the following should be at RMX temperature.
        //       also, at the Initialization rmx_wid_vern should be used, later on
        //       portions of rmx_wid_vern will be picked off from stand Operate().
        //------------------------------------------------------------------------
        if ( pcSched->pcSetupD->fstpas > 1 )					计算宽度误差（wid_error）
        {
            ps = pcSched->pcSetupD->fstpas;

            // Check for exit feedback - for case of FM entry edger
            if( pcSched->wrk[cXlevt::wrk_exit_fbk]  <= 0 )
            {
                wid_error = pcSched->pcSupPassD[ps]->pcEnPceD[iseg]->width
                        * pcSched->pcSuSensorCfgD[iseg]->pcRMXWidthTargtD->Expansion()
                        / pcSched->pcSupPassD[ps]->pcEnPceD[iseg]->Expansion()
                        - pcSched->pcSuSensorCfgD[iseg]->pcRMXWidthD->targ
                        - pcSched->pcSuSensorCfgD[iseg]->pcRMXWidthD->wid_offset;
            }
            else
            {
                wid_error = 0.0F;
                if ( pcSched->pcFbSensorCfgD[iseg]->pcRMXWidthD->meas > 0.0F )
                {
                    wid_error = pcSched->pcFbSensorCfgD[iseg]->pcRMXWidthD->meas
                            - pcSched->pcSuSensorCfgD[iseg]->pcRMXWidthD->targ;
                }
            }
        }
        else
        {
            // Check Pass0 data, if available use it - more accurate
            if ( pcSched->pcSupPassD[0]->pcExPceD[iseg]->Expansion() > 0.0F )
            {
                wid_error = pcSched->pcSupPassD[0]->pcExPceD[iseg]->width
                        * pcSched->pcSuSensorCfgD[iseg]->pcRMXWidthTargtD->Expansion()
                        / pcSched->pcSupPassD[0]->pcExPceD[iseg]->Expansion()
                        - pcSched->pcSuSensorCfgD[iseg]->pcRMXWidthD->targ
                        - pcSched->pcSuSensorCfgD[iseg]->pcRMXWidthD->wid_offset;
            }
            else
            {
                wid_error = pcSched->pcSuSensorCfgD[iseg]->pcRMEWidthTargtD->width
                        * pcSched->pcSuSensorCfgD[iseg]->pcRMXWidthTargtD->Expansion()
                        / pcSched->pcSuSensorCfgD[iseg]->pcRMEWidthTargtD->Expansion()
                        - pcSched->pcSuSensorCfgD[iseg]->pcRMXWidthD->targ
                        - pcSched->pcSuSensorCfgD[iseg]->pcRMXWidthD->wid_offset;
            }
        }

        DMSG(diagLvl) << pcSched->objName()
                    << ", wid error= " << wid_error
                    << ", Initial avg_effi= " << this->avg_effi
                    << END_OF_MESSAGE;

        //-----------------------
        // Perform edger setup
        //-----------------------
        if ( !Setup( pcSched, wid_error ) )													执行立辊轧机设置（Setup函数），基于初始化的限制和误差，计算各道次的辊缝、速度等参数，形成可执行的轧制计划
        {
            //---------------------------------------------------------
            // INVALID status for Setup
            //---------------------------------------------------------
            EMSG << "  ESU::Initialize: INVALID status return Setup() "
                << "  prod_id= " << pcSched->objName()
                << END_OF_MESSAGE;

            return false;
        }

#ifdef ALUMINUM_HSM
        //  clear flag to continue looping
        go_back = false;

        //  save the width error from present dummy pattern
        float xwid_err = wid_error;

        //  look through undummy list to undummy edgers if necessary
        //  start with edging passes not yet undummied
        for (j=undummy_index_start; j<this->undummy_index; j++)
        {
            //  get edging pass number for next to be undummied
            ps = this->undummy_order[j];

            if (ps >= pcSched->pcSetupD->fstpas)
            {

                //  if width error requires more undummied passes
                if (xwid_err > (2.0F * pcESU->accuracy))
                {
                    //  if entry edger is present and not hard dummied but dummied then
                    if (pcSched->pcSupPassD[ps]->EnEdgD_Present())
                    {
                        if (!pcSched->pcSupPassD[ps]->pcEnEdgD[bseg]->hard_dummied)
                        {
                            if (pcSched->pcSupPassD[ps]->pcEnEdgD[bseg]->dummied)
                            {
                                //  undummy edger
                                pcSched->pcSupPassD[ps]->pcEnEdgD[hseg]->dummied =
                                pcSched->pcSupPassD[ps]->pcEnEdgD[bseg]->dummied =
                                pcSched->pcSupPassD[ps]->pcEnEdgD[tseg]->dummied = false;

                                //  set draft limit and decrement width error
                                Set_Draft_Limit(pcSched->pcSupPassD[ps]);
                                xwid_err -= (pcSched->pcSupPassD[ps]->pcEnEdgD[bseg]->draft_max*this->avg_effi);
                                undummy_index_start = j+1;
                                //  set flag to go back through loops since dummy pattern has changed
                                go_back = true;
                            }
                        }
                    }
                }
                else
                    //  else not enough error to worry about, get out of loop
                    break;

                //  if width error still requires more undummied passes
                if (xwid_err > (2.0F * pcESU->accuracy))
                {
                    //  if exity edger is present and not hard dummied but dummied then
                    if (pcSched->pcSupPassD[ps]->ExEdgD_Present())
                    {
                        if (!pcSched->pcSupPassD[ps]->pcExEdgD[bseg]->hard_dummied)
                        {
                            if (pcSched->pcSupPassD[ps]->pcExEdgD[bseg]->dummied)
                            {
                                //  undummy edger
                                pcSched->pcSupPassD[ps]->pcExEdgD[hseg]->dummied =
                                pcSched->pcSupPassD[ps]->pcExEdgD[bseg]->dummied =
                                pcSched->pcSupPassD[ps]->pcExEdgD[tseg]->dummied = false;

                                //  set draft limit and decrement width error
                                Set_Draft_Limit(pcSched->pcSupPassD[ps]);
                                xwid_err -= (pcSched->pcSupPassD[ps]->pcExEdgD[bseg]->draft_max*this->avg_effi);

                                undummy_index_start = j+1;

                                //  set flag to go back through loops since dummy pattern has changed
                                go_back = true;
                            }
                        }
                    }
                }
                else
                    //  else not enough error to worry about, get out of loop
                    break;

            }
        }
    }

#endif

    return true;

}   // end cESUD::Initialize

//-------------------------------------------------------------------------
// cESUD::Executive - Main drafting executive module					根据输入的调度信息（pcSched）对粗轧过程中的轧边机道次进行调整，以确保最终产品的宽度符合目标要求
//																	该函数会计算各种参数，如总轧边机道次、精轧展宽量、粗轧出口目标宽度和剩余宽度误差等，
// Argument list:														并根据这些参数进行负载分配和轧边机设置，最后判断是否对道次进行了调整并返回执行状态。
//    pcSched         - OUT/IN  pointer to Sched
//    dft_adjusted    - OUT     if true, edger drafts were modified
//
// Return:
//    bool            - status
//-------------------------------------------------------------------------
bool cESUD::Executive( cSched* pcSched, bool&  dft_adjusted )
{
    //-----------------
    // Local variables
    //-----------------
    int     iseg        = pcObjChain->Body_Index();
    int     last_pass   = pcSched->pcSetupD->lstpas;
    float   wid_error   = 0.0F;         // Remaining width error
    float   drafts      = 0.0F;         // total edger drafts
    float   drafts_min  = 0.0F;         // total edger min drafts
    float   drafts_max  = 0.0F;         // total edger max drafts

    //------------------------------
    // Check for all edgers dummied
    //------------------------------
    if ( pcSched->pcSetupD->edgs_dummied )
    {
        pcSched->pcSetupD->esu_ok = true;
        dft_adjusted = false;
        return true;
    }

    //-------------------------------------------------------------------------
    // Should not have the limit check and EXIT here, because we need to go
    // through this code at least once to allow for all width verniers to be
    // counted.
    //-------------------------------------------------------------------------

    //------------------------------------------------
    // Get total edger drafts in RM									压下量
    //------------------------------------------------
    Total_Edg_Draft( pcSched->pcSupPassD,
                     pcSched->pcSetupD->fstpas,
                     pcSched->pcSetupD->lstpas,
                     drafts,
                     drafts_min,
                     drafts_max);

    pcSched->pcSetupD->totedgdft = drafts;;

    //------------------------------------------
    // Assign expansion coefficients for logging						膨胀系数
    //------------------------------------------
    if ( prd_plate != pcSched->pcPDI->state.product )
    {
        ((cWidthSensorD *)
            (pcSched->pcSuSensorCfgD[iseg]->pcFMXWidthTargtD->previous_obj))->expansion =
                    pcSched->pcSuSensorCfgD[iseg]->pcFMXWidthTargtD->Expansion();
        ((cThicknessSensorD *)
            (pcSched->pcSuSensorCfgD[iseg]->pcFMXThickTargtD->previous_obj))->expansion =
                    pcSched->pcSuSensorCfgD[iseg]->pcFMXThickTargtD->Expansion();
    }

    //-------------------------
    // Predict FM spread amount									预测精轧展宽量
    //-------------------------
    if ( prd_plate != pcSched->pcPDI->state.product )
    {
        pcSched->pcSetupD->fm_spread = Get_Fm_Spread( pcSched->pcSuSensorCfgD[iseg], pcSched ); //add  pcSched 20150319
    }
    else
    {
        pcSched->pcSetupD->fm_spread = 0.0F;
    }

    //-----------------------------
    // Predict RM exit target width									预测粗轧出口目标宽度
    //-----------------------------
    if ( !Set_Tgt_Width( pcSched, pcSched->pcSuSensorCfgD[iseg],				调用 Set_Tgt_Width 函数预测粗轧出口目标宽度。
                         pcSched->pcSetupD->fm_spread ) )
    {
        //---------------------------------------------------------
        // INVALID status for Set_Tgt_Width
        //---------------------------------------------------------
        EMSG << "  ESU::Executive: INVALID status return Set_Tgt_Width() "
             << "  prod_id= " << pcSched->objName()
             << END_OF_MESSAGE;

        return false;
    }

    //---------------------------------
    // Calculate remaining Width Error								计算剩余宽度误差（根据是否有出口反馈信息和产品类型（板材或卷材），计算剩余宽度误差 wid_error）
    //---------------------------------
    // Check for exit feedback - for case of FM entry edger
    if( pcSched->wrk[cXlevt::wrk_exit_fbk]  <= 0 )
    {
#if !INCLUDE_FME_EDGER
        wid_error = pcSched->pcSuSensorCfgD[iseg]->pcRMXWidthTargtD->width
                    - pcSched->pcSuSensorCfgD[iseg]->pcRMXWidthD->targ
                    - pcSched->pcSuSensorCfgD[iseg]->pcRMXWidthD->wid_offset;    //aeb
#else
        // Use the following code for COILS NOT for PLATES
        if ( prd_plate != pcSched->pcPDI->state.product )				plate 板材
        {
            pcSched->pcSetupD->fme_prd_wid =
                pcSched->pcSupPassD[last_pass]->pcEnEdgD[iseg]->pcExPceD->width
              - pcSched->pcSuSensorCfgD[iseg]->pcRMXWidthD->wid_offset;    //aeb
            if ( pcSched->pcSupPassD[last_pass]->pcEnEdgD[iseg]->draft > Physcon.tol2 )
            {
                pcSched->pcSetupD->fme_prd_wid += pcSched->pcSupPassD[last_pass]->pcEnEdgD[iseg]->pcExPceD->recovery;
            }
            wid_error = pcSched->pcSetupD->fme_prd_wid
                      - pcSched->pcSetupD->fme_tgt_wid;
        }
        else    // PLATES ONLY
        {
            wid_error = pcSched->pcSuSensorCfgD[iseg]->pcRMXWidthTargtD->width
                        - pcSched->pcSuSensorCfgD[iseg]->pcRMXWidthD->targ
                        - pcSched->pcSuSensorCfgD[iseg]->pcRMXWidthD->wid_offset;    //aeb
        }
    }
    else
    {
        // Use the following code for COILS NOT for PLATES			coils 卷材
        if ( prd_plate != pcSched->pcPDI->state.product )
        {
            wid_error = pcSched->pcSupPassD[last_pass]->pcEnEdgD[iseg]->pcExPceD->width
                      - pcSched->pcSetupD->fme_prd_wid;

            if ( pcSched->pcSupPassD[last_pass]->pcEnEdgD[iseg]->draft > Physcon.tol2 )
            {
                wid_error += pcSched->pcSupPassD[last_pass]->pcEnEdgD[iseg]->pcExPceD->recovery;
            }
        }
        else    // PLATES ONLY
        {
            wid_error = pcSched->pcSuSensorCfgD[iseg]->pcRMXWidthTargtD->width
                        - pcSched->pcSuSensorCfgD[iseg]->pcRMXWidthD->targ
                        - pcSched->pcSuSensorCfgD[iseg]->pcRMXWidthD->wid_offset;    //aeb
        }
#endif
    }

    //-------------------------------------------------------------------------
    // Limit check width error and zero drafts to set dft_adjusted flag
    //-------------------------------------------------------------------------
    if ( (fabs(wid_error) <= (2.0F * this->pcESU->accuracy)) &&
         (drafts >= drafts_min) )
    {
        dft_adjusted = false;
        return true;
    }


    //------------------------------------
    // Assign loads
    //------------------------------------
    if ( !Assign_Load( pcSched->pcSupPassD,
                           1, //pcSched->pcSetupD->fstpas,
                           pcSched->pcSetupD->lstpas ) )
    {
        //---------------------------------------------------------
        // INVALID status for Set_Draft_Limit
        //---------------------------------------------------------
        EMSG << "  ESU::Executive: INVALID status return Assign_Load() "
             << "  prod_id= " << pcSched->objName()
             << END_OF_MESSAGE;

        return false;
    }

    //-----------------------
    // Perform edger setup
    //-----------------------
    if ( !Setup( pcSched, wid_error ) )
    {
        //---------------------------------------------------------
        // INVALID status for Setup
        //---------------------------------------------------------
        EMSG << "  ESU::Executive: INVALID status return Setup() "
             << "  prod_id= " << pcSched->objName()
             << END_OF_MESSAGE;

        return false;
    }

    //------------------------------------------------
    // Calculate delta total edger drafts in RM
    //------------------------------------------------
    float tot_dft_err = drafts;
    Total_Edg_Draft( pcSched->pcSupPassD,
                     pcSched->pcSetupD->fstpas,
                     pcSched->pcSetupD->lstpas,
                     drafts,
                     drafts_min,
                     drafts_max);
    tot_dft_err -= drafts;

    pcSched->pcSetupD->totedgdft = drafts;;

    //-------------------------------------------------------------------------
    // Limit check delta total edger drafts in RM and set the dft_adjusted flag
    //-------------------------------------------------------------------------
    dft_adjusted = (fabs(tot_dft_err) > (2.0F * this->pcESU->accuracy));

    return true;

}   // end cESU::Executive

//---------------------------------------------
// cESUD::Setup - calculate edger setup
//
//
// Argument list:
//    pcSched         - OUT/IN  pointer to Sched
//    wid_error       -     IN  width error to be removed
//
// Return:
//    bool            - status
//---------------------------------------------
bool cESUD::Setup( cSched* pcSched, float &width_error )
{
    //-----------------
    // Local variables
    //-----------------
    int     iseg         = pcObjChain->Body_Index();
    float   fx_hot_width = 0.0F;
    bool    calc_draft   = true;
    int     loop_count   = 0;
    int     loop_limit   = 20;
    int     ps;
    int     fstpas       = pcSched->pcSetupD->fstpas;
    int     lstpas       = pcSched->pcSetupD->lstpas;
    float   dft_error;

    // Use the following code for PLATES
    if ( prd_plate == pcSched->pcPDI->state.product )
    {
        lstpas--;
    }

    dft_error  = width_error;
    if ( this->avg_effi > 0.0F )
    {
        dft_error /= this->avg_effi;
    }

    //-------------------------------------
    // Set edger setup status to INVALID
    //-------------------------------------
    pcSched->pcSetupD->esu_ok = false;								在轧边机道次动态优化之前将 esu_ok 设置为 false，是一种安全初始化策略，用于确保系统在执行优化过程中正确跟踪状态

    while ( calc_draft && (loop_count <= loop_limit) )						迭代优化
    {
        calc_draft = false;													默认先执行一次调整，再根据结果决定是否继续

        //---------------------------------------------------------------------
        // aeb - Should go through the following code because, it could be the
        //       first time at initialization time and all edger drafts are
        //       zeros and min drafts are small positive value.
        //---------------------------------------------------------------------

        //---------------------------------------------------------------------
        // Befor distributing width_error, check the edger setup OVERDRAFT
        // and UNDERDRAFT conditions.
        // Note: width_error = predicted - target
        //---------------------------------------------------------------------
        if ( ( width_error > 0.0F && el_overdraft  == this->esu_status ) ||				匹配：说明当前状态已反映误差方向，不重新分配压下量
             ( width_error < 0.0F && el_underdraft == this->esu_status ) )
        {
            ;
        }
        else
        {
            if( !Distribute_Dft_Error( pcSched->pcSupPassD,
                                       fstpas,
                                       lstpas,
                                       dft_error) )
            {
                //---------------------------------------------------------
                // Draft cannot be distributed amongst the passes specified
                //  draft distribution did not converge
                //---------------------------------------------------------
                EMSG << pcSched->objName()
                     << "  ESU::Setup: INVALID status return  Distribute_Dft_Error()"
                     << NEWLINE
                     << pcSched->objName() << " slbw/fxw= "
                     << pcSched->pcPDI->state.slabw << "," << pcSched->pcPDI->state.fxwaim
                     << " dft_er= " << dft_error
                     << " fst/lst= " << fstpas
                     << "," << lstpas
                     << END_OF_MESSAGE;

                return false;

            } // end Distribute_Dft_Error()
        }   // end el_overdraft == pcSched->pcESUD->esu_status ) ||

        //---------------------------------------------------------------------
        // Note: at this point should walk the objects from first rm pass to
        // pcRMXTgt_ExPceD object, so we would have the correct
        // "pcRMXTgt_ExPceD->width".  This should be done so we pass the
        // correct R2X width to draft->Executive.
        //---------------------------------------------------------------------

        Draft_Callback(pcSched);

        //------------------------------------------------------
        // Recalculate the average efficiency and width error.
        // width_error "= predicted - target" at RMX
        //------------------------------------------------------
        float   tot_draft = 0.0F;
        float   tot_recov = 0.0F;
        float   tot_effi  = 0.0F;
        for ( ps = fstpas; ps <= lstpas; ps++ )
        {
            cEdgD *pcEdgD;

            if( (pcEdgD=pcSched->pcSupPassD[ps]->Drafting_EdgD(iseg)) != NULL )
            {
                tot_draft += pcEdgD->draft;
            }
        }
        for ( ps = fstpas; ps <= lstpas; ps++ )
        {
            cEdgD *pcEdgD;

            //-------------------------------------------------------------
            // Calculate total edge draft and recovery.  Take account of
            // double dogbone when accumulating recovery.
            //-------------------------------------------------------------
            if( (pcEdgD=pcSched->pcSupPassD[ps]->Drafting_EdgD(iseg)) != NULL )
            {
                tot_recov += (pcEdgD->pcExPceD->recovery - pcEdgD->pcEnPceD->recovery);
                tot_effi += pcEdgD->effi* pcEdgD->draft/tot_draft;
            }
        }
        this->avg_effi = tot_effi;

        //-------------------------------------------------------------
        // Calculate the width error.
        //-------------------------------------------------------------
        // Check for exit feedback - for case of FM entry edger
        if( pcSched->wrk[cXlevt::wrk_exit_fbk]  <= 0 )						根据是否有出口反馈信号（wrk_exit_fbk）和产品类型（卷材 / 板材），分路径计算新的宽度误差
        {
#if !INCLUDE_FME_EDGER
            width_error = pcSched->pcSuSensorCfgD[iseg]->pcRMXWidthTargtD->width
                        - pcSched->pcSuSensorCfgD[iseg]->pcRMXWidthD->targ
                        - pcSched->pcSuSensorCfgD[iseg]->pcRMXWidthD->wid_offset;    //aeb
#else
            // Use the following code for COILS NOT for PLATES
            if ( prd_plate != pcSched->pcPDI->state.product )
            {
                pcSched->pcSetupD->fme_prd_wid =
                    pcSched->pcSupPassD[lstpas]->pcEnEdgD[iseg]->pcExPceD->width
                  - pcSched->pcSuSensorCfgD[iseg]->pcRMXWidthD->wid_offset;    //aeb
                if ( pcSched->pcSupPassD[lstpas]->pcEnEdgD[iseg]->draft > Physcon.tol2 )
                {
                    pcSched->pcSetupD->fme_prd_wid += pcSched->pcSupPassD[lstpas]->pcEnEdgD[iseg]->pcExPceD->recovery;
                }
                width_error = pcSched->pcSetupD->fme_prd_wid
                            - pcSched->pcSetupD->fme_tgt_wid;
            }
            else    // PLATES ONLY
            {
                width_error = pcSched->pcSuSensorCfgD[iseg]->pcRMXWidthTargtD->width
                            - pcSched->pcSuSensorCfgD[iseg]->pcRMXWidthD->targ
                            - pcSched->pcSuSensorCfgD[iseg]->pcRMXWidthD->wid_offset;    //aeb
            }
        }
        else
        {
            // Use the following code for COILS NOT for PLATES
            if ( prd_plate != pcSched->pcPDI->state.product )
            {
                width_error = pcSched->pcSupPassD[lstpas]->pcEnEdgD[iseg]->pcExPceD->width
                            - pcSched->pcSetupD->fme_prd_wid;

                if ( pcSched->pcSupPassD[lstpas]->pcEnEdgD[iseg]->draft > Physcon.tol2 )
                {
                    width_error += pcSched->pcSupPassD[lstpas]->pcEnEdgD[iseg]->pcExPceD->recovery;
                }
            }
            else    // PLATES ONLY
            {
                width_error = pcSched->pcSuSensorCfgD[iseg]->pcRMXWidthTargtD->width
                            - pcSched->pcSuSensorCfgD[iseg]->pcRMXWidthD->targ
                            - pcSched->pcSuSensorCfgD[iseg]->pcRMXWidthD->wid_offset;    //aeb
            }
#endif
        }

        DMSG((loop_count>5)?(-diagLvl):(diagLvl))
                    << pcSched->objName()
                    << ", status= " << cEdgD::Image(this->esu_status)
                    << END_OF_MESSAGE;

        //------------------------------
        // set ESU status
        //------------------------------
        Set_Esu_Status( pcSched, width_error);

        //-------------------------------------------------------------
        // When calculating dft_error we need to damp the calculation
        // since we expect efficiency to reduce as we increase the
        // total draft taken.  The value 0.75 seems a reasonable
        // compromise between fast convergence and potential overshoot
        // problems during convergence.
        //-------------------------------------------------------------
        dft_error  = 0.75 * width_error;
        if ( this->avg_effi > 0.0F )
        {
            dft_error /= this->avg_effi;
        }

        //--------------------------------------------------------
        // overdraft condition, not able to make the target width.					过载 / 欠载状态处理
        // actual width will be wider than the target width.
        // underdraft condition, not able to make the target width.
        // actual width will be narrower than the target width.
        //--------------------------------------------------------
        if ( el_overdraft  == this->esu_status ||
             el_underdraft == this->esu_status )
        {

            //-----------------------------------------------------------------
            // Limit check width_error wrt esu_status, if necessary, reset
            // esu_status and loop to resolve the width error
            //-----------------------------------------------------------------
            if ( fabs(width_error) <= (2.0F * this->pcESU->accuracy) )
            {
                // width error is within tolerance, reset
                // esu status and  exit the loop
                this->esu_status = el_nolim;
            }
            else if ( ( width_error < 0.0F && el_overdraft  == this->esu_status ) ||
                      ( width_error > 0.0F && el_underdraft == this->esu_status ) )
            {
                // Reset esu status and force another draft distribution
                this->esu_status = el_nolim;
                calc_draft = true;
            }
            else
            {
                //---------------------------------------------------------------------
                // Edger drafts are clamped to limits - not able to make target width.
                // Repredict FMX target width based on the new RMX target width
                //---------------------------------------------------------------------
                if ( prd_plate != pcSched->pcPDI->state.product )
                {
                    float lin_exp_rat = 0.0F;
                    if ( pcSched->pcSuSensorCfgD[iseg]->pcRMXWidthTargtD->Expansion() > 0.0F )
                    {
                        lin_exp_rat = pcSched->pcSuSensorCfgD[iseg]->pcFMXWidthTargtD->Expansion()
                                    / pcSched->pcSuSensorCfgD[iseg]->pcRMXWidthTargtD->Expansion();
                    }
//==========================================================================start delete by xubin 2012.10.9
//                    fx_hot_width =
////AEB??                        ( pcSched->pcSuSensorCfgD[iseg]->pcRMXWidthTargtD->width
//                        ( pcSched->pcSuSensorCfgD[iseg]->pcRMXWidthD->pred
//                          + pcSched->pcSetupD->fm_spread ) * lin_exp_rat
//                        - pcSched->pcRAMP->state.fmx_wid_vern * lin_exp_rat * lin_exp_rat;
//==========================================================================end delete by xubin 2012.10.9
//====================================================================================start add by xubin 2012.10.9
		 			if ( pcSched->pcRAMP->adapted_state.family_prv != pcSched->pcRAPP->state.family )
		 			{
				     fx_hot_width =
							( pcSched->pcSuSensorCfgD[iseg]->pcRMXWidthD->pred
							+ pcSched->pcSetupD->fm_spread ) * lin_exp_rat
							- ( pcSched->pcRAMP->state.fmx_wid_vern * ( 1 - pcSched->pcRAPP->state.fwid_off_mult ) 
							+ pcSched->pcRAPP->state.fmx_wid_off * pcSched->pcRAPP->state.fwid_off_mult ) * lin_exp_rat * lin_exp_rat;
		 			}
		 			else
		 			{
                     fx_hot_width =
							( pcSched->pcSuSensorCfgD[iseg]->pcRMXWidthD->pred
							+ pcSched->pcSetupD->fm_spread ) * lin_exp_rat
							- pcSched->pcRAMP->state.fmx_wid_vern * lin_exp_rat * lin_exp_rat;
		 			}
//====================================================================================end add by xubin 2012.10.9
#if INCLUDE_FME_EDGER
                    //----------------------------------------------------------
                    // Calculate new FMX target width, considering a F0 Edger.
                    // Convert the (draft - recov) to FMX target temperature.
                    // FXW = RXW + fm_spread - F0_edger net effect
                    //----------------------------------------------------------
                    if ( pcSched->pcSupPassD[lstpas]->pcExPceD[iseg]->Expansion() > Physcon.tol2 &&
                         pcSched->pcSupPassD[lstpas]->pcEnEdgD[iseg]->draft > Physcon.tol2 )
                    {
                        fx_hot_width -= ( pcSched->pcSupPassD[lstpas]->pcEnEdgD[iseg]->draft
                                        - pcSched->pcSupPassD[lstpas]->pcEnEdgD[iseg]->pcExPceD->recovery )
                                        * pcSched->pcSuSensorCfgD[iseg]->pcFMXWidthTargtD->Expansion()
                                        / pcSched->pcSupPassD[lstpas]->pcExPceD[iseg]->Expansion();
                    }
#endif

                    //------------------------------------------------------
                    // Set FMX target width on the width sensor object
                    //------------------------------------------------------
                    pcSched->pcSuSensorCfgD[iseg]->pcFMXWidthD->targ = fx_hot_width;

                    //--------------------------------------------------------------
                    // Repredict FM spread amount based on the new FMX target width
                    //--------------------------------------------------------------
                    pcSched->pcSetupD->fm_spread =
                                        Get_Fm_Spread( pcSched->pcSuSensorCfgD[iseg], pcSched ); //add  pcSched 20150319

                    //---------------------------------------------------------------------
                    // Repredict FMX target width based on
                    // the new RMX target width and new fm_spread
                    //---------------------------------------------------------------------
//================================================================================start delete by xubin 2012.10.9
//                    fx_hot_width =
////AEB??                        ( pcSched->pcSuSensorCfgD[iseg]->pcRMXWidthTargtD->width
//                        ( pcSched->pcSuSensorCfgD[iseg]->pcRMXWidthD->pred
//                          + pcSched->pcSetupD->fm_spread ) * lin_exp_rat
//                        - pcSched->pcRAMP->state.fmx_wid_vern * lin_exp_rat * lin_exp_rat;
//================================================================================end delete by xubin 2012.10.9
//====================================================================================start add by xubin 2012.10.9
		 			if ( pcSched->pcRAMP->adapted_state.family_prv != pcSched->pcRAPP->state.family )
		 			{
				     fx_hot_width =
                        ( pcSched->pcSuSensorCfgD[iseg]->pcRMXWidthD->pred
                          + pcSched->pcSetupD->fm_spread ) * lin_exp_rat
                        - ( pcSched->pcRAMP->state.fmx_wid_vern * ( 1 - pcSched->pcRAPP->state.fwid_off_mult ) 
						+ pcSched->pcRAPP->state.fmx_wid_off * pcSched->pcRAPP->state.fwid_off_mult ) * lin_exp_rat * lin_exp_rat;
		 			}
		 			else
		 			{
                     fx_hot_width =
                        ( pcSched->pcSuSensorCfgD[iseg]->pcRMXWidthD->pred
                          + pcSched->pcSetupD->fm_spread ) * lin_exp_rat
                        - pcSched->pcRAMP->state.fmx_wid_vern * lin_exp_rat * lin_exp_rat;
		 			}
//====================================================================================end add by xubin 2012.10.9
#if INCLUDE_FME_EDGER
                    //----------------------------------------------------------
                    // Calculate new FMX target width, considering a F0 Edger.
                    // Convert the (draft - recov) to FMX target temperature.
                    // FXW = RXW + fm_spread - F0_edger net effect
                    //----------------------------------------------------------
                    if ( pcSched->pcSupPassD[lstpas]->pcExPceD[iseg]->Expansion() > Physcon.tol2 &&
                         pcSched->pcSupPassD[lstpas]->pcEnEdgD[iseg]->draft > Physcon.tol2 )
                    {
                        fx_hot_width -= ( pcSched->pcSupPassD[lstpas]->pcEnEdgD[iseg]->draft
                                        - pcSched->pcSupPassD[lstpas]->pcEnEdgD[iseg]->pcExPceD->recovery )
                                        * pcSched->pcSuSensorCfgD[iseg]->pcFMXWidthTargtD->Expansion()
                                        / pcSched->pcSupPassD[lstpas]->pcExPceD[iseg]->Expansion();
                    }
#endif

                    //---------------------------------------------------
                    // Set FMX target width on the width sensor object.				
                    // Also, adjust the FMX target width.
                    //---------------------------------------------------
                    pcSched->pcSuSensorCfgD[iseg]->pcFMXWidthD->targ = fx_hot_width;
                    pcSched->pcSuSensorCfgD[iseg]->pcFMXWidthTargtD->width = fx_hot_width;
                }
                //---------------------------------------------------
                // Set RMX target width on the width sensor object
                //---------------------------------------------------
                pcSched->pcSuSensorCfgD[iseg]->pcRMXWidthD->targ =
                                pcSched->pcSuSensorCfgD[iseg]->pcRMXWidthTargtD->width
                              - pcSched->pcSuSensorCfgD[iseg]->pcRMXWidthD->wid_offset;  //aeb
            }   //  if ( ( width_error < 0.0F &&
        }   // end if ( el_overdraft == pcSched->pcESUD->esu_status ||
        else
        {
            if ( fabs(width_error) > (2.0F * this->pcESU->accuracy) )
            {
                calc_draft = true;
            }
        }

        DMSG((loop_count>5)?(-diagLvl):(diagLvl))
                    << pcSched->objName()
                      << ", wid error= " << width_error
                      << ", loop_count= " << loop_count
                      << ", avg_effi= " << this->avg_effi
                      << ", tot_draft= " << tot_draft
                      << ", status= " << cEdgD::Image(this->esu_status)
                      << END_OF_MESSAGE;

        loop_count++;

    }   // end while ( calc_draft && (loop_count <= loop_limit) )

    //----------------------------------------------------------
    // Set FMX target width on the width sensor object and			将精轧目标宽度（fx_hot_width）同步到传感器配置对象，作为实际生产的控制目标
    // adjust the FMX target width, if esu is not in limit.
    //----------------------------------------------------------
    if ( (prd_plate     != pcSched->pcPDI->state.product) &&				
         (el_underdraft != this->esu_status)   &&
         (el_overdraft  != this->esu_status) )
    {
        pcSched->pcSuSensorCfgD[iseg]->pcFMXWidthD->targ       =
        pcSched->pcSuSensorCfgD[iseg]->pcFMXWidthTargtD->width =
                                                    pcSched->pcSuPce->fx_hot_width;
    }

    //-------------------------------------
    // Set edger setup status
    //-------------------------------------
    pcSched->pcSetupD->esu_ok = (loop_count <= loop_limit);

    //--------------------------------------------------------------------
    // Recalculate load_act, if all or remaining edgers are not dummied
    //--------------------------------------------------------------------
    if ( !pcSched->pcSetupD->edgs_dummied )
    {
        fstpas = 1;

        cEdgD   *pcEdgD;

        float   *load_fac = new float [lstpas+1];
        for ( ps = fstpas; ps <= lstpas; ps++ )
        {
            if( (pcEdgD=pcSched->pcSupPassD[ps]->Drafting_EdgD(iseg)) != NULL )
            {
                load_fac[ps-fstpas] = pcEdgD->draft;
            }
            else
            {
                load_fac[ps-fstpas] = 0.0F;
            }
        }
        cRMLoad::Normalize_Load(
                        lstpas-fstpas+1,
                        load_fac);
        for ( ps = fstpas; ps <= lstpas; ps++ )
        {
            if( (pcEdgD=pcSched->pcSupPassD[ps]->Drafting_EdgD(iseg)) != NULL )
            {
                pcEdgD->load_act = load_fac[ps-fstpas];
            }
        }
        delete [] load_fac;

        //-------------------------------------
        // Determine if load has been modified.
        //-------------------------------------
        load_modified = false;
        for ( ps = fstpas; ps <= lstpas; ps++ )
        {
            if( (pcEdgD=pcSched->pcSupPassD[ps]->Drafting_EdgD(iseg)) != NULL )
            {
                if ( pcEdgD->load_init != 0.0F )
                {
                    float   load_error = fabs( 1.0 - (pcEdgD->load_act /
                                                      pcEdgD->load_init));
                    if ( load_error > Physcon.tol2 )
                    {
                        load_modified = true;
                        break;
                    }
                }
            }
        }
    }   //  END !pcSched->pcSetupD->edgs_dummied

    return (loop_count <= loop_limit);

}   // cESUD::Setup
