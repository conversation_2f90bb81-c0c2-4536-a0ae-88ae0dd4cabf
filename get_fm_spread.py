class ESUD:
    def __init__(self):
        self.pcWidth = None  # 宽度计算对象

    def get_fm_spread(self, sensor_cfg_d, sched):
        """
        计算FM总展宽量（在RMX温度下），并在轧制FM机架上分配冷态单个FM展宽量。
        该函数可用于设置和反馈模式。

        Args:
            sensor_cfg_d: 动态公共配置指针
            sched: 调度对象

        Returns:
            float: fm_spread（精轧展宽量）

        计算步骤：
        1. 获取热态厚度和宽度（优先使用反馈值）
        2. 将所有尺寸转换到RMX温度
        3. 计算FM压下量
        4. 预测FM展宽量
        """
        # 获取热态厚度和宽度（反馈模式优先）
        if sensor_cfg_d.pcFMXWidthD.feedback:
            # 使用反馈测量值
            fx_hot_thick = sensor_cfg_d.pcFMXWidthTargtD.thick
            fx_hot_width = sensor_cfg_d.pcFMXWidthTargtD.width
        else:
            # 使用目标值
            fx_hot_thick = sensor_cfg_d.pcFMXThickD.targ
            fx_hot_width = sensor_cfg_d.pcFMXWidthD.targ

        # 计算线性膨胀比（RMX温度/FMX温度）
        lin_exp_rat = (
            sensor_cfg_d.pcRMXWidthTargtD.expansion() /
            sensor_cfg_d.pcFMXWidthTargtD.expansion()
        )

        # 将尺寸转换到RMX温度
        thick = fx_hot_thick * lin_exp_rat
        width = fx_hot_width * lin_exp_rat
        
        # 计算FM压下量
        fm_draft = sensor_cfg_d.pcRMXWidthTargtD.thick - thick

        # 预测FM展宽量
        fm_spread = self.pcWidth.fm_spread(
            family=sensor_cfg_d.pcRMXWidthTargtD.pcPce.family,
            thick=thick,
            width=width,
            fm_draft=fm_draft,
            fm_sprd_coeff_0=sched.pcRAPP.state.fm_sprd_coeff[0],
            fm_sprd_coeff_1=sched.pcRAPP.state.fm_sprd_coeff[1],
            fm_sprd_coeff_2=sched.pcRAPP.state.fm_sprd_coeff[2],
            fm_sprd_coeff_3=sched.pcRAPP.state.fm_sprd_coeff[3]
        )

        return fm_spread

class Width:
    """宽度计算相关的辅助类"""
    
    def fm_spread(self, family, thick, width, fm_draft, 
                 fm_sprd_coeff_0, fm_sprd_coeff_1, 
                 fm_sprd_coeff_2, fm_sprd_coeff_3):
        """
        计算FM展宽量

        Args:
            family: 钢种族
            thick: RMX温度下的厚度
            width: RMX温度下的宽度
            fm_draft: FM压下量
            fm_sprd_coeff_0: FM展宽系数0
            fm_sprd_coeff_1: FM展宽系数1
            fm_sprd_coeff_2: FM展宽系数2
            fm_sprd_coeff_3: FM展宽系数3

        Returns:
            float: FM展宽量
        """
        # TODO: 实现具体的FM展宽量计算逻辑
        # 这里需要根据实际工艺要求实现展宽量的计算
        # 可能需要考虑：
        # 1. 钢种特性
        # 2. 材料尺寸
        # 3. 压下量
        # 4. 展宽系数
        return 0.0  # 临时返回值，需要替换为实际计算结果
