       //---------------------------------------------------------------
       // Copyright (c) 2006 by
       // Toshiba Mitsubishi-Electric Industrial Systems Corp. 
       // TMGE Automation Systems LLC, U.S.A.
       // Published in only a limited, copyright sense, and all
       // rights, including trade secret rights are reserved.
       //---------------------------------------------------------------
//-----------------------------------------------------------------------------
//
//     TITLE:         Roll Bite Model Class Implementation
//
//     FILE NAME:     MDS\SOURCE\ROLLBITE.CXX
//
//     PREPARED BY:   TMGE Automation Systems LLC, U.S.A.
//                      1501 Roanoke Blvd., Salem, Virginia, USA
//                    Toshiba Mitsubishi-Electric Industrial Systems Corp.
//                      Mita 43 MT Bldg. Mita 3-13-16, Minato-ku Tokyo, Japan
//
//     CUSTOMER:      STANDARD
//
//     SYSTEM:        Mill Automation System
//
//--------------------------------------------------------------------------------------
//
//     REVISIONS:
//     level  review date  author        change content
//     -----  -----------  ------------  -----------------------------------------------
//     1.0-0  20-Sep-2006  FM Williams   Original
//--------------------------------------------------------------------------------------

//-----------------------------------------------------------------------------
//
//
// ABSTRACT:  The Rollbite Class implements rolling model force, torque and power
//            calculations.
//            
//            The unit dimension is indicated in the
//            comments.   For more information,  refer to the standard unit
//            description for models programs.
//
//                             english         metric         SI
//         major_length         feet             m             m
//         minor_length        inches            mm           mm
//         volume_flow       inches**3/sec    mm**3/sec    mm**3/sec
//         linear_speed        feet/min        m/min        m/sec
//         major force          US tons        tonne        tonne
//         minor_force         lb(force)      kg(force)       kN
//         torque             lb(f)-ft        kg(f)-m        N-m
//         power                 kW             kW           kW
//         flow stress           psi          kg/sqmm        MPa
//         mech.power           ft-lb           kg-m          J
//         temperature          degF            degC         degC
//
//
//     PUBLIC FUNCTION          DESCRIPTION
//     -----------------------  -----------------------------------------------
//     cRollbite                Constructor.  Two constructors are provided.
//                              The first version without any parameter is
//                              designed for use without the hash table.  The 
//                              second version, provides facilities required
//                              for hash table registration.
//     ~cRollbite
//
//     cRollbite                Copy Constructor.  Make a simple copy of the
//                              object.  Not intended for the copy to be
//                              installed in the Hash table.
//
//     =                        Assignment operator.  Assigns the contents of
//                              one rollbite object to another.
//
//     Image                    Returns the image of the status_type enum.
//
//     Value                    Converts an ASCII representation of the
//                              status_type enum to its binary value.
//
//     Status_Type_Fmt          Formatter for status_type enum.
//
//     dump                     Standard dump() method.
//
//
//     Simplified Interface
//     --------------------
//     General purpose interface methods providing a simplified interface
//     to the Rollbite class.  Most of these  methods are almost identical
//     to the methods provided by the force class.  The simplified interface
//     expects mean flow stress adjusted by strain rate as an input.  It
//     does not account for variations in temperature or flow stress in
//     the roll bite.
//
//     Calculate()              Calculate() returns force, slip, and 
//                              arc of contact for the specified roll bite 
//                              conditions.  It is equivalent to the
//                              Calculate() method in the Force Class.
//
//     Calc_Power()             Calc_Power() returns force, slip, arc of contact,
//                              plus torque and 4 components of rolling power,
//                              ie, friction power, deformation power, tension
//                              power and torque power.    It is equivalent to
//                              the calcPwr() method in the force class.
//
//     Invert_Force()           Invert_Force() returns the coefficient of friction
//                              given the force and specified roll bite conditions.
//                              Slip is an optional input and if provided,
//                              Invert_Force() will try to satisfy both force and
//                              slip, adjusting flow stress and friction to achieve
//                              the desired result.  TBD
//
//     Approximate_Slip()       Calculate an approximation for the slip factor.
//                              This method relies on Sim's equation for the
//                              neutral point and is fast to calculate.
//
//
//     Accessor methods to return calculated results
//     ---------------------------------------------
//     These methods return the specified calculated quantities.  If the 
//     calculated quantities are determined to be invalid, a value of 0.0
//     will be returned.
//
//     Status()                 Returns the last calculation status
//
//     Precision()              Returns the current precision of calculations
//
//     Pressure_Valid()         Returns the roll pressure distribution validity flag
//
//     Force()                  Returns the calculated roll force
//
//     Force_Valid()            Returns the roll force validity flag
//
//     Slip()                   Returns the calculated slip
//
//     Draft_Comp()             Returns the calculated draft compensation
//
//     Arcon()                  Returns the calculated arc of contact length
//
//     ExThick_Limit()          Returns the minimum thickness that can be
//                              rolled.
//
//     Neutral_Angle()          Returns the calculated neutral point angle
//
//     Neutral_Pressure()       Returns the calculated neutral point angle
//
//     Neutral_Thick()          Returns the calculated neutral point angle
//
//     Entry_Angle()            Returns the calculated entry angle
//
//     Deform_Radius()          Returns the calculated deformed roll radius
//
//     Torque()                 Returns the torque per unit width
//
//     Torque_Valid()           Returns the torque validity flag
//
//     Deform_pwr()             Returns the deformation power
//
//     Friction_Pwr()           Returns the friction power
//
//     Tension_Power()          Returns the tension power
//
//     Torque_Power()           Returns the torque power
//
//     Power_Valid()            Returns the power validity flag
//
//
//-----------------------------------------------------------------------------

#define __ROLLBITE_MAINLINE__

//------------------------------
// C++ standard library includes
//------------------------------
#include <math.h>

// mds includes
#include "alarm.hxx"
#include "objhash.hxx"
#include "physcon.hxx"
#include "rbheat.hxx"
#include "flowstress.hxx"
#include "rollbite.hxx"

#ifdef WIN32
	#ifdef _DEBUG
	#define new DEBUG_NEW
	#endif
#endif

#ifdef WIN32
    #pragma warning(disable: 4244) // double to float conversion (NT thinks constants are doubles)
    #pragma warning(disable: 4305) // truncation from 'const double' to 'const float'
    #pragma warning(disable: 4505) // disable unreferenced inline function warning
#endif

#define DEFAULT_PRECISION   (0.001F)   // The default global precision (.1%)
                                       // required for the roll pressure
#define MINIMUM_SLICES      (7)        // The default minimum number of slices to be taken
                                       // NOTE: May have problems with 2D minimization
                                       // if the configured minimum is less than 15,
                                       // due to small wrinkles in flowstress/cof/objective surface.

//-----------------------------------------------------------------------------
// Schema definitions for the cRollbite Class
//-----------------------------------------------------------------------------
// Data schema for the rollbite_type structure.
cSchema::schema_type cRollbite::sSlice_schema[]=
{
    //Next  Enum  Schema details                                                Fmt  Units        Comment
    //====  ====  ========================================                      ==== ===========  ==================================================
    { NULL, NULL, SCHEMA_T(cRollbite::slice_type,int,max_slices),               "", "",           "Max slices allowed in distribution" },
    { NULL, NULL, SCHEMA_T(cRollbite::slice_type,int,num_slices),               "", "",           "Number of slices in distribution" },
    { NULL, NULL, SCHEMA_P(cRollbite::slice_type,double,phi,num_slices),        "", "",           "Slice angle from exit plane" },
    { NULL, NULL, SCHEMA_P(cRollbite::slice_type,double,flows,num_slices),      "", "",           "Flow stress at angle phi" },
    { NULL, NULL, SCHEMA_P(cRollbite::slice_type,double,bite_thick,num_slices), "", "",           "Bite thickness at angle phi" },
    { NULL, NULL, SCHEMA_P(cRollbite::slice_type,double,strain_rate,num_slices),"", "",           "Strain rate at angle phi" },
    { NULL, NULL, SCHEMA_P(cRollbite::slice_type,double,strip_speed,num_slices),"", "",           "Strip speed at angle phi" },
    { NULL, NULL, SCHEMA_P(cRollbite::slice_type,double,tang_pressure,num_slices),"", "",         "Tangential pressure at angle phi" },
    { NULL, NULL, SCHEMA_P(cRollbite::slice_type,double,norm_pressure,num_slices),"", "",         "Normal pressure at angle phi" },
    { NULL, NULL, SCHEMA_P(cRollbite::slice_type,double,temp_mean,num_slices),  "", "",           "Mean temperature at angle phi" },
    { NULL, NULL, SCHEMA_P(cRollbite::slice_type,double,temp_surf,num_slices),  "", "",           "Surface temperature at angle phi" },
    { 0 }   // terminate list
};
    
// Data schema for the rollbite_type structure.
cSchema::schema_type cRollbite::sRollbite_schema[]=
{
    //Next  Enum  Schema details                                        Fmt  Units          Comment
    //====  ====  ========================================              ==== ===========    ==================================================
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,int,min_slices),    "",  "",            "Minimum number of slices to use" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,float,pr_precsn),   "",  "",            "Precision of the pressure calculations" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,float,frc_precsn),  "",  "",            "Precision of the force calculations" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,bool,precise_frc),  "",  "",            "Consider tangential component acting through deformed radius" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,bool,precise_trq),  "",  "",            "Consider normal component acting through deformed radius" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,bool,dbg_strain),   "",  "",            "Do debugging instantaneous strain rate calcs" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,bool,neut_sims),    "",  "",            "Include approximate neutral point calc by Sim's formula" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,bool,elast_calcs),  "",  "",            "Include elastic calcs for debugging" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,bool,err_bounds),   "",  "",            "Include error bounds by C-K RK4 algorithm" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,bool,precise_derivs),"", "",            "Include precise cos/sin in derivative calcs" },

    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,float,ftol),        "", "",            "fractional convergence tolerance ( 0.05% )" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,int,iiter),         "", "",            "number of moves within any call to amebsa" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,int,riter),         "", "",            "number of moves before a restart" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,int,max_moves),     "", "",            "max number of moves of the simplex for which we reduce temp" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,int,max_iter),      "", "",            "maximum number of iterations allowed" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,float,tfac),        "", "",            "pow factor to control how fast temperature is reduced" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,float,tdiv),        "", "",            "divide initial objective fn by this to get start temperature" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,bool,replace),      "", "",            "if true, use best ever point to replace a point on the simplex every riter tries" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,bool,debug),        "", "",            "dump out some debug trace ( 2D minimization only )" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,bool,alternate_power),        "", "",            "Utilize alternate power calculation" },

    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,double,precision),  "", "",             "The global precision reuired, (default 1%)" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,int,init_slices),   "", "",             "Initial guess for number of slices in rollbite" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,bool,exit_side),    "", "",             "Flag for exit side calcs" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,bool,inputs_valid), "", "",             "Inputs valid flag" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,double,wrdiam),     "", "minor_length", "Undeformed work roll diameter" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,double,hitchcock),  "", "pressure",     "Hitchcock's constant" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,double,entry_thk),  "", "minor_length", "Entry thickness" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,double,exit_thk),   "", "minor_length", "Exit thickness" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,double,pu_draft),   "", "",             "Per unit draft" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,double,flows_mean), "", "pressure",     "Mean flow stress" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,double,temp_mean),  "", "C_F",          "Mean temperature" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,double,entry_tension),"","pressure",     "Entry tension" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,double,exit_tension),"", "pressure",     "Exit tension" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,double,strip_modulus),"", "pressure",    "Strip elastic modulus" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,double,strip_poisson),"", "",            "Strip poisson ratio" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,double,friction_coeff),"","",            "Mean coefficient of friction" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,double,vroll),       "", "m/sec_ft/sec", "Roll surface velocity" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,int,matl_code),     "", "",             "Material code" },
    { NULL, NULL, SCHEMA_PO(cRollbite::rollbite_type,cRbheat,pcEtemp),  "", "",             "pointer to the roll bite entry temperature class" },

    // Elastic effects
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,double,entry_str),   "", "minor_length", "Entry thickness to plastic zone" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,double,exit_str),    "", "minor_length", "Exit thickness from plastic zone" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,double,min_thk),     "", "minor_length", "Minimum gap thickness" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,double,entry_ten_pls), "", "pressure",   "Entry tension to plastic zone" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,double,exit_ten_pls),  "", "pressure",   "Exit tension to plastic zone" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,double,elfrc_entry),  "", "minor_force", "Entry side elastic force/unit width" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,double,elfrc_exit),   "", "minor_force", "Exit side elastic force/unit width" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,double,hitch_draft),  "", "minor_length","Draft term for Bland & Ford modified Hitchcock equation" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,double,e_force),      "", "minor_force", "Total force due to elastic zones" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,double,e_torque),      "", "minor_torque","Total torque due to elastic zones" },

    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,bool,xtemp_valid),  "", "",             "Exit temperature distribution valid" },
    { NULL, NULL, SCHEMA_PO(cRollbite::rollbite_type,cRbheat,pcXtemp),      "", "",             "pointer to the roll bite exit temperature class" },

    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,double,phin_guess),  "",  "radians", "First guess at neutral angle" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,double,phie_guess),  "",  "radians", "First guess at entry angle" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,double,slip_guess),  "",  "radians", "First guess at slip factor" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,bool,pressure_valid), "", "",           "Pressure distribution valid" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,double,phi_entry),   "",  "radians",      "Entry angle" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,double,phi_neut),   "",  "radians",      "Neutral angle" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,double,phi_neut_sims),   "",  "radians", "Neutral angle by Sim's formula" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,double,norm_pressure_neut),   "",  "", "[-] normal pressure at neutral point" },

    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,int,idx_neut),     "",  "",             "Slice ndex to the neutral point" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,int,nslice_entry), "",  "",             "Slices on entry side of neutral point" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,int,nslice_exit),  "",  "",             "Slices on exit side of neutral point, incl np" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,double,wrdeform),   "",  "minor_length", "Deformed work roll diameter" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,double,force_guess),"",  "minor_length", "Guess at the force" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,int,force_iter),   "",  "",             "Number of iterations in the force loop" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,double,vstrip_entry),      "", "m/sec_ft/sec", "Strip entry velocity" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,double,vstrip_exit),      "", "m/sec_ft/sec", "Strip exit velocity" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,double,mean_strain_guess),      "", "1/sec", "Mean strain rate, guess using undeformed radius" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,double,mean_strain),      "", "1/sec", "Mean strain rate, deformed radius used" },
    { cRollbite::sSlice_schema, NULL, SCHEMA_T(cRollbite::rollbite_type,cRollbite::slice_type,entry), "",  "",             "Entry side distribution" },
    { cRollbite::sSlice_schema, NULL, SCHEMA_T(cRollbite::rollbite_type,cRollbite::slice_type,exit),  "",  "",             "Exit side distribution" },
    { cRollbite::sSlice_schema, NULL, SCHEMA_T(cRollbite::rollbite_type,cRollbite::slice_type,final), "",  "",             "Final distribution" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,bool,slip_valid),  "",  "",             "Slip calculation valid" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,double,slip),      "",  "",             "Slip" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,bool,arcon_valid), "",  "",             "Arc of contact calculation valid" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,double,arcon),      "",  "mm_in",             "Arc of contact" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,bool,exthklim_valid),"", "",             "Exit thickness limit calculation valid" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,double,exthk_lim),  "",  "mm_in",             "Exit thickness limit (min exit thickness that can be rolled)" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,bool,force_valid), "",  "",             "Force calculation valid" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,double,force),      "",  "mton/mm_eton/in_kN/mm",             "Force per unit width" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,double,force_minor),      "",  "kg/mm_lbf/in_N/mm",             "Force per unit width" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,bool,torque_valid),"",  "",             "Torque calculation valid" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,double,torque),     "",  "",             "Torque per unit width" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,bool,frict_pwr_valid),"",  "",          "Friction power calculation valid" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,double,frict_pwr),     "",  "",          "Friction power per unit width" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,bool,deform_pwr_valid),"",  "",         "Deformation power calculation valid" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,double,deform_pwr),     "",  "",         "Deformation power per unit width" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,bool,tension_pwr_valid),"",  "",        "Tension power calculation valid" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,double,tension_pwr),     "",  "",        "Tension power per unit width" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,bool,torque_pwr_valid),"",  "",         "Torque power calculation valid" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,double,torque_pwr),     "",  "",         "Torque power per unit width" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,bool,shaft_pwr_valid), "",  "",         "Shaft power calculation valid" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,double,shaft_pwr),      "",  "",         "Shaft power per unit width" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,double,pwr_ratio),      "",  "",         "ratio (shaft_pwr/torque_pwr)" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,bool,dforce_dengag_valid),"",  "",       "DForce_DEntryThick calculation valid" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,double,dforce_dengag),     "",  "",      "DForce_DEntryThick sensitivity factor" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,bool,dforce_dexgag_valid),"",  "",       "DForce_DExitThick calculation valid" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,double,dforce_dexgag),     "",  "",      "DForce_DExitThick sensitivity factor" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,bool,dforce_dentens_valid),"",  "",      "DForce_DEntryTens calculation valid" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,double,dforce_dentens),     "",  "",     "DForce_DEntryTens sensitivity factor" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,bool,dforce_dextens_valid),"",  "",      "DForce_DExitTens calculation valid" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,double,dforce_dextens),     "",  "",     "DForce_DExitTens sensitivity factor" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,bool,dforce_ddraft_valid),"",  "",       "DForce_DDraft calculation valid" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,double,dforce_ddraft),     "",  "",      "DForce_DDraft sensitivity factor" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,bool,dtorquepwr_dexgag_valid),"",  "",   "DTorquePwr_DExitThick calculation valid" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,double,dtorquepwr_dexgag),     "",  "",  "DTorquePwr_DExitThick sensitivity factor" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,bool,dtorquepwr_ddraft_valid),"",  "",   "DTorquePwr_DDraft calculation valid" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,double,dtorquepwr_ddraft),     "",  "",  "DTorquePwr_DDraft sensitivity factor" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,bool,dslip_dengag_valid),"",  "",        "DSlip_DEntryThick calculation valid" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,double,dslip_dengag),     "",  "",       "DSlip_DEntryThick sensitivity factor" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,bool,dslip_dexgag_valid),"",  "",        "DSlip_DExitThick calculation valid" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,double,dslip_dexgag),     "",  "",       "DSlip_DExitThick sensitivity factor" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,bool,dslip_dentens_valid),"",  "",       "DSlip_DEntryTension calculation valid" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,double,dslip_dentens),     "",  "",      "DSlip_DEntryTension sensitivity factor" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,bool,dslip_dextens_valid),"",  "",       "DSlip_DExitTension calculation valid" },
    { NULL, NULL, SCHEMA_T(cRollbite::rollbite_type,double,dslip_dextens),     "",  "",      "DSlip_DExitTension sensitivity factor" },
    { NULL, cRollbite::Status_Type_Fmt, SCHEMA_T(cRollbite::rollbite_type,cRollbite::status_type,status), "",  "",          "Last rollbite status" },
    { 0 }   // terminate list
};

// Data schema for the cRollbite class.
cSchema::schema_type cRollbite::cRollbite_schema[]=
{
    //Next                         Enum  Schema details                                                           Fmt  Units        Comment
    //====                         ====  ========================================                                 ==== ===========  ==================================================
    { cRollbite::sRollbite_schema, NULL, SCHEMA_T(cRollbite,cRollbite::rollbite_type,bite),                       "",  "",          "" },

    { 0 }   // terminate list
};

// Link all the schema's together
cSchema::schema_name_type cRollbite::sSchema[]=
{
    {	"cRollbite",					    // Name
		sizeof(cRollbite),	                // Size
		cRollbite_schema,					// Schema
		false,								// Packed
		true,								// Pointer
		false,								// read_only
		"cRollbite schema name def comment",// comment
        0                       			// offset to config data
	},    // Note: Packed has been set false, pointer set true

    {	"rollbite_type",					// Name
		sizeof(cRollbite::rollbite_type),	// Size
		sRollbite_schema,					// Schema
		false,								// Packed
		true,								// Pointer
		false,								// read_only
		"rollbite_type comment",			// comment
        0			                        // offset to config data
	},    // Note: Packed has been set false, pointer set true

    {	"slice_type",						// Name
		sizeof(cRollbite::slice_type),		// Size
		sSlice_schema,						// Schema
		false,								// Packed
		true,								// Pointer
		false,								// read_only
		"slice_type comment",				// comment
		0									// offset to config data
	},    // Note: Packed has been set false, pointer set true

    { 0 } // terminate list
};


//-----------------------------------------------------------------------------
// Public member functions
//-----------------------------------------------------------------------------

//---------------------------------------------------------------
// Declare CONSTRUCTOR and DESTRUCTOR for class cRollbite.
//---------------------------------------------------------------
// Use this constructor if no hash table support required
cRollbite::cRollbite(void)
{
    Set_Class_Name("cRollbite");
    Set_Schema("cRollbite",sSchema);
    memset(&bite, 0, sizeof(bite));
    memset(&bite.entry, 0, sizeof(bite.entry));
    memset(&bite.exit,  0, sizeof(bite.exit));
    memset(&bite.final, 0, sizeof(bite.final));

    bite.init_slices = bite.min_slices = MINIMUM_SLICES-2;
    bite.precision  = DEFAULT_PRECISION;
    bite.pr_precsn  = DEFAULT_PRECISION;
    bite.frc_precsn = DEFAULT_PRECISION;

    bite.precise_frc = INCLUDE_PRECISE_FORCE;
    bite.precise_trq = INCLUDE_PRECISE_TORQUE;
    bite.dbg_strain  = INCLUDE_STRAIN_RATE;
    bite.neut_sims   = INCLUDE_NEUTRAL_SIMS;
    bite.elast_calcs = INCLUDE_ELASTIC_CALCS;
    bite.err_bounds  = INCLUDE_ERROR_BOUNDS;
    bite.precise_derivs = INCLUDE_PRECISE_DERIVS;

    bite.ftol       = 0.0005;   // fractional convergence tolerance ( 0.05% )
    bite.iiter      = 5;        // number of moves within any call to amebsa
    bite.riter      = 20;       // number of moves before a restart
    bite.max_moves  = 40;       // max number of moves of the simplex for which we reduce temp
    bite.max_iter   = 200;      // maximum number of iterations allowed
    bite.tfac       = 4.0;      // pow factor to control how fast temperature is reduced
    bite.tdiv       = 5.0;      // divide initial objective fn by this to get start temperature
    bite.replace    = false;    // if true, use best ever point to replace a point on the
                                // simplex every riter tries
    bite.debug      = false;    // dump out some debug trace ( 2D minimization only )
    bite.alternate_power = false;
}
// Use this constructor if hash table support required
cRollbiteCfg    *cRollbite::pcStaticRollbiteCfg = NULL;
cRollbite::cRollbite( const MString        &objName,
                        const objTypEnum     turncodes,
                        const objPosEnum     position,
                        void                *pHash,
                        const int            size )
                        : cBase( objName, turncodes, position, pHash, size, 0 )

{
    Set_Class_Name("cRollbite");
    Set_Schema("cRollbite",sSchema);
    memset(&bite, 0, sizeof(bite));
    memset(&bite.entry, 0, sizeof(bite.entry));
    memset(&bite.exit,  0, sizeof(bite.exit));
    memset(&bite.final, 0, sizeof(bite.final));

    bite.init_slices = bite.min_slices = MINIMUM_SLICES-2;
    bite.pr_precsn   = DEFAULT_PRECISION;
    bite.frc_precsn  = DEFAULT_PRECISION;
    bite.precise_frc = INCLUDE_PRECISE_FORCE;
    bite.precise_trq = INCLUDE_PRECISE_TORQUE;
    bite.dbg_strain  = INCLUDE_STRAIN_RATE;
    bite.neut_sims   = INCLUDE_NEUTRAL_SIMS;
    bite.elast_calcs = INCLUDE_ELASTIC_CALCS;
    bite.err_bounds  = INCLUDE_ERROR_BOUNDS;
    bite.precise_derivs = INCLUDE_PRECISE_DERIVS;

    bite.ftol       = 0.0005;   // fractional convergence tolerance ( 0.05% )
    bite.iiter      = 5;        // number of moves within any call to amebsa
    bite.riter      = 20;       // number of moves before a restart
    bite.max_moves  = 40;       // max number of moves of the simplex for which we reduce temp
    bite.max_iter   = 200;      // maximum number of iterations allowed
    bite.tfac       = 4.0;      // pow factor to control how fast temperature is reduced
    bite.tdiv       = 5.0;      // divide initial objective fn by this to get start temperature
    bite.replace    = false;    // if true, use best ever point to replace a point on the
                                // simplex every riter tries
    bite.debug      = false;    // dump out some debug trace ( 2D minimization only )
    bite.alternate_power = false;

    // Find the configuration class in the global hash table
    // and re-initialize the default values with configured data
    // if the configuration object is found.  Only one rollbite
    // configuration object may be specified, since the first
    // one found in the hash table is used.
    int index = 0;
    if ( NULL == pcStaticRollbiteCfg )
    {
        pcStaticRollbiteCfg = (cRollbiteCfg *)Objhash.Find_Class_In_Hash(
                                                    index,
                                                    Objhash.cmnGetPtr(),
                                                    MString("cRollbiteCfg"));
    }
    if ( pcStaticRollbiteCfg != NULL )
    {
        bite.init_slices    = bite.min_slices = pcStaticRollbiteCfg->min_slices-2;
        bite.pr_precsn      = pcStaticRollbiteCfg->pr_precsn;
        bite.frc_precsn     = pcStaticRollbiteCfg->frc_precsn;
        bite.precise_frc    = pcStaticRollbiteCfg->precise_frc;
        bite.precise_trq    = pcStaticRollbiteCfg->precise_trq;
        bite.dbg_strain     = pcStaticRollbiteCfg->dbg_strain;
        bite.neut_sims      = pcStaticRollbiteCfg->neut_sims;
        bite.elast_calcs    = pcStaticRollbiteCfg->elast_calcs;
        bite.err_bounds     = pcStaticRollbiteCfg->err_bounds;
        bite.precise_derivs = pcStaticRollbiteCfg->precise_derivs;

        bite.ftol           = pcStaticRollbiteCfg->ftol;
        bite.iiter          = pcStaticRollbiteCfg->iiter;
        bite.riter          = pcStaticRollbiteCfg->riter;
        bite.max_moves      = pcStaticRollbiteCfg->max_moves;
        bite.max_iter       = pcStaticRollbiteCfg->max_iter;
        bite.tfac           = pcStaticRollbiteCfg->tfac;
        bite.tdiv           = pcStaticRollbiteCfg->tdiv;
        bite.replace        = pcStaticRollbiteCfg->replace;
        bite.debug          = pcStaticRollbiteCfg->debug;
        bite.alternate_power = pcStaticRollbiteCfg->alternate_power;
    }

    bite.precision  = bite.pr_precsn;

}


cRollbite::~cRollbite()
{
    Delete_Distribution(&bite.entry);
    Delete_Distribution(&bite.exit);
    Delete_Distribution(&bite.final);
    if ( bite.pcXtemp != NULL )
    {
        delete bite.pcXtemp;
    }
}


//--------------------------------------
// Copy CONSTRUCTOR for class cRollbite.
//--------------------------------------
cRollbite::cRollbite( const cRollbite& source)
{
    if ( this != &source )
    {
        // Assign source to destination
        this->bite = source.bite;
        // Clear out the copy of distributions in destination
        this->bite.entry.max_slices = this->bite.entry.num_slices = 0;
        this->bite.exit.max_slices  = this->bite.exit.num_slices  = 0;
        this->bite.final.max_slices = this->bite.final.num_slices = 0;
        // ... and copy the distributions, allocating new memory in
        // the destination object.
        Copy_Distribution(&this->bite.entry, &source.bite.entry);
        Copy_Distribution(&this->bite.exit,  &source.bite.exit);
        Copy_Distribution(&this->bite.final, &source.bite.final);
    }
}


//-----------------------------------------
// Assignment operator for class cRollbite.
//-----------------------------------------
cRollbite& cRollbite::operator = ( const cRollbite& source)
{
    if ( this != &source )
    {
        // Delete any distributions in destination object,
        // prevents memory leaks.
        Delete_Distribution(&this->bite.entry);
        Delete_Distribution(&this->bite.exit);
        Delete_Distribution(&this->bite.final);
        // Assign source to destination
        this->bite = source.bite;
        // Clear out the copy of distributions in destination
        this->bite.entry.max_slices = this->bite.entry.num_slices = 0;
        this->bite.exit.max_slices  = this->bite.exit.num_slices  = 0;
        this->bite.final.max_slices = this->bite.final.num_slices = 0;
        // ... and copy the distributions, allocating new memory in
        // the destination object.
        Copy_Distribution(&this->bite.entry, &source.bite.entry);
        Copy_Distribution(&this->bite.exit,  &source.bite.exit);
        Copy_Distribution(&this->bite.final, &source.bite.final);
    }
    return (*this);
}


//-------------------------------------------------------------------------
// Dump the object data.  Accessible from the base object.
//-------------------------------------------------------------------------
void cRollbite::dump ( const bool composed )
{
    // Generically dump the data
    Dump_Data(stdout, "rollbite_type", (void *)(&bite), 0, "");

    if (composed)
    {   
        ; // NULL
    }
}

//-------------------------------------------------------------------------
// Print an error message, called if Image fn passed an out of bounds enum.
//-------------------------------------------------------------------------
static  void    Print_Image_Error(cRollbite::status_type rb_status)
{
    EMSG << "rb_status out of range " << (int) cRollbite::rb_undef
         << " - " << (int)(cRollbite::rb_guardvalue)-1
         << ", passed = " << (int) rb_status
         << END_OF_MESSAGE;
}

//-------------------------------------------------------------------------
// status_type_image is used for returning an image of status_type.  The
// next to last char* MUST always be "guardvalue".  The last char* MUST be
// "\0".
//-------------------------------------------------------------------------
#define T(x) #x
    const char* cRollbite::status_type_image[] = { ROLLBITE_STATUS_TYPE , "\0" };
#undef  T


//-------------------------------------------------------------------------
// Image function, returns ASCII string corresponding to enum.
//-------------------------------------------------------------------------

const   char* cRollbite::Image(const cRollbite::status_type rb_status)
{
    status_type     status = rb_status;

    if (rb_status >= rb_guardvalue)
    {
        status = rb_guardvalue;
        Print_Image_Error(rb_status);
    }
    if (rb_status < rb_undef)
    {
        status = rb_undef;
        Print_Image_Error(rb_status);
    }
    //return (strchr(cRollbite::status_type_image[(int)status],'_') + 1);
    return (cRollbite::status_type_image[(int)status]);

} // char* cRollbite::Image(const cForce::status_type frc_status)


//-------------------------------------------------------------------------
// Value function, returns enum corresponding to ASCII string.
//-------------------------------------------------------------------------
cRollbite::status_type   cRollbite::Value(const char *rb_image)
{
    int     i;

    for ( i=(int)(rb_undef); i<=(int)(rb_guardvalue); i++ )
    {
        if ( strcmp(status_type_image[i], rb_image) == 0 )
            return (cRollbite::status_type)(i);
    }
    EMSG << "rb_image '" << rb_image << "' not valid" << END_OF_MESSAGE;

    return rb_undef;

} // cRollbite::status_type   cRollbite::Value(const char *rb_image)

//----------------------------------------------------------
// cRollbite::Status_Type_Fmt - Must be static
//    dir
//      FMT_ENCODE    encode the binary structure data to an ASCII string
//      FMT_DECODE    decode the ASCII string to binary structure data
//      FMT_MIN_ENUM  Put min value of enum into p
//      FMT_MAX_ENUM  Put max value of enum into p
///----------------------------------------------------------
void    cRollbite::Status_Type_Fmt(
                int dir,                // 0=encode, 1=decode
                void *p,                // pointer to structure data member
                cSchema::schema_type *schema,  // pointer to schema
                char *buff)             // pointer to ASCII character buffer
{
    switch (dir)
    {
      case FMT_ENCODE:   // encode the binary structure data to an ASCII string
      {
        // encode the binary structure data to an ASCII string
        const   char    *side;

        side = cRollbite::Image(*((status_type *)(p)));
        strcpy(buff, side);
        break;
      }
      case FMT_DECODE:   // decode the ASCII string to binary structure data
        // decode the ASCII string to binary structure data
        *((status_type *)(p)) = cRollbite::Value(buff);
        break;
      case FMT_MIN_ENUM: // Put min value of enum into p
          *((status_type *)(p)) = rb_undef;
        break;
      case FMT_MAX_ENUM: // Put min value of enum into p
        *((status_type *)(p)) = rb_guardvalue;
        break;
      default:
          EMSG << " dir out of range " << END_OF_MESSAGE; 
    }
}


//-----------------------------------------------------------------------------
//     Simplified Interface
//     --------------------
//     General purpose interface methods providing a simplified interface
//     to the Rollbite class.  These methods are almost identical to the
//     methods provided by the force class.
//-----------------------------------------------------------------------------


//----------------------------------------------------------------------
// This function calculates the force, slip, and the arc of contact			计算轧制力、前后滑、接触弧长
//----------------------------------------------------------------------
bool cRollbite::Calculate (
              cRollbite::status_type *status,          // OUT status from calculations                          输出
              float       *force,           // OUT rolling force/width 
                                            //      [major_force]/[minor_length]
              float       *slip,            // OUT exit slip ratio [-]
              float       *arcon,           // OUT length of arc [minor_length]

        const float       wrdiam,           // IN undeformed work roll diam.                                  输入
                                            //      [minor_length]
        const float       entry_thk,        // IN entry thickness [minor_length]
        const float       exit_thk,         // IN exit thickness [minor_length]
        const float       flows,            // IN flow stress (const) [pressure]
        const float       hitchcock,        // IN Hitchcock's constant[pressure]
        const float       entry_tension,    // IN entry tension [pressure]			
        const float       exit_tension,     // IN exit tension [pressure]			
        const float       strip_modulus,    // IN strip elastic mod. [pressure]
        const float       strip_poisson,    // IN strip poisson ratio [-]	                           													
        const float       friction_coeff )  // IN coeff of friction [-]			             
{
    MDSVERIFYNAME(status,"status")
    MDSVERIFYNAME(force,"force")
    MDSVERIFYNAME(slip,"slip")
    MDSVERIFYNAME(arcon,"arcon")

    //-----------------------------------------
    // Initialize all OUT parameters 
    //-----------------------------------------
    *status         = bite.status = rb_invalid;
    *slip           = 1.0;
    *force          = 0.0;
    *arcon          = 0.0;

    //-------------------------------------------------------------------
    // Detect the condition of no reduction,  set output variables and
    // return immediately.  No force, torque or power.
    //-------------------------------------------------------------------
    if ( (exit_thk >= entry_thk) ||
         (exit_thk <= 0.0 )      ||
         (entry_thk <= 0.0 ) )
    {  
        if ( (exit_thk == entry_thk) && (exit_thk != 0.0) )			无压下轧制，厚度压下为0，且厚度不为0
        {
            //  Set valid conditions for a zero draft condition, does not set torque/pwr

            Invalidate_All_Calcs();						重置内部计算状态（清除历史计算）

            Set_Input_Data(						初始化输入参数，但不触发实际计算（存储输入参数）
                wrdiam,             // IN undeformed work roll diam. [minor_length]
                hitchcock,          // IN Hitchcock's constant [pressure]
                entry_thk,          // IN entry thickness [minor_length]
                exit_thk,           // IN exit thickness [minor_length]
                flows,              // IN mean flow stress (const) [pressure]
                0.0,                // IN mean temperature (const) [C_F]
                entry_tension,      // IN entry tension [pressure]
                exit_tension,       // IN exit tension [pressure]
                strip_modulus,      // IN strip elastic mod. [pressure]
                strip_poisson,      // IN strip poisson ratio [-]
                friction_coeff,     // IN mean coeff of friction [-]
                1.0 * Physcon.vel_time,// [m/sec_ft/sec] surface velocity of roll ( guess )
                1);                 // material code

            *status = bite.status = rb_valid;
            bite.slip  = 1.0;						滑移率为 1.0（带钢速度与轧辊速度相同）
            bite.slip_valid = true;
            bite.arcon = 0.0;						轧制力为零，但轧辊与带钢间可能存在接触（arcon = 0.0）
            bite.arcon_valid = true;
            bite.force = 0.0;						轧制力为零，但轧辊与带钢间可能存在接触（arcon = 0.0）
            bite.force_valid = true;
            bite.phi_entry = 0.0;
            bite.phi_neut   = 0.0;
            bite.wrdeform  = wrdiam;
        }

        return (bite.status == rb_valid);
 
    } // end if

    //-------------------------------------------------------------------
    // Detect the condition of tension greater than flow stress			张力合理性检查
    // and return immediately.  No force, torque or power.			从物理意义上限制张力的上限 ——张力不能超过材料的流动应力（flows）
    //-------------------------------------------------------------------
    if ( entry_tension >= flows ) 				流动应力（flows）是材料抵抗塑性变形的最小应力，若张力（entry_tension/exit_tension）大于等于流动应力，
    {  							材料会被直接拉断（而非正常轧制），此时轧制过程无法进行
        *status = bite.status = rb_invalid_entry_tension;
        return false;
    } // end if

    if ( exit_tension >= flows ) 
    {  
        *status = bite.status = rb_invalid_exit_tension;
        return false;
    } // end if


    Invalidate_All_Calcs();

    Set_Input_Data(
        wrdiam,             // IN undeformed work roll diam. [minor_length]
        hitchcock,          // IN Hitchcock's constant [pressure]
        entry_thk,          // IN entry thickness [minor_length]
        exit_thk,           // IN exit thickness [minor_length]
        flows,              // IN mean flow stress (const) [pressure]
        0.0,                // IN mean temperature (const) [C_F]
        entry_tension,      // IN entry tension [pressure]
        exit_tension,       // IN exit tension [pressure]
        strip_modulus,      // IN strip elastic mod. [pressure]
        strip_poisson,      // IN strip poisson ratio [-]
        friction_coeff,     // IN mean coeff of friction [-]
        1.0 * Physcon.vel_time,// [m/sec_ft/sec] surface velocity of roll ( guess )
        1);                 // material code

    if ( false == Calculate_Force() )
    {
        *status = bite.status;
        return false;
    }

    *force = Force();  					提取计算结果（轧制力、滑移率、接触弧长度）
    *slip = Slip();
    *arcon = Arcon();

    *status = bite.status = rb_valid;

    return true;

} // end calculate

//----------------------------------------------------------------------
// This function calculates the force, slip, and the arc of contact
//----------------------------------------------------------------------
bool cRollbite::Calculate (
              cRollbite::status_type *status,          // OUT status from calculations
        const float       wrdiam,           // IN undeformed work roll diam.
                                            //      [minor_length]
        const float       entry_thk,        // IN entry thickness [minor_length]
        const float       exit_thk,         // IN exit thickness [minor_length]
        const float       flows,            // IN flow stress (const) [pressure]
        const float       hitchcock,        // IN Hitchcock's constant[pressure]
        const float       entry_tension,    // IN entry tension [pressure]
        const float       exit_tension,     // IN exit tension [pressure]
        const float       strip_modulus,    // IN strip elastic mod. [pressure]
        const float       strip_poisson,    // IN strip poisson ratio [-]
        const float       friction_coeff )  // IN coeff of friction [-]
{
    float   force, slip, arcon;     // throw away results, they are in object

    return  Calculate(
                status,
                &force,
                &slip,
                &arcon,
                wrdiam,
                entry_thk,
                exit_thk,
                flows,
                hitchcock,
                entry_tension,
                exit_tension,
                strip_modulus,
                strip_poisson,
                friction_coeff);
}

//----------------------------------------------------------------------------
// This function calculates the force, torque, slip, arc of contact and power.
//----------------------------------------------------------------------------
bool    cRollbite::Calc_Power (
          status_type *status,         // OUT status from calculations
          float       *force,          // OUT rolling force p.u. width
                                       //     [major_force]/[minor_length]
          float       *slip,           // OUT exit slip ratio
          float       *arcon,          // OUT length of arc of contact
                                       //     [minor_length]
          float       *torque,         // OUT torque p.u. width
                                       //     [torque]/[minor_length]
          float       *friction_pwr,   // OUT friction power
                                       //     [power_kw]/[volume_flow]
          float       *deform_pwr,     // OUT deformation power 
                                       //     [power_kw]/[volume_flow]
          float       *torque_pwr,     // OUT torque power
                                       //     [power_kw]/[minor_length]
          float       *tension_pwr,    // OUT tension power/width
                                       //     [power_kw]/[volume_flow]
         const float  wrdiam,          // IN undeformed work roll [minor_length]
         const float  entry_thk,       // IN entry thickness [minor_length]
         const float  exit_thk,        // IN exit thickness [minor_length]
         const float  flows,           // IN flow stress (const) [pressure]
         const float  hitchcock,       // IN Hitchcock's constant [pressure]
         const float  entry_tension,   // IN entry tension [pressure]
         const float  exit_tension,    // IN exit tension [pressure]
         const float  strip_modulus,   // IN strip elastic modulus [pressure]
         const float  strip_poisson,   // IN strip poisson ratio [-]
         const float  friction_coeff,  // IN coeff of friction [-]
         const float  roll_speed )     // IN work roll surface spd. [linear_speed]
{
    MDSVERIFYNAME(status,"status")
    MDSVERIFYNAME(force,"force")
    MDSVERIFYNAME(slip,"slip")
    MDSVERIFYNAME(arcon,"arcon")
    MDSVERIFYNAME(torque,"torque")
    MDSVERIFYNAME(friction_pwr,"friction_pwr")
    MDSVERIFYNAME(deform_pwr,"deform_pwr")
    MDSVERIFYNAME(torque_pwr,"torque_pwr")
    MDSVERIFYNAME(tension_pwr,"tension_pwr")

    //-----------------------------------------
    // Initialize all OUT parameters 
    //-----------------------------------------
    *status         = bite.status = rb_invalid;
    *slip           = 1.0;
    *force          = 0.0;
    *torque         = 0.0;
    *arcon          = 0.0;
    *friction_pwr   = 0.0;
    *deform_pwr     = 0.0;
    *torque_pwr     = 0.0;
    *tension_pwr    = 0.0;

    //-------------------------------------------------------------------
    // Detect the condition of no reduction,  set output variables and
    // return immediately.  No force, torque or power.
    //-------------------------------------------------------------------
    if ( (exit_thk >= entry_thk) ||
         (exit_thk <= 0.0 )      ||
         (entry_thk <= 0.0 ) )
    {  
        if ( (exit_thk == entry_thk) && (exit_thk != 0.0) )
        {
            *status = bite.status = rb_valid;
        }

        return (bite.status == rb_valid);
 
    } // end if

    //-------------------------------------------------------------------
    // Detect the condition of tension greater than flow stress
    // and return immediately.  No force, torque or power.
    //-------------------------------------------------------------------
    if ( entry_tension >= flows ) 
    {  
        *status = bite.status = rb_invalid_entry_tension;
        return false;
    } // end if

    if ( exit_tension >= flows ) 
    {  
        *status = bite.status = rb_invalid_exit_tension;
        return false;
    } // end if

    Invalidate_All_Calcs();

    Set_Input_Data(
        wrdiam,             // IN undeformed work roll diam. [minor_length]
        hitchcock,          // IN Hitchcock's constant [pressure]
        entry_thk,          // IN entry thickness [minor_length]
        exit_thk,           // IN exit thickness [minor_length]
        flows,              // IN mean flow stress (const) [pressure]
        0.0,                // IN mean temperature (const) [C_F]
        entry_tension,      // IN entry tension [pressure]
        exit_tension,       // IN exit tension [pressure]
        strip_modulus,      // IN strip elastic mod. [pressure]
        strip_poisson,      // IN strip poisson ratio [-]
        friction_coeff,     // IN mean coeff of friction [-]
        roll_speed/Physcon.vel_time,// [m/sec_ft/sec] surface velocity of roll
        1);                 // material code

    if ( false == Calculate_All() )
    {
        *status = bite.status = rb_invalid;
        return false;
    }

    *force = Force();
    *slip = Slip();
    *arcon = Arcon();
    *torque = Torque();
    *friction_pwr = Friction_Pwr();
    *deform_pwr = Deform_Pwr();
    *torque_pwr = Torque_Pwr();
    *tension_pwr = Tension_Pwr();

    *status = bite.status = rb_valid;

    return true;

} // end Calc_Power()
                       

//----------------------------------------------------------------------------
// This function calculates the force, torque, slip, arc of contact and power.
//----------------------------------------------------------------------------
bool    cRollbite::Calc_Power (
          status_type *status,         // OUT status from calculations
         const float  wrdiam,          // IN undeformed work roll [minor_length]
         const float  entry_thk,       // IN entry thickness [minor_length]
         const float  exit_thk,        // IN exit thickness [minor_length]
         const float  flows,           // IN flow stress (const) [pressure]
         const float  hitchcock,       // IN Hitchcock's constant [pressure]
         const float  entry_tension,   // IN entry tension [pressure]
         const float  exit_tension,    // IN exit tension [pressure]
         const float  strip_modulus,   // IN strip elastic modulus [pressure]
         const float  strip_poisson,   // IN strip poisson ratio [-]
         const float  friction_coeff,  // IN coeff of friction [-]
         const float  roll_speed )     // IN work roll surface spd. [linear_speed]
{
    // Throw away calculated results, they are in object
    float   force, slip, arcon;
    float   torque, friction_pwr, deform_pwr, torque_pwr, tension_pwr;

    return Calc_Power(
          status,         // OUT status from calculations
          &force,          // OUT rolling force p.u. width
                                       //     [major_force]/[minor_length]
          &slip,           // OUT exit slip ratio
          &arcon,          // OUT length of arc of contact
                                       //     [minor_length]
          &torque,         // OUT torque p.u. width
                                       //     [torque]/[minor_length]
          &friction_pwr,   // OUT friction power
                                       //     [power_kw]/[volume_flow]
          &deform_pwr,     // OUT deformation power 
                                       //     [power_kw]/[volume_flow]
          &torque_pwr,     // OUT torque power
                                       //     [power_kw]/[minor_length]
          &tension_pwr,    // OUT tension power/width
                                       //     [power_kw]/[volume_flow]
          wrdiam,          // IN undeformed work roll [minor_length]
          entry_thk,       // IN entry thickness [minor_length]
          exit_thk,        // IN exit thickness [minor_length]
          flows,           // IN flow stress (const) [pressure]
          hitchcock,       // IN Hitchcock's constant [pressure]
          entry_tension,   // IN entry tension [pressure]
          exit_tension,    // IN exit tension [pressure]
          strip_modulus,   // IN strip elastic modulus [pressure]
          strip_poisson,   // IN strip poisson ratio [-]
          friction_coeff,  // IN coeff of friction [-]
          roll_speed );    // IN work roll surface spd. [linear_speed]
}

//--------------------------------------------------------------------------
// Exit_Thickness_Limit() ABSTRACT:
//
//     This formula is taken from Alexander's 1971 paper and represents the
//     minimum exit thickness that can be rolled for a given entry thickness,
//     coefficient of friction and undeformed roll radius.
//
//     The criterion is, for normal pressure s and tangential pressure tau:
//   
//     s*sin(phi) < tau*cos(phi)
//
//     Since tau = mu*s we get
//
//     tan(phi) < mu
//
//     Substituting for phi = phi_entry we get an exit thickness limit:
//
//     exith = entryh - 2*radius(1 - cos(atan(mu)))
//
//--------------------------------------------------------------------------
float cRollbite::Exit_Thickness_Limit(  // OUT min exit thickness [minor_length]
           const float radius,       // IN undeformed roll radius [minor_length]
           const float entryh,       // IN strip entry thickness [minor_length]
           const float frict_coeff)  // IN coefficient of friction
{
    return entryh -2.0 * radius * ( 1.0 - cos(atan(frict_coeff)));
}

//--------------------------------------------------------------------------
// Mu_Critical() ABSTRACT:
//
//     Returns the minimum reasonable value of coefficient of friction for
//     given entry thickness, exit thickness and deformed roll radius.  A
//     value of mu below this will result in DECREASING pressure in the
//     pressure distribution starting from the entry side of the roll bite.
//
//     Refer to rollbite design document for details on this calculation.
//
//                cos(phi)*sin(phi)
//     mu = ------------------------------
//                        entryh
//          cos^2(phi) + -------- * sec(phi)
//                       2*defrad
//
//     where phi is the entry angle
//
//--------------------------------------------------------------------------
float cRollbite::Mu_Critical(  // OUT critical value for coefficient of friction
           const float defrad,       // IN deformed roll radius [minor_length]
           const float entryh,       // IN strip entry thickness [minor_length]
           const float exith)        // IN strip exit thickness [minor_length]
{
    float   phi;
    float   honwr;
    float   mu_crit;
    float   cos_phi;
    
    phi   = Entry_Angle(2.0*defrad, exith, entryh);
    cos_phi = cos(phi);
    honwr = entryh/2.0*defrad;
    mu_crit = sin(phi)*cos_phi/(cos_phi*cos_phi + honwr/cos_phi);

    return mu_crit;
}

//------------------------------------------------------------------------------
// DEFORMED_RADIUS Abstract:
//     Function to estimate the apparent radius of the deformed work roll
//     surface in contact with the work piece.  Hitchcock's constant is a term
//     in Hitchcocks equation which incorporates the Young's modulus and
//     Poisson's ratio of the work rolls, evaluated externally.
//
//     Refer to rollbite design document for further details.
//
//                             force
//     defrad = radius*( 1 + --------- )                      
//                            c*draft
//
//     where c is Hitchcock's constant
//
//     c = pi*roll_modulus
//         ---------------
//         16*roll_poisson
//
//------------------------------------------------------------------------------
double cRollbite::Deformed_Radius(          // [mm_in_mm] deformed roll radius
                const double radius,        // [mm_in_mm] undeformed roll radius
                const double draft,         // [mm_in_mm] draft
                const double force,         // [kg/mm_lb/mm_n/mm] rolling force per
                                            //    unit piece width
                const double hitchcock      // [kg/mm2_psi_mpa] Hitchcock's constant
                                 )
{
    if ( draft > 0.0 )
        return ( radius * ( 1.0 + ( force / ( hitchcock * draft ) ) ) );

    return radius;

} // end Deformed_Radius

//------------------------------------------------------------------------------
// ARC_CONTACT ABSTRACT:
//     Function to estimate the length of the arc of contact between the work
//     piece and work roll.  Specifically, the calculate result is the
//     horizontal distance between the entry plane and the plane of minimum gap.
//     The exit plane is defined as the point of minimum gap between the
//     deformed roll surfaces, and any contact in the elastic zone at exit is
//     omitted.  The roll profile is assumed to be circular.
//------------------------------------------------------------------------------
double cRollbite::Arc_Contact(          // [mm_in_mm] arc of contact
            const double def_wr_rad,    // [mm_in_mm] deformed work roll radius
            const double draft          // [mm_in_mm] draft
                             )
{
    double entry_angl;                  // [deg_deg_deg] entry angle

    if ( def_wr_rad > 0.0 &&
         draft > 0.0 )
        //--------------------------------------------------------------------
        // Calculate the entry angle as a f(draft, deformed work roll radius).
        //--------------------------------------------------------------------
        entry_angl = acos( 1.0 - draft / (2.0*def_wr_rad) );
    else
        //-------------------------
        // Set entry angle to zero.
        //-------------------------
        entry_angl = 0.0;

    return entry_angl * def_wr_rad;
}

//-------------------------------------------------------------------
// Approximate_Slip
//
// Calculate a first approximation to slip.
//-------------------------------------------------------------------
float  cRollbite::Approximate_Slip(
                        float   wrdeform,       // IN deformed roll diameter
                        float   exit_thk,       // IN exit thickness
                        float   entry_thk,      // IN entry thickness
                        float   exit_tension,   // IN exit tension
                        float   entry_tension,  // IN entry tension
                        float   flows           // IN flow stress
                                    )           // OUT approximate slip factor
{
    double  phie;       // entry angle
    double  phin;       // neutral angle
    float   slip;       // approximate slip

    if ( exit_thk < 0.0 )
    {
        EMSG << "Approximate_Slip(): Exit thickness less than zero."
             << END_OF_MESSAGE;
        return 1.0;
    }

    if ( entry_thk <= exit_thk )
    {
        return 1.0;
    }

    phie = Entry_Angle(
                wrdeform,
                exit_thk,
                entry_thk);

    phin = Neutral_Sims(
                wrdeform,       // deformed roll diameter
                exit_thk,       // exit thickness
                entry_thk,      // entry thickness
                exit_tension,   // IN  exit tension
                entry_tension,  // IN  entry tension
                flows);         // IN  mean flow stress

    slip = Bite_Thickness(
                  phin,         // exit angle
                  wrdeform/2.0, // deformed roll radius
                  exit_thk      // exit thickness
                          ) / exit_thk;

    return slip;

}

//----------------------------------------------------------------------------
// STRAIN_RATE() ABSTRACT:
//     Strain_Rate() returns the mean strain rate in the roll bite for
//     the given geometry and speed.
//     The strain rate ( units 1/sec ) is given by
//    
//          strain_rate =  V * r / L * ((4-3r)/((2-r)**2))
//    
//          where V is the roll surface speed [minor_length] / [sec]
//                r is the pass reduction (per-unit draft) [-]
//                L is the arc of contact [minor_length]
//    
//     This formula is from Alexander and Ford.  It makes the significant
//     assumptions that  cos(angle)=1  and that the average horizontal
//     speed of the strip at the neutral point is equal to the
//     peripheral roll speed there.
//--------------------------------------------------------------------------
float cRollbite::Mean_Strain_Rate (  // OUT mean strain rate [1/sec]
           const float defrad,       // IN deformed roll radius [minor_length]
           const float entryh,       // IN strip entry thickness [minor_length]
           const float exith,        // IN strip exit thickness [minor_length]
           const float roll_speed )  // IN roll surface speed [linear_speed]
{
    float   arcon;
    float   pu_draft = (entryh - exith) / entryh;
    float   phi1 = acos(1.0F - (entryh - exith)/defrad);
    float   rollsp (roll_speed * Physcon.mmpm_inpft / Physcon.vel_time); 
                               // roll surface speed  [minor_length] / [sec]
    float   str;

    //------------------------------------------------------------------
    // calculate the horizontal length of the arc of contact 
    //------------------------------------------------------------------
    arcon = phi1 * defrad;

    //-----------------------------------------------------------------
    // calculate the strain rate using Alexander and Ford's formula.
    //-----------------------------------------------------------------
    if (arcon > 0.0) 
    {
        str = (rollsp * pu_draft * ((4.0 - (3.0 * pu_draft)) / 
               ((2.0 - pu_draft) * (2.0 - pu_draft))) / arcon);
    }
    else
    {
        //-----------------------------------------------------
        // else, there is no draft and arc of contact is 0.0
        //-----------------------------------------------------
        str = 0.0;
    }
    return str;
}


//---------------------------------------------------------------
// cRollbite::Hitch_Draft() ABSTRACT:
//
// Calculate draft term for Bland and Ford modified Hitchcock 
// equation. See Bland and Ford for details.
//---------------------------------------------------------------
float   cRollbite::Hitch_Draft(void)
{
    float   draft;
    float   draft2;
    float   draftt;
    float   sqrt_hdraft;
    float   hitch_draft;

    draft2  = bite.exit_thk * (1.0 - bite.strip_poisson * bite.strip_poisson) *
                  (bite.flows_mean - bite.exit_tension) / bite.strip_modulus;
    draftt  = bite.strip_poisson * (1.0 + bite.strip_poisson) *
                  (bite.exit_thk * bite.exit_tension - bite.entry_thk * bite.entry_tension) /
                  bite.strip_modulus;
    draft   = bite.entry_thk - bite.exit_thk;

    sqrt_hdraft = sqrt( draft + draft2 + draftt ) + sqrt( draft2 );
    hitch_draft = sqrt_hdraft * sqrt_hdraft;

    return hitch_draft;
}

//---------------------------------------------------------------
// cRollbite::Calculate_Elastic_Terms() ABSTRACT:			计算弹性变形相关参数的核心函数
//
// Calculate various terms required for elastic arc calculations.
// Must be called even if elastci contributions are not used.
//---------------------------------------------------------------
void    cRollbite::Calculate_Elastic_Terms(void)
{
    if ( bite.elast_calcs )
    {
        // Calculate the elastic effects
        if ( (bite.strip_modulus > 0.0) && (bite.strip_poisson > 0.0) )
        {
            Elastic_Thickness (
                &bite.entry_str,     // OUT entry thickness, stretched 
                                     //        [minor_length]
                &bite.exit_str,      // OUT exit thickness, stretched
                                     //        [minor_length]
                &bite.min_thk,       // OUT minimum gap thickness
                                     //        [minor_length]
                bite.entry_thk,      // IN entry thickness, no tension
                                     //        [minor_length]
                bite.exit_thk,       // IN exit thickness, no tension
                                     //        [minor_length]
                bite.entry_tension,  // IN external tension, entry [pressure]
                bite.exit_tension,   // IN external tension, exit [pressure]
                bite.strip_poisson,  // IN strip poisson ratio [-]
                bite.strip_modulus,  // IN elastic modulus of strip [pressure]
                bite.flows_mean );   // IN strip flow stress, exit [pressure]

            //---------------------------------------------------------
            // Calculated modified draft term for Hitchcock's equation.
            //---------------------------------------------------------
            bite.hitch_draft = this->Hitch_Draft();

        }
        else
        {
            bite.entry_str = bite.entry_thk;
            bite.exit_str  = bite.exit_thk;
            bite.min_thk   = bite.exit_thk;
            bite.hitch_draft = bite.entry_thk - bite.exit_thk;
        }
    }
    else
    {
        bite.entry_str = bite.entry_thk;
        bite.exit_str  = bite.exit_thk;
        bite.min_thk   = bite.exit_thk;
        bite.hitch_draft = bite.entry_thk - bite.exit_thk;
    }
}


//--------------------------------------------------------------------------
// Accessor functions							cRollbite类的访问器函数
//--------------------------------------------------------------------------

// Returns the object status
cRollbite::status_type  cRollbite::Status(void)
{
    return bite.status;
}
// Returns the precision of force calculation
float           cRollbite::Precision(void)
{
    return bite.precision;
}
// Returns the pressure valid flag
bool            cRollbite::Pressure_Valid(void)
{
    return bite.pressure_valid;
}
// Returns the calculated roll force
float           cRollbite::Force(void)
{
    return bite.force_valid?bite.force:0.0;
}
// Returns the number of force iterations used
float           cRollbite::Force_Iter(void)
{
    return bite.force_valid?bite.force_iter:0.0;
}
// Returns the roll force validity flag
bool            cRollbite::Force_Valid(void)
{
    return bite.force_valid;
}
// Returns the calculated slip
float           cRollbite::Slip(void)
{
    return bite.slip_valid?bite.slip:0.0;
}
// Returns the calculated draft compensation
float           cRollbite::Draft_Comp(void)
{
    return bite.slip_valid?(bite.exit_thk * bite.slip / bite.entry_thk):0.0;
}
// Returns the calculated arc of contact length
float           cRollbite::Arcon(void)
{
    return bite.arcon_valid?bite.arcon:0.0;
}
// Returns the minimum exit thickness that can be rolled
float           cRollbite::ExThick_Limit(void)
{
    return bite.exthklim_valid?bite.exthk_lim:0.0;
}
// Returns the number of slices used
float           cRollbite::Slices(void)
{
    return bite.pressure_valid?bite.final.num_slices:0.0;
}
// Returns the calculated neutral point angle
float           cRollbite::Neutral_Angle(void)
{
    return bite.pressure_valid?bite.phi_neut:0.0;
}
// Returns the pressure at the neutral point angle
float           cRollbite::Neutral_Pressure(void)
{
    return bite.pressure_valid?bite.final.norm_pressure[bite.idx_neut]:0.0;
}
// Returns the thickness at the neutral point angle
float           cRollbite::Neutral_Thick(void)
{
    return bite.pressure_valid?bite.final.bite_thick[bite.idx_neut]:0.0;
}
// Returns the calculated entry angle
float           cRollbite::Entry_Angle(void)
{
    return bite.pressure_valid?bite.phi_entry:0.0;
}
// Returns the calculated deformed roll diameter
float           cRollbite::Deform_Diameter(void)
{
    return bite.force_valid?bite.wrdeform:0.0;
}
// Returns the torque per unit width
float           cRollbite::Torque(void)
{
    return bite.torque_valid?bite.torque:0.0;
}
// Returns the torque validity flag
bool            cRollbite::Torque_Valid(void)
{
    return bite.torque_valid;
}
// Returns the deformation power
float           cRollbite::Deform_Pwr(void)
{
    return bite.deform_pwr_valid?bite.deform_pwr:0.0;
}
// Returns the friction power
float           cRollbite::Friction_Pwr(void)
{
    return bite.frict_pwr_valid?bite.frict_pwr:0.0;
}
// Returns the tension power
float           cRollbite::Tension_Pwr(void)
{
    return bite.tension_pwr_valid?bite.tension_pwr:0.0;
}
// Returns the torque power
float           cRollbite::Torque_Pwr(void)
{
    return bite.torque_pwr_valid?bite.torque_pwr:0.0;
}
// Returns the power validity flag
bool            cRollbite::Pwr_Valid(void)
{
    return bite.deform_pwr_valid  &&
           bite.frict_pwr_valid   &&
           bite.tension_pwr_valid &&
           bite.torque_pwr_valid;
}
// Returns the ratio of torque power to shaft power
float           cRollbite::Pwr_Ratio(void)
{
    return (bite.deform_pwr_valid  &&
           bite.frict_pwr_valid   &&
           bite.tension_pwr_valid &&
           bite.torque_pwr_valid)?bite.pwr_ratio:0.0;
}
// Returns the sensitivity of force to entry thickness
float           cRollbite::DForce_DEnthick(void)
{
    return bite.dforce_dengag_valid?bite.dforce_dengag:0.0;
}
// Returns the sensitivity of force to exit thickness
float           cRollbite::DForce_DExthick(void)
{
    return bite.dforce_dexgag_valid?bite.dforce_dexgag:0.0;
}
// Returns the sensitivity of force to draft
float           cRollbite::DForce_DDraft(void)
{
    return bite.dforce_ddraft_valid?bite.dforce_ddraft:0.0;
}
// Returns the sensitivity of force to entry tension
float           cRollbite::DForce_DEntens(void)
{
    return bite.dforce_dentens_valid?bite.dforce_dentens:0.0;
}
// Returns the sensitivity of force to exit tension
float           cRollbite::DForce_DExtens(void)
{
    return bite.dforce_dextens_valid?bite.dforce_dextens:0.0;
}
// Returns the sensitivity of torque power to exit thickness
float           cRollbite::DTorquepwr_DExthick(void)
{
    return bite.dtorquepwr_dexgag_valid?bite.dtorquepwr_dexgag:0.0;
}
// Returns the sensitivity of torque power to draft
float           cRollbite::DTorquepwr_DDraft(void)
{
    return bite.dtorquepwr_ddraft_valid?bite.dtorquepwr_ddraft:0.0;
}
// Returns the sensitivity of slip to entry thickness
float           cRollbite::DSlip_DEnthick(void)
{
    return bite.dslip_dengag_valid?bite.dslip_dengag:0.0;
}
// Returns the sensitivity of slip to exit thickness
float           cRollbite::DSlip_DExthick(void)
{
    return bite.dslip_dexgag_valid?bite.dslip_dexgag:0.0;
}
// Returns the sensitivity of slip to entry tension
float           cRollbite::DSlip_DEntens(void)
{
    return bite.dslip_dentens_valid?bite.dslip_dentens:0.0;
}
// Returns the sensitivity of slip to exit tension
float           cRollbite::DSlip_DExtens(void)
{
    return bite.dslip_dextens_valid?bite.dslip_dextens:0.0;
}



// Returns the sensitivity of force to entry thickness validity
bool            cRollbite::DForce_DEnthick_Valid(void)
{
    return bite.dforce_dengag_valid;
}
// Returns the sensitivity of force to exit thickness validity
bool            cRollbite::DForce_DExthick_Valid(void)
{
    return bite.dforce_dexgag_valid;
}
// Returns the sensitivity of force to draft validity
bool            cRollbite::DForce_DDraft_Valid(void)
{
    return bite.dforce_ddraft_valid;
}
// Returns the sensitivity of force to entry tension validity
bool            cRollbite::DForce_DEntens_Valid(void)
{
    return bite.dforce_dentens_valid;
}
// Returns the sensitivity of force to exit tension validity
bool            cRollbite::DForce_DExtens_Valid(void)
{
    return bite.dforce_dextens_valid;
}
// Returns the sensitivity of torque power to exit thickness validity
bool            cRollbite::DTorquepwr_DExthick_Valid(void)
{
    return bite.dtorquepwr_dexgag_valid;
}
// Returns the sensitivity of torque power to draft validity
bool            cRollbite::DTorquepwr_DDraft_Valid(void)
{
    return bite.dtorquepwr_ddraft_valid;
}
// Returns the sensitivity of slip to entry thickness validity
bool            cRollbite::DSlip_DEnthick_Valid(void)
{
    return bite.dslip_dengag_valid;
}
// Returns the sensitivity of slip to exit thickness validity
bool            cRollbite::DSlip_DExthick_Valid(void)
{
    return bite.dslip_dexgag_valid;
}
// Returns the sensitivity of slip to entry tension validity
bool            cRollbite::DSlip_DEntens_Valid(void)
{
    return bite.dslip_dentens_valid;
}
// Returns the sensitivity of slip to exit tension validity
bool            cRollbite::DSlip_DExtens_Valid(void)
{
    return bite.dslip_dextens_valid;
}


//------------------------------------------------------------------------
// Zero out calculated bite data.
//------------------------------------------------------------------------
void    cRollbite::Zero_Bite_Data(void)
{
    bite.precision          = 0.0;
    bite.init_slices        = 0.0;
    bite.exit_side          = 0.0;
    bite.inputs_valid       = false;
    bite.wrdiam             = 0.0;
    bite.hitchcock          = 0.0;
    bite.entry_thk          = 0.0;
    bite.exit_thk           = 0.0;
    bite.pu_draft           = 0.0;
    bite.flows_mean         = 0.0;
    bite.temp_mean          = 0.0;
    bite.entry_tension      = 0.0;
    bite.exit_tension       = 0.0;
    bite.strip_modulus      = 0.0;
    bite.strip_poisson      = 0.0;
    bite.friction_coeff     = 0.0;
    bite.vroll              = 0.0;
    bite.matl_code          = 0.0;
    bite.entry_str          = 0.0;
    bite.exit_str           = 0.0;
    bite.min_thk            = 0.0;
    bite.xtemp_valid        = false;
    bite.phin_guess         = 0.0;
    bite.phie_guess         = 0.0;
    bite.slip_guess         = 0.0;
    bite.pressure_valid     = false;
    bite.phi_entry          = 0.0;
    bite.phi_neut           = 0.0;
    bite.phi_neut_sims      = 0.0;
    bite.idx_neut           = 0.0;
    bite.nslice_entry       = 0.0;
    bite.nslice_exit        = 0.0;
    bite.wrdeform           = 0.0;
    bite.force_guess        = 0.0;
    bite.force_iter         = 0.0;
    bite.vstrip_entry       = 0.0;
    bite.vstrip_exit        = 0.0;
    bite.mean_strain_guess  = 0.0;
    bite.mean_strain        = 0.0;

    Delete_Distribution(&bite.entry);
    Delete_Distribution(&bite.exit);
    Delete_Distribution(&bite.final);

    bite.slip_valid         =  false;
    bite.slip               = 0.0;
    bite.arcon_valid        =  false;
    bite.arcon              = 0.0;
    bite.exthklim_valid     =  false;
    bite.exthk_lim          = 0.0;
    bite.force_valid        =  false;
    bite.force              = 0.0;
    bite.force_minor        = 0.0;
    bite.torque_valid       =  false;
    bite.torque             = 0.0;
    bite.frict_pwr_valid    =  false;
    bite.frict_pwr          = 0.0;
    bite.deform_pwr_valid   =  false;
    bite.deform_pwr         = 0.0;
    bite.tension_pwr_valid  =  false;
    bite.tension_pwr        = 0.0;
    bite.torque_pwr_valid   =  false;
    bite.torque_pwr         = 0.0;
    bite.shaft_pwr_valid    =  false;
    bite.shaft_pwr          = 0.0;
    bite.pwr_ratio          = 0.0;
    bite.dforce_dengag_valid        =  false;
    bite.dforce_dengag              = 0.0;
    bite.dforce_dexgag_valid        =  false;
    bite.dforce_dexgag              = 0.0;
    bite.dforce_dentens_valid       =  false;
    bite.dforce_dentens             = 0.0;
    bite.dforce_dextens_valid       =  false;
    bite.dforce_dextens             = 0.0;
    bite.dforce_ddraft_valid        =  false;
    bite.dforce_ddraft              = 0.0;
    bite.dtorquepwr_dexgag_valid    =  false;
    bite.dtorquepwr_dexgag          = 0.0;
    bite.dtorquepwr_ddraft_valid    =  false;
    bite.dtorquepwr_ddraft          = 0.0;
    bite.dslip_dengag_valid         =  false;
    bite.dslip_dengag               = 0.0;
    bite.dslip_dexgag_valid         =  false;
    bite.dslip_dexgag               = 0.0;
    bite.dslip_dentens_valid        =  false;
    bite.dslip_dentens              = 0.0;
    bite.dslip_dextens_valid        =  false;
    bite.dslip_dextens              = 0.0;
    bite.status                     = rb_invalid;

}


static cSchema::schema_type cRollbiteCfg_schema[]=
{
    //Next  Enum  Schema details                            Fmt  Units        Comment
    //====  ====  ========================================  ==== ===========  ==================================================
    { NULL, NULL, SCHEMA_T(cRollbiteCfg,int,min_slices),    "",  "",          "Minimum number of slices to use" },
    { NULL, NULL, SCHEMA_T(cRollbiteCfg,float,pr_precsn),   "",  "",          "Precision of the pressure calculations" },
    { NULL, NULL, SCHEMA_T(cRollbiteCfg,float,frc_precsn),  "",  "",          "Precision of the force calculations" },
    { NULL, NULL, SCHEMA_T(cRollbiteCfg,bool,precise_frc),  "",  "",          "Consider tangential component acting through deformed radius" },
    { NULL, NULL, SCHEMA_T(cRollbiteCfg,bool,precise_trq),  "",  "",          "Consider normal component acting through deformed radius" },
    { NULL, NULL, SCHEMA_T(cRollbiteCfg,bool,dbg_strain),   "",  "",          "Do debugging instantaneous strain rate calcs" },
    { NULL, NULL, SCHEMA_T(cRollbiteCfg,bool,neut_sims),    "",  "",          "Include approximate neutral point calc by Sim's formula" },
    { NULL, NULL, SCHEMA_T(cRollbiteCfg,bool,elast_calcs),  "",  "",          "Include elastic calcs for debugging" },
    { NULL, NULL, SCHEMA_T(cRollbiteCfg,bool,err_bounds),   "",  "",          "Include error bounds by C-K RK5 algorithm" },
    { NULL, NULL, SCHEMA_T(cRollbiteCfg,bool,precise_derivs),"", "",          "Include precise cos/sin in derivative calcs" },

    { NULL, NULL, SCHEMA_T(cRollbiteCfg,float,ftol),        "",  "",          "Fractional convergence tolerance (default = 0.0005)" },
    { NULL, NULL, SCHEMA_T(cRollbiteCfg,int,iiter),         "",  "",          "Number of moves within any call to amebsa (default = 5)" },
    { NULL, NULL, SCHEMA_T(cRollbiteCfg,int,riter),         "",  "",          "Max simplex moves before replacing vertex with 'best' vertex (default = 20)" },
    { NULL, NULL, SCHEMA_T(cRollbiteCfg,int,max_moves),     "",  "",          "Max simplex moves over which temperature is reduced (default = 40)" },
    { NULL, NULL, SCHEMA_T(cRollbiteCfg,int,max_iter),      "",  "",          "Max simplex moves allowed (default = 200)" },
    { NULL, NULL, SCHEMA_T(cRollbiteCfg,float,tfac),        "",  "",          "Pow factor to control how fast temperature is reduced (default = 4.0)" },
    { NULL, NULL, SCHEMA_T(cRollbiteCfg,float,tdiv),        "",  "",          "Divide objective fn value by this to get initial temperature (default = 5.0)" },
    { NULL, NULL, SCHEMA_T(cRollbiteCfg,bool,replace),      "",  "",          "If true, use best ever point to replace a vertex every riter tries (default = false)" },
    { NULL, NULL, SCHEMA_T(cRollbiteCfg,bool,debug),        "",  "",          "If true, print some debug info (default = false)" },
    { NULL, NULL, SCHEMA_T(cRollbiteCfg,bool,alternate_power),        "",  "",          "Use alternate power calculation" },
    
    { 0 }   // terminate list

};

// Link all the schema's together
cSchema::schema_name_type cRollbiteCfg::sSchema[]=
{
    // Name            Size                Schema             Packed
    { 
        "cRollbiteCfg",                     //name
        sizeof(cRollbiteCfg),               // size
        cRollbiteCfg_schema,                // schema
        false,                              // packed
        false,                              // allow ptr
        false,                              // Read only
        "Static rollbite configuration",    // comment
        0                                   // offset to config data
    },
    { 0 } // terminate list
};

//-----------------------------------------------------------------------------
// CRollbiteCfg ABSTRACT:
//   constructor
//-----------------------------------------------------------------------------
cRollbiteCfg::cRollbiteCfg()
{
    Set_Class_Name("cRollbiteCfg");
    Set_Schema("cRollbiteCfg",sSchema);

    // Zero out member data
    Zero_Data(this, sizeof(cRollbiteCfg), Get_Schema("cRollbiteCfg"));
}

cRollbiteCfg::cRollbiteCfg( const MString     &objName, 
            const objTypEnum RollbiteCfg, 
            const objPosEnum position, 
            void       *pHash ) 
      : cBase( objName, RollbiteCfg, position, pHash, 0, 0 )
{
    Set_Class_Name("cRollbiteCfg");
    Set_Schema("cRollbiteCfg",sSchema);

    // Zero out member data
    Zero_Data(this, sizeof(cRollbiteCfg), Get_Schema("cRollbiteCfg"));
}

//-------------------------------------------------------------------------
// CRollbiteCfg ABSTRACT:
//   copy constructor
//-------------------------------------------------------------------------
cRollbiteCfg::cRollbiteCfg (const cRollbiteCfg& source)
      : cBase( source )
{
}

//-------------------------------------------------------------------------
// OPERATOR = ABSTRACT:
//   assignment operator
//-------------------------------------------------------------------------
cRollbiteCfg& cRollbiteCfg::operator= (const cRollbiteCfg& source)
{
    if (this != &source)
    {
//      cBase::operator=(source);
      Copy_Data(this,(void *)&source,sizeof(cRollbiteCfg),cRollbiteCfg_schema);
    }

    return (*this);
}

//-------------------------------------------------------------------------
// DUMP ABSTRACT:
//   RollbiteCfg dump contents of the struct.
// The boolean composed if true indicates that the
// object should call the dump function for the 
// objects that it contains.
//-------------------------------------------------------------------------
void cRollbiteCfg::dump(const bool composed)
{
    Dump_Data(stdout, "cRollbiteCfg", this, 0, (const char *)objName());

    if (composed)
    {
        ;
    }
} // end dump

//---------------------------------------------------------------------
// Virtual function to allow the user to carry out post processing.
//---------------------------------------------------------------------
bool cRollbiteCfg::Post_Config(
            char *name,         // unused
            void *psStruct)     // unused
{

    if ( min_slices == 0 )
    {
        min_slices = MINIMUM_SLICES;
    }
    if ( min_slices < MINIMUM_SLICES )
    {
        EMSG << "Warning, must have min_slices at least " << (int)MINIMUM_SLICES
             << END_OF_MESSAGE;
        min_slices = MINIMUM_SLICES;
    }
    if ( min_slices > MAX_GAP_SLICES-2 )
    {
        EMSG << "Warning, can't have more than " << (int)(MAX_GAP_SLICES-2)
             << END_OF_MESSAGE;
        min_slices = MAX_GAP_SLICES-2;
    }
    if ( pr_precsn == 0.0 )
    {
        pr_precsn = DEFAULT_PRECISION;
    }
    if ( frc_precsn == 0.0 )
    {
        frc_precsn = pr_precsn/2.0;
    }

    //-----------------------------------------------------------------
    // setup reasonable values for 2D minimization fn.
    //-----------------------------------------------------------------
    if ( ftol == 0.0 )
    {
        ftol = 0.0005;      // fractional convergence tolerance ( 0.05% )
    }
    if ( iiter == 0 )
    {
        iiter = 5;          // number of moves within any call to amebsa
    }
    if ( riter == 0 )
    {
        riter = 20;         // number of moves before a restart
    }
    if ( max_moves == 0 )
    {
        max_moves = 40;     // max number of moves of the simplex for which we reduce temp
    }
    if ( max_iter == 0 )
    {
        max_iter  = 200;    // maximum number of iterations allowed
    }
    if ( tfac == 0.0 )
    {
        tfac = 4.0;         // pow factor to control how fast temperature is reduced
    }
    if ( tdiv == 0.0 )
    {
        tdiv = 5.0;         // divide initial objective fn by this to get start temperature
    }

    return true;

}
