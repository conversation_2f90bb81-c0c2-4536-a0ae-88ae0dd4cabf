       //---------------------------------------------------------------
       // Copyright (c) 2006 by
       // Toshiba Mitsubishi-Electric Industrial Systems Corp. 
       // TMGE Automation Systems LLC, U.S.A.
       // Published in only a limited, copyright sense, and all
       // rights, including trade secret rights are reserved.
       //---------------------------------------------------------------
//-----------------------------------------------------------------------------
//
//     TITLE:         New Roll Bite Model Class Implementation - Calculations
//
//     FILE NAME:     MDS\SOURCE\ROLLBITE_CALCS.CXX
//
//     PREPARED BY:   TMGE Automation Systems LLC, U.S.A.
//                      1501 Roanoke Blvd., Salem, Virginia, USA
//                    Toshiba Mitsubishi-Electric Industrial Systems Corp.
//                      Mita 43 MT Bldg. Mita 3-13-16, Minato-ku Tokyo, Japan
//
//     CUSTOMER:      STANDARD
//
//     SYSTEM:        Mill Automation System
//
//--------------------------------------------------------------------------------------
//
//     REVISIONS:
//     level  review date  author        change content
//     -----  -----------  ------------  -----------------------------------------------
//     1.0-0  20-Sep-2006  FM Williams   Original
//--------------------------------------------------------------------------------------

//-----------------------------------------------------------------------------
//
//
// ABSTRACT:  This module implements most of the rollbite class public
//            calculation methods.
//
//
//     PUBLIC FUNCTION          DESCRIPTION
//     -----------------------  -----------------------------------------------
//
//     Methods providing a complete interface to the Rollbite Class.  These
//     methods are designed for use where efficiency is important and they
//     cache intermediate and final results as needed.
//
//     Methods to set input parameters and control cached results.
//     -----------------------------------------------------------
//
//     Invalidate_All_Calcs()   This method invalidates all cached calculation
//                              results.  Input data is preserved.
//
//     Invalidate_Torque_Power() This method invalidates torque and power
//                              calculations, whilst preserving roll pressure
//                              and force calcs.
//
//     Set_Input_Data()         This method sets required piece input data for
//                              the roll pressure distribution calculations.
//
//     Set_Precision()          This method sets the precision for roll normal
//                              pressure calculation.  If not set, the precision
//                              defaults to 1%.  Making the precision higher
//                              increases the compuation time required to solve
//                              von Karman's differential equation.
//
//     Hooks to External Classes
//     -------------------------
//     These next two methods provide the means to establish a hook to
//     external classes for the calculation of temperature and flow stress within
//     the roll bite.
//
//     Assign_Rbheat()          This method assigns the specified cRbheat Class
//                              to the entry side of the roll bite and creates
//                              an exit temperature instance to hold the exit
//                              temperature distribution.  If the entry temperature
//                              distribution is not set, the average temperature
//                              will be used in roll bite calculations.
//
//     Assign_Flowstress()      This method assigns the specified cFlowstress
//                              Class for use by the roll bite calculations.
//                              To provide maximum flexibility, calculation of
//                              Flowstress is implemented separately from the
//                              roll bite calculations.  If the cFlowstress class
//                              is not assigned, the mean flow stress adjusted
//                              for strain rate shall be used.
//
//     Calculation Methods.
//     --------------------
//     These methods perform the various calculations required.  Calculations
//     if possible, are cached, so that if the calculation method is called
//     again, and the required results are available, and the input data has
//     not changed, they will be returned, without recalculation.
//
//     Calculate_Pressure()     Calculate the roll pressure distribution.
//
//     Calculate_Force()        Calculate the roll separating force.
//
//     Calculate_Torque()       Calculate the roll torque.
//
//     Calculate_Power()        Calculates the deformation, friction, tension
//                              and torque powers.
//
//     Calculate_All()          Calculates all roll bite quantities.
//
//
//
//     Debugging information
//     ---------------------
//
//     Dump()                   Dump the context of the Rollbite object, optionally
//                              including the roll bite slice information.
//
//
//-----------------------------------------------------------------------------

#define __ROLLBITE_MAINLINE__

//------------------------------
// C++ standard library includes
//------------------------------
#include <math.h>

#include "alarm.hxx"
#include "mathuty.hxx"
#include "physcon.hxx"
#include "rbheat.hxx"
#include "flowstress.hxx"
#include "rollbite.hxx"

#ifdef WIN32
	#ifdef _DEBUG
	#define new DEBUG_NEW
	#endif
#endif

#ifdef WIN32
    #pragma warning(disable: 4244) // double to float conversion (NT thinks constants are doubles)
    #pragma warning(disable: 4305) // truncation from 'const double' to 'const float'
    #pragma warning(disable: 4505) // disable unreferenced inline function warning
#endif



//-----------------------------------------------------------------------------
// Static functions
//-----------------------------------------------------------------------------

//---------------------------------------------------------------
// Solve y = mx + b for m and b
//---------------------------------------------------------------
static  void    linear_mxb(double x1, double y1, double x2, double y2, double& m, double& b)
{
    m = (y2 - y1)/(x2 - x1);
    b = y1 - m * x1;
}

//--------------------------------------------------------------------------
// This method invalidates all calculations, whilst preserving input data
//--------------------------------------------------------------------------
bool    cRollbite::Invalidate_All_Calcs (void)
{
    bite.pressure_valid = false;
    bite.slip_valid = false;
    bite.arcon_valid = false;
    if ( true == bite.force_valid )
    {
        bite.force_guess = bite.force_minor;
    }
    else
    {
        bite.force = bite.force_minor = 0.0;
    }
    bite.force_valid = false;
    bite.dforce_dengag_valid = false;
    bite.dforce_dexgag_valid = false;
    bite.dforce_dentens_valid = false;
    bite.dforce_dextens_valid = false;
    bite.dslip_dentens_valid = false;
    bite.dslip_dextens_valid = false;
    bite.dslip_dengag_valid = false;
    bite.dslip_dexgag_valid = false;
    bite.dforce_ddraft_valid = false;
    return Invalidate_Torque_Power();
}

//--------------------------------------------------------------------------
// This method invalidates torque and power calculations, whilst
// preserving force and roll pressure calcs.
//--------------------------------------------------------------------------
bool    cRollbite::Invalidate_Torque_Power (void)
{
    bite.torque_valid = false;
    bite.frict_pwr_valid = false;
    bite.deform_pwr_valid = false;
    bite.torque_pwr_valid = false;
    bite.tension_pwr_valid = false;
    bite.shaft_pwr_valid = false;
    bite.dtorquepwr_dexgag_valid = false;
    bite.dtorquepwr_ddraft_valid = false;
    return true;
}

//--------------------------------------------------------------------------
// This method sets required piece input data for the roll pressure
// distribution calculations.
//--------------------------------------------------------------------------
bool    cRollbite::Set_Input_Data (
    double   wrdiam,             // IN undeformed work roll diam.
                                //      [minor_length]
    double   hitchcock,          // IN Hitchcock's constant [pressure]
    double   entry_thk,          // IN entry thickness [minor_length]
    double   exit_thk,           // IN exit thickness [minor_length]
    double   flows_mean,         // IN mean flow stress (const) [pressure]
    double   temp_mean,          // IN mean temperature (const) [C_F]
    double   entry_tension,      // IN entry tension [pressure]
    double   exit_tension,       // IN exit tension [pressure]
    double   strip_modulus,      // IN strip elastic mod. [pressure]
    double   strip_poisson,      // IN strip poisson ratio [-]
    double   friction_coeff,     // IN mean coeff of friction [-]
    double   vroll,              // [m/sec_ft/sec] roll surface velocity
    int     matl_code)          // material code
{
    bite.exthklim_valid = false;

    // Assign the input parameters, invalidating calculations if any parameter
    // has changed
    if ( bite.wrdiam != wrdiam )
    {
        bite.wrdiam = wrdiam;
        bite.wrdeform = bite.wrdiam;      // initial first guess
        Invalidate_All_Calcs();
    }
    if ( bite.hitchcock != hitchcock )
    {
        bite.hitchcock = hitchcock;
        Invalidate_All_Calcs();
    }
    if ( entry_thk < exit_thk )
    {
        EMSG << (const char*)objName()
             << ": Entry thickness "
             << entry_thk
             << " cannot be less than exit_thk "
             << exit_thk
             << " thickness"
             << END_OF_MESSAGE;
        return false;
    }
    if ( bite.entry_thk != entry_thk )
    {
        bite.entry_thk = entry_thk;
        Invalidate_All_Calcs();
    }
    if ( bite.exit_thk != exit_thk )
    {
        bite.exit_thk = exit_thk;
        Invalidate_All_Calcs();
    }
    if ( flows_mean <= 0.0 )
    {
        EMSG << (const char*)objName()
             << ": Flowstress "
             << flows_mean
             << " too small"
             << END_OF_MESSAGE;
        return false;
    }
    if ( bite.flows_mean != flows_mean )
    {
        bite.flows_mean = flows_mean;
        Invalidate_All_Calcs();
    }
    if ( bite.temp_mean != temp_mean )
    {
        bite.temp_mean = temp_mean;
        Invalidate_All_Calcs();
    }
    if ( bite.entry_tension != entry_tension )
    {
        bite.entry_tension = entry_tension;
        Invalidate_All_Calcs();
    }
    if ( bite.exit_tension != exit_tension )
    {
        bite.exit_tension = exit_tension;
        Invalidate_All_Calcs();
    }
    if ( bite.strip_modulus != strip_modulus )
    {
        bite.strip_modulus = strip_modulus;
        Invalidate_All_Calcs();
    }
    if ( bite.strip_poisson != strip_poisson )
    {
        bite.strip_poisson = strip_poisson;
        Invalidate_All_Calcs();
    }
    if ( friction_coeff <= 0.0 )
    {
        EMSG << (const char*)objName()
             << ": Friction coefficient "
             << friction_coeff
             << " too small"
             << END_OF_MESSAGE;
        return false;
    }
    if ( bite.friction_coeff != friction_coeff )
    {
        bite.friction_coeff = friction_coeff;
        Invalidate_All_Calcs();
    }
    if ( vroll <= 0.0 )
    {
        EMSG << (const char*)objName()
             << ": Roll speed "
             << vroll
             << " too small"
             << END_OF_MESSAGE;
        return false;
    }
    if ( bite.vroll != vroll )
    {
        bite.vroll = vroll;
        Invalidate_All_Calcs();
    }
    if ( bite.matl_code != matl_code )
    {
        bite.matl_code = matl_code;
        Invalidate_All_Calcs();
    }

    //-----------------------------------------------------------------
    // Calculate terms related to elastic arc contributions
    //-----------------------------------------------------------------
    this->Calculate_Elastic_Terms();

    if ( bite.entry_thk > 0.0 )
    {
        bite.pu_draft = (bite.entry_thk - bite.exit_thk)/bite.entry_thk;
    }
    else
    {
        bite.pu_draft = 0.0;
    }

    if ( bite.neut_sims )
    {
        //-------------------------------------------------------------------
        // ??????KGM
        // Following code only here for debugging and comparison purposes
        // May be removed when testing is complete
        //-------------------------------------------------------------------

        //-------------------------------------------------------------------
        // Rough guess at angles of entry and neutral point calculated for
        // comparison purposes.  Calculate the Neutral angle by Sims formula,
        // use undeformed radius and assume no tension component
        //-------------------------------------------------------------------
        bite.phie_guess = Entry_Angle(
                                bite.wrdiam,        // IN  use undeformed roll diameter
                                bite.min_thk,       // IN  exit thickness
                                bite.entry_thk);    // IN  entry thickness

        bite.phin_guess = Neutral_Sims(
                                bite.wrdiam,        // IN  use undeformed roll diameter
                                bite.min_thk,        // IN  exit thickness
                                bite.entry_thk);    // IN  entry thickness

        //-------------------------------------------------------------------
        // Rough guess at slip calculated for comparison purposes.
        //-------------------------------------------------------------------
        bite.slip_guess = Approximate_Slip(
                                bite.wrdiam,        // IN  use undeformed roll diameter
                                bite.min_thk,       // IN  exit thickness
                                bite.entry_thk);    // IN  entry thickness

        //-------------------------------------------------------------------
        // ??????KGM
        // End of debugging code
        //-------------------------------------------------------------------
    }

    bite.mean_strain_guess = Mean_Strain_Rate(
                                bite.wrdiam / 2.0,
                                bite.entry_thk,
                                bite.min_thk,
                                bite.vroll * Physcon.vel_time);

    // determine minimum exit thickness that can be rolled for a given
    // entry thickness and coefficient of friction
    bite.exthk_lim = Exit_Thickness_Limit(
                               bite.wrdiam / 2.0,
                               bite.entry_thk,
                               bite.friction_coeff);
    bite.exthklim_valid = true;

    bite.inputs_valid = true;

    return true;

} // cRollbite::Set_Input_Data 


//--------------------------------------------------------------------------
// This method sets the precision for roll normal pressure calculation.
// If not set, the precision defaults to the configured value.
// Making the precision higher increases the computation time required to
// solve von Karman's differential equation.
// Setting precision will invalidate any existing results.
//--------------------------------------------------------------------------
bool    cRollbite::Set_Precision (double precision)
{
    if ( (precision > 0.0) && (bite.precision != precision) )
    {
        bite.precision = precision;
        Invalidate_All_Calcs();
    }

    return true;

} // cRollbite::Set_Precision

//--------------------------------------------------------------------------
// This method sets the minimum number of slices that may be used in the
// rollbite pressure calculations.
//--------------------------------------------------------------------------
bool    cRollbite::Set_Min_Slices (int min_slices)
{
    if ( (min_slices > 0) && (bite.min_slices != min_slices) )
    {
        bite.min_slices = bite.init_slices = min_slices-2;
        Invalidate_All_Calcs();
    }

    return true;

} // cRollbite::Set_Min_Slices


//--------------------------------------------------------------------------
// This method assigns the specified cRbheat Class
// to the entry side of the roll bite and creates
// an exit temperature instance to hold the exit
// temperature distribution.  If the entry temperature
// distribution is not set, the average temperature
// will be used in roll bite calculations.
//
//  If the calculation is OK, true is returned, else false.
//--------------------------------------------------------------------------
bool    cRollbite::Assign_Rbheat (
    cRbheat* pcTemp   // IN  The class to assign
    ) // [bool] status of assignment
{
    if ( pcTemp != NULL )
    {
        bite.pcEtemp = pcTemp;
        // Make an instance for exit temperature calculations
        // Preserve the geometry from the entry side.
        bite.pcXtemp = new cRbheat(
                                bite.pcEtemp->geometry.num_nodes,
                                bite.pcEtemp->geometry.fac_a,
                                bite.pcEtemp->geometry.fac_b);
        // Assign the geometry
        bite.pcXtemp->Assign_Geometry(&bite.pcEtemp->geometry);

    }

    return true;

} // cRollbite::Assign_Rbheat

//--------------------------------------------------------------------------
// This method assigns the specified cFlowstress
// Class for use by the roll bite calculations.
// To provide maximum flexibility, calculation of
// Flowstress is implemented separately from the
// roll bite calculations.  If the cFlowstress class
// is not assigned, the mean flow stress adjusted
// for strain rate shall be used.
//
//  If the calculation is OK, true is returned, else false.
//--------------------------------------------------------------------------
bool    cRollbite::Assign_Flowstress (
    cFlowstress* pcFlow   // IN  The class to assign
    ) // [bool] status of assignment
{

    return false;

} // cRollbite::Assign_Flowstress



//--------------------------------------------------------------------------
//
// CALCULATION METHODS
//
// These methods perform the various calculations required.  Calculations,
// once performed, are cached.  That is, if the method is called again, if
// the required results are available, they are not recalculated.  Certain
// methods will cause the calculations to be invalidated, eg changing the
// precision of the roll pressure distribution or any piece specific
// properties.
//--------------------------------------------------------------------------


//--------------------------------------------------------------------------
// This method calculates the roll pressure distribution.
//--------------------------------------------------------------------------
bool    cRollbite::Calculate_Pressure (void)
{
    int     i, j;
    double   phi_e;
    double   phi_x;
    double   phi_ehtry;
    double   phi_ehdone;
    double   phi_ehnext;
    double   phi_xhtry;
    double   phi_xhdone;
    double   phi_xhnext;
    int     ie;
    int     ix;

    // Check if pressure distribution is already valid.  If so
    // just return, rather than recalculating.
    if ( bite.pressure_valid )
    {
        return true;
    }

    // Allocate entry side and exit side distribution arrays.			分配入口端和出口端分布数组
    // Init slices is just a guess, as the arrays are expanded
    // automatically if needed.  The expansion algorithm is simple,
    // just double the array size.  Only allocate a distribution
    // if not already allocated.
    if ( 0 == bite.entry.max_slices )
    {
        Allocate_Distribution(&bite.entry, bite.init_slices*2);		分配内存
    }
    if ( 0 == bite.exit.max_slices )
    {
        Allocate_Distribution(&bite.exit, bite.init_slices*2);
    }

    // Set number of slices used to zero
    bite.entry.num_slices = 0;
    bite.exit.num_slices = 0;


    //-------------------------------------------------------------------------
    // Boundary conditions							边界条件
    //-------------------------------------------------------------------------

    // Mean flow stress.
    // Will be replaced by flow stress calculation ??????KGM
    bite.entry.flows[0] = bite.flows_mean;
    bite.exit.flows[0]  = bite.flows_mean;

    // Calculate the entry and exit angles
    bite.phi_entry = acos(1.0F - (bite.entry_thk - bite.min_thk)/bite.wrdeform);		入口角（咬入角）
    if ( fabs ( bite.phi_entry ) < 1.0E-10 )
    {
        EMSG << (const char *)(this->objName())
             <<" Calculate_Pressure() phi_entry="<< bite.phi_entry
             <<" is too small.  entry_thk="<< bite.entry_thk
             <<"  min_thk="<< bite.min_thk
             <<"  wrdeform="<< bite.wrdeform
             << END_OF_MESSAGE ;
    }

    bite.entry.phi[0] = phi_e = bite.phi_entry;
    bite.exit.phi[0]  = phi_x = 0.0F;

    // Calculate the entry normal and tangential pressures				入口法向压力、切向压力（切向压力应该是摩擦力吧）
    bite.entry.norm_pressure[0] = (bite.entry.flows[0] - bite.entry_tension) /
                                 (1.0F + bite.friction_coeff*tan(bite.phi_entry));
    if ( bite.entry.norm_pressure[0]*bite.friction_coeff > bite.entry.flows[0] / 2.0F )		 检查摩擦条件是否满足，必要时调整法向压力，摩擦力小于材料剪切屈服极限时，产生滑移
    {
        bite.entry.norm_pressure[0] = bite.entry.flows[0] -
                                      bite.entry_tension -
                                      (bite.entry.flows[0]/2.0)*tan(bite.phi_entry);
    }
    bite.entry.tang_pressure[0] = Tangent_Pressure(					切向压力=摩擦系数*法向压力
                                        bite.friction_coeff,
                                        bite.entry.norm_pressure[0],
                                        bite.entry.flows[0]/2.0);					bite.entry.flows[0]/2.0表示材料剪切屈服强度

    // Calculate the exit normal and tangential pressures				出口法向压力、切向压力
    bite.exit.norm_pressure[0] = bite.exit.flows[0] - bite.exit_tension;
    bite.exit.tang_pressure[0] = Tangent_Pressure(
                                        bite.friction_coeff,
                                        bite.exit.norm_pressure[0],
                                        bite.exit.flows[0]/2.0);

    // Bite thickness at entry and exit						咬入厚度
    bite.exit.bite_thick[0] = bite.min_thk;						
    bite.entry.bite_thick[0] = bite.entry_thk;

    if ( bite.dbg_strain )
    {
        // Calculate the instantaneous strain rate at entry and exit			计算轧制过程中入口和出口处的瞬时应变速率（strain rate）
        bite.entry.strain_rate[0] = Instantaneous_Strain_Rate(
                                            bite.entry.phi[0], // angle
                                            bite.wrdeform/2.0, // deformed roll radius
                                            bite.wrdiam/2.0,   // un-deformed roll radius
                                            bite.min_thk,     // exit thickness
                                            bite.vroll         // roll surface speed
                                            );
        bite.exit.strain_rate[0]  = Instantaneous_Strain_Rate(
                                            bite.exit.phi[0],  // angle
                                            bite.wrdeform/2.0, // deformed roll radius
                                            bite.wrdiam/2.0,   // un-deformed roll radius
                                            bite.min_thk,     // exit thickness
                                            bite.vroll         // roll surface speed
                                            );
    }

    // Initialise angle step size for numerical integration				初始化数值积分的角度步长
    phi_xhtry = bite.phi_entry/bite.init_slices;   // exit side forward to entry side
    if ( fabs( phi_xhtry ) < 1.0E-10 )
    {
        EMSG << (const char *)(this->objName())
             <<" Calculate_Pressure() phi_xhtry= "<<phi_xhtry
             <<" is too small.  phi_entry="<< bite.phi_entry
             <<"  init_slices="<< bite.init_slices
             <<END_OF_MESSAGE ;
        phi_xhtry = 1.0E-10 ;
    }

    phi_ehtry  = - phi_xhtry;               // entry side backwards to exit side
    ie = ix = 0;                            // slice index

    //-----------------------------------------------------------------
    // Integrate the normal pressure across the roll bite from entry and			法向压力分布的数值积分求解
    // exit sides.  In each case take the next step on the side with
    // the lower value of normal pressure, except for the very first
    // step, which is taken on the exit side.  Continue until the sides
    // cross over.
    //-----------------------------------------------------------------
    while(1)
    {
        if ( !(ie == 0 && ix == 0) && bite.entry.norm_pressure[ie] < bite.exit.norm_pressure[ix] )		首次迭代且入口侧低于出口侧，从入口侧积分
        {
            // Take the next step on the entry side
            bite.exit_side = false;
            ie++;
            if ( false == Step_Side(					步进函数调用
                            ie,             // the current slice index
                            &bite.entry,     // the slice structure
                            phi_ehtry,      // the stepsize to try
                            phi_ehdone,     // the actual stepsize accomplished
                            phi_ehnext      // the estimated size for next step
                              ) )
            {
                EMSG << (const char *)(this->objName())
                     << " Error in evaluating step on entry side of rollbite  ie="<<ie
                     << END_OF_MESSAGE;
                char buff[132];
                sprintf(buff, "eth: %g, xth: %g, fs: %g, eten: %g, xten: %g, wrdiam: %g",
                        bite.entry_thk, bite.exit_thk, bite.flows_mean,
                        bite.entry_tension, bite.exit_tension, bite.wrdiam);
                EMSG << buff
                        << END_OF_MESSAGE;
                return false;
            }
            phi_ehtry = phi_ehnext;				更新下一步迭代的步长
            if ( (bite.entry.phi[ie] + phi_ehtry) <  bite.exit.phi[0] )
            {
                // Must use ix = 1, it is always forced to be 1 before this code is entered
                if ( (ix == 1) &&  (bite.exit.phi[0] == bite.entry.phi[ie]) 
                      && (bite.entry.norm_pressure[ie] < bite.exit.norm_pressure[0]) )
                {
                    // we have reached the exit plane with everywhere lower
                    // normal pressure than the exit plane boundary condition.
                    // This situation may arise when we have high entry tension
                    // and low draft.  We need to take car of this condition,
                    // otherwise the step algorithm will fail.
                    EMSG << (const char *)(this->objName())
                         << " Exit plane boundary reached without neutral crossover"
                         << END_OF_MESSAGE;
                    char buff[132];
                    sprintf(buff, "eth: %g, xth: %g, fs: %g, eten: %g, xten: %g, wrdiam: %g",
                            bite.entry_thk, bite.exit_thk, bite.flows_mean,
                            bite.entry_tension, bite.exit_tension, bite.wrdiam);
                    EMSG << buff
                         << END_OF_MESSAGE;
                    bite.status = rb_phi_neut_beyond_exit;
                    return false;
                }
                phi_ehtry = bite.exit.phi[0] - bite.entry.phi[ie];
            }
        }
        else							非首次迭代或者入口侧高于出口侧，从出口侧积分
        {
            // Take the next step on the exit side
            bite.exit_side = true;
            ix++;
            if ( false == Step_Side(					步进函数调用，	利用卡尔曼微分方程求解
                            ix,             // the current slice index
                            &bite.exit,     // the slice structure
                            phi_xhtry,      // the stepsize to try
                            phi_xhdone,     // the actual stepsize accomplished
                            phi_xhnext      // the estimated size for next step
                                   ) )
            {
                EMSG << (const char *)(this->objName())
                     << " Error in evaluating step on exit side of rollbite  ix="<<ix
                     << END_OF_MESSAGE;
                char buff[132];
                sprintf(buff, "eth: %g, xth: %g, fs: %g, eten: %g, xten: %g, wrdiam: %g",
                        bite.entry_thk, bite.exit_thk, bite.flows_mean,
                        bite.entry_tension, bite.exit_tension, bite.wrdiam);
                EMSG << buff
                     << END_OF_MESSAGE;
                return false;
            }
            phi_xhtry = phi_xhnext;
            if ( (bite.exit.phi[ix] + phi_xhtry) > bite.entry.phi[0] )			步长自适应调整
            {
                if ( (ie == 0) && (bite.entry.phi[0] == bite.exit.phi[ix]) )	
                {
                    // we have reached the entry plane with everywhere lower
                    // normal pressure than the entry plane boundary condition.
                    // This situation may arise when we have high exit tension
                    // and low draft.  We need to take care of this condition,
                    // otherwise the step algorithm will fail.
                    EMSG << (const char *)(this->objName())
                         << " Entry plane boundary reached without neutral crossover"
                         << END_OF_MESSAGE;
                    char buff[132];
                    sprintf(buff, "eth: %g, xth: %g, fs: %g, eten: %g, xten: %g, wrdiam: %g",
                            bite.entry_thk, bite.exit_thk, bite.flows_mean,
                            bite.entry_tension, bite.exit_tension, bite.wrdiam);
                    EMSG << buff
                         << END_OF_MESSAGE;
                    bite.status = rb_phi_neut_beyond_entry;
                    return false;
                }
                phi_xhtry = bite.entry.phi[0] - bite.exit.phi[ix];
            }
        }
        //------------------------------------------------------------
        // Terminate the pressure integration when the two sides have
        // crossed over.  A cross over of angle is a necessary, but not
        // sufficient condition for this.
        //------------------------------------------------------------
        if ( bite.exit.phi[ix] > bite.entry.phi[ie] )
        {
            // Now estimate the position of the neutral point by		通过计算压力分布交叉的位置来估计中性点的位置
            // calculating where the pressure distributions cross
            // over.
            double   be, bx, me, mx;
            linear_mxb(
                bite.exit.phi[ix-1], bite.exit.norm_pressure[ix-1],		计算出口侧线段的斜率(mx)和截距(bx)
                bite.exit.phi[ix],   bite.exit.norm_pressure[ix],
                mx, bx);
            linear_mxb(	
                bite.entry.phi[ie-1], bite.entry.norm_pressure[ie-1],	计算入口侧线段的斜率(mx)和截距(bx)
                bite.entry.phi[ie],   bite.entry.norm_pressure[ie],
                me, be);
            // Having linear equations for the two crossing segments,
            // calculate the neutral point angle.
            bite.phi_neut = (bx - be) / (me - mx);			计算中性点	
            // The neutral point should lie between the terminating
            // points on the entry and exit side distributions.  If not
            // we need to take at least one more step
            if ( (bite.entry.phi[ie] <= bite.phi_neut) &&			检验中性点是否在两侧最后计算点之间
                 (bite.phi_neut <= bite.exit.phi[ix]) )
            {
                if ( (bite.phi_neut <= bite.exit.phi[ix-1]) ||		进一步检验中性点是否在合理区间
                     (bite.entry.phi[ie-1] <= bite.phi_neut) )
                {
                    EMSG << (const char *)(this->objName())
                         << " Problem with neutral point"
                         << END_OF_MESSAGE;
                    char buff[132];
                    sprintf(buff, "eth: %g, xth: %g, fs: %g, eten: %g, xten: %g, wrdiam: %g",
                            bite.entry_thk, bite.exit_thk, bite.flows_mean,
                            bite.entry_tension, bite.exit_tension, bite.wrdiam);
                    EMSG << buff
                         << END_OF_MESSAGE;
               }
                break;
            }
        }
    }

    // We need to force the calculated neutral point to lie on a		强制让计算出的中性点恰好落在某个离散的切片上
    // slice.  For convenience we apply another step(s) in the exit
    // direction from the slice just prior to the neutral point,
    // up to the neutral point.
    bite.exit_side = true;
    bite.exit.num_slices--;
    bite.entry.num_slices--;
    while(1)
    {
        // We are actually retrying the last step
        phi_xhtry = bite.phi_neut - bite.exit.phi[ix-1];
        if ( false == Step_Side(
                        ix,             // the current slice index
                        &bite.exit,      // the slice structure
                        phi_xhtry,      // the stepsize to try
                        phi_xhdone,     // the actual stepsize accomplished
                        phi_xhnext      // the estimated size for next step
                               ) )
        {
            EMSG << (const char *)(this->objName())
                 << " Error in evaluating step at neutral point"
                 << END_OF_MESSAGE;
            char buff[132];
            sprintf(buff, "eth: %g, xth: %g, fs: %g, eten: %g, xten: %g, wrdiam: %g",
                    bite.entry_thk, bite.exit_thk, bite.flows_mean,
                    bite.entry_tension, bite.exit_tension, bite.wrdiam);
            EMSG << buff
                    << END_OF_MESSAGE;
            return false;
        }
        if ( phi_xhtry == phi_xhdone )
        {
            break;
        }
        else
        {
            ix++;
            phi_xhtry -= phi_xhdone;
        }
    }

    bite.idx_neut = ix;
    bite.nslice_entry = bite.entry.num_slices-1;
    bite.nslice_exit = bite.exit.num_slices;

    // Allocate the final pressure distribution if needed
    if ( bite.final.max_slices < (bite.entry.num_slices + bite.exit.num_slices + 1) )
    {
        Allocate_Distribution(
            &bite.final,
            bite.entry.num_slices + bite.exit.num_slices + 1);
    }

    // Assign data from exit side to entry side, combining		将数据从出口侧分配到入口侧，结合之前计算的分布和中性点
    // the previously calculated distributions and neutral
    // point.

    // Exit side
    for ( i=0; i<bite.exit.num_slices; i++)
    {
        bite.final.norm_pressure[i] = bite.exit.norm_pressure[i];
        bite.final.tang_pressure[i] = bite.exit.tang_pressure[i];
        bite.final.bite_thick[i]    = bite.exit.bite_thick[i];
        if ( bite.dbg_strain )
        {
            bite.final.strain_rate[i]   = bite.exit.strain_rate[i];
        }
        bite.final.strip_speed[i]   = bite.vroll *
                                      bite.exit.bite_thick[bite.idx_neut] /
                                      bite.exit.bite_thick[i];
        bite.final.phi[i]           = bite.exit.phi[i];
    }

    // Entry side
    for ( i=bite.entry.num_slices-1, j=bite.exit.num_slices; i>=0; i--, j++)
    {
        bite.final.norm_pressure[j] = bite.entry.norm_pressure[i];
        bite.final.tang_pressure[j] = bite.entry.tang_pressure[i];
        bite.final.bite_thick[j]    = bite.entry.bite_thick[i];
        if ( bite.dbg_strain )
        {
            bite.final.strain_rate[j]   = bite.entry.strain_rate[i];
        }
        bite.final.strip_speed[j]   = bite.vroll *
                                      bite.final.bite_thick[bite.idx_neut] /
                                      bite.entry.bite_thick[i];
        bite.final.phi[j]           = bite.entry.phi[i];
    }

    // Total slices in final distribution
    bite.final.num_slices = bite.entry.num_slices + bite.exit.num_slices;

    bite.slip = bite.final.strip_speed[0]/bite.vroll;				计算并存储滑移率
    bite.vstrip_exit = bite.final.strip_speed[0];				计算并存储出入口速度
    bite.vstrip_entry = bite.final.strip_speed[bite.final.num_slices-1];

    // Consistency check on neutral angle
    if ( bite.final.phi[ bite.idx_neut] != bite.phi_neut )
    {
        EMSG << (const char *)(this->objName())
             << " Calculate_Pressure(): Neutral angle mismatch"
             << END_OF_MESSAGE;
        Dump(1);
        return false;
    }

    //-------------------------------------------------------------------
    // Calculate the Neutral angle by Sims formula, use deformed radius		用Sims公式计算中性角，使用变形半径
    //-------------------------------------------------------------------
    bite.phi_neut_sims =  Neutral_Sims(
                                bite.wrdeform,      // IN  deformed roll diameter
                                bite.min_thk,      // IN  exit thickness
                                bite.entry_thk,     // IN  entry thickness
                                bite.exit_tension,  // IN  exit tension
                                bite.entry_tension, // IN  entry tension
                                bite.flows_mean);   // IN  mean flow stress

    // Calculate the mean strain rate
    bite.mean_strain = Mean_Strain_Rate(
                            bite.wrdeform / 2.0,
                            bite.entry_thk,
                            bite.min_thk,
                            bite.vroll * Physcon.vel_time);

    // Declare the pressure calculation valid
    bite.pressure_valid = true;

    return true;

} // cRollbite::Calculate_Pressure


//--------------------------------------------------------------------------
// This method calculates the roll separating force.
//--------------------------------------------------------------------------
bool    cRollbite::Calculate_Force (void) 
{
    double      x1;
    double      x2;
    double      x3;
    double      norm_angle;
    double      dphi;
    double      precision = bite.precision/2.0;
    double      force_error;
    double      cos_phi;
    double      sin_phi;

    status_type e_status;

    int     i;

    if ( !bite.inputs_valid )		输入有效性检查，检查类内部存储的输入参数（如轧辊直径、厚度、张力等）是否有效。
    {				若无效（如参数未初始化或错误），则重置所有计算结果并返回失败（false）
        Invalidate_All_Calcs();
        return false;
    }

    if ( bite.force_valid )		避免重复计算
    {
        return true;
    }
    bite.force_iter = 0;		初始化轧制力计算的迭代计数器，不超过100

    if ( precision > 0.01 )		精度控制，限制计算精度的下限（不低于 0.01）
    {
        precision = 0.01F;
    }

    do
    {
        //--------------------------------------------------------------------
        //  After the first time through the loop,  re-calculate the
        //  apparent deformed work roll radius.
        //  For the first loop,  the deformed radius is set elsewhere
        //  to the specified undeformed roll radius.
        //--------------------------------------------------------------------
        if ( bite.force_iter > 0)
        {
            bite.min_slices = bite.init_slices;      // set slices to our desired guess				wrdeform：考虑弹性压扁变形后的轧辊等效直径
            bite.wrdeform = 2.0*Deformed_Radius (    // OUT deformed roll radius [minor_length]		调用rollbite中的Deformed_Radius函数
                                bite.wrdiam/2.0,     // IN undeformed radius [minor_length]
                                bite.hitch_draft,    // IN strip reduction [minor_length]
                                bite.force_minor,    // IN force/unit width [minor_force] /
                                                     //                     [minor_length]
                                bite.hitchcock );    // IN Hitchcock's constant [pressure]
            bite.force_guess = bite.force_minor;
        }

        // Make sure that all calculations are invalidated
        Invalidate_All_Calcs();
        if ( false == Calculate_Pressure() )					压力计算有效性检查
        {
            bite.min_slices = bite.init_slices;
            return false;
        }

        //--------------------------------------------------------------
        // If selected, estimate the force and torque contribution from
        // the elastic zones,  the effective tension, and the minimum gap.
        //---------------------------------------------------------------
        if ( bite.elast_calcs )
        { 
            Elastic_Force_Tension (						调用弹性区计算函数，计算弹性区的力、张力
                     &e_status,           // OUT status of elastic calculation
                     &bite.entry_ten_pls, // OUT effective tension applied to
                     &bite.exit_ten_pls,  // OUT    zones of plastic deformation
                     &bite.elfrc_entry,   // OUT force/width, entry side
                     &bite.elfrc_exit,    // OUT force/width, exit side
                     bite.wrdeform/2.0,   // IN deformed radius
                     bite.entry_thk,      // IN entry thickness
                     bite.exit_thk,       // IN exit  thickness
                     bite.entry_tension,  // IN externally applied entry tension
                     bite.exit_tension,   // IN externally applied entry tension
                     bite.flows_mean,     // IN entry flowstress
                     bite.flows_mean,     // IN exit flowstress
                     bite.friction_coeff, // IN coefficient of friction
                     bite.strip_poisson,  // IN poisson ratio of the strip
                     bite.strip_modulus,  // IN strip modulus
                     bite.entry_str,      // IN entry thickness to plastic zone
                     bite.exit_str,       // IN exit thickness from plastic zone
                     bite.min_thk );      // IN minimum gap

            if ( e_status != rb_valid ) 
            {
                EMSG << (const char *)(this->objName())
                     << " Calculate_Force(): Error from Elastic_Force_Tension()"
                     << END_OF_MESSAGE;
                return false;
            }
 
            bite.e_force = bite.elfrc_entry + bite.elfrc_exit;  			计算总弹性区力贡献（单位宽度）
                                   // per unit width total elastic force
                                   // contribution [minor_force]/[minor_length]
 
            //--------------------------------------------------------------
            // Calculate elastic torque contribution - same for all models.
            // Slipping friction is assumed.
            // Elastic zone torque is positive ( retarding roll movement )
            // at entry and negative ( assisting roll movement ) at exit.
            // e_torque is for both rolls.
            // Standard torque unit is [minor_force]*[major_length]
            //     [ft-lb],[kg-m],[N-m]
            // torque = tangential force * lever arm
            //--------------------------------------------------------------
            bite.e_torque = ( 2.0 * (bite.friction_coeff * bite.elfrc_entry) 				弹性区的扭矩贡献
                * ( (bite.wrdiam/2.0) / Physcon.mmpm_inpft) )         //   entry side, retarding
                - ( 2.0 * (bite.friction_coeff * bite.elfrc_exit) 
                * ( (bite.wrdiam/2.0) / Physcon.mmpm_inpft) );        // exit side, accelerating

        } 
        else // ignore the effect of the elastic zones.
        { 
            bite.entry_ten_pls = bite.entry_tension; // effective tension
                                                   // = external tension
            bite.exit_ten_pls  = bite.exit_tension;
            bite.elfrc_entry   = 0.0;
            bite.elfrc_exit    = 0.0;
            bite.e_force       = 0.0;
            bite.e_torque      = 0.0;
 
        } // end if (elastic_contributions)
 

        x1 = x2 = x3 = 0;
        for ( i=0; i<bite.final.num_slices-1; i++ )
        {
            norm_angle = 0.5*(bite.final.phi[i] + bite.final.phi[i+1] - bite.phi_entry);
            dphi = (bite.final.phi[i+1] - bite.final.phi[i]);

            // Integrate force using midpoint rule
            // Consider the resolved component of normal pressure
            if ( bite.precise_derivs )					
            {
                cos_phi = cos(norm_angle);
            }
            else
            {
                cos_phi = 1.0 - norm_angle*norm_angle/2.0;			泰勒展开
            }
            x1 += (bite.final.norm_pressure[i] + bite.final.norm_pressure[i+1]) *
                  cos_phi * dphi * 0.5;
            if ( bite.precise_frc )
            {
                if ( bite.precise_derivs )
                {
                    sin_phi = sin(norm_angle);
                }
                else
                {
                    sin_phi = norm_angle*(1.0 - norm_angle*norm_angle/6.0);
                }
                // Consider the resolved component of tangential pressure
                if ( i < bite.nslice_exit - 1 )
                {
                    // Exit side of neutral point
                    x2 += (bite.final.tang_pressure[i] + bite.final.tang_pressure[i+1]) *
                          sin_phi * dphi * 0.5;
                }
                else
                {
                    // entry side of neutral point
                    x3 += (bite.final.tang_pressure[i] + bite.final.tang_pressure[i+1]) *
                          sin_phi * dphi * 0.5;
                }
            }
        }

        // Add the individual components
        bite.force_minor = 0.5*bite.wrdeform*(x1 - x2 + x3) + bite.e_force;

        bite.force_iter++;

        if ( bite.force_iter > 100 )
        {
            EMSG << (const char *)(this->objName())
                 << " Calculate_Force(): Too many iterations "
                 << " wrdef=" << bite.wrdeform
                 << " diam="  << bite.wrdiam
                 << " dft="   << bite.hitch_draft
                 << " frc="   << bite.force_minor
                 << " hitchcock="  << bite.hitchcock
                 << " flows_mean=" << bite.flows_mean
                 << " entry_thk="  << bite.entry_thk
                 << END_OF_MESSAGE;
            break;
        }

        force_error = (bite.force_minor - bite.force_guess)/bite.force_minor;
    } while ( fabs(force_error) > precision );


    bite.force = bite.force_minor / Physcon.kgpt_lbpt;  // convert from minor to major force
    bite.force_valid = true;
    bite.slip_valid = true;

    bite.arcon_valid = true;
    bite.arcon = bite.phi_entry * bite.wrdeform * 0.5;

    return true;

} // cRollbite::Calculate_Force


//--------------------------------------------------------------------------
// This method calculates the roll torque.
//--------------------------------------------------------------------------
bool    cRollbite::Calculate_Torque (void)
{
    double   x1;
    double   x2;
    double   x3;
    double   r1;
    double   rd;
    double   rn;
    double   norm_angle;
    double   dphi;
    double   norm;
    double   tang;
    double   cos_phi;
    double   sin_phi;
    int     i;

    if ( !bite.inputs_valid )
    {
        Invalidate_All_Calcs();
        return false;
    }

    if ( bite.torque_valid )
    {
        return true;
    }
    if ( !Calculate_Force() )
    {
        EMSG << "Error in Calculate_Force()"
             << END_OF_MESSAGE;
        Invalidate_All_Calcs();
        return false;
    }
    x1 = x2 = x3 = 0.0;
    rd = 0.5 * bite.wrdeform;						变形辊半径
    rn = 0.5 * bite.wrdiam;						原始辊半径
    r1 = rd - rn;							弹性压扁导致的半径增量
    for ( i=0; i<bite.final.num_slices-1; i++ )
    {
        norm_angle = 0.5*(bite.final.phi[i] + bite.final.phi[i+1] - bite.phi_entry);
        dphi = (bite.final.phi[i+1] - bite.final.phi[i]);
        norm = 0.5 * (bite.final.norm_pressure[i] + bite.final.norm_pressure[i+1]);
        tang = 0.5 * (bite.final.tang_pressure[i] + bite.final.tang_pressure[i+1]);

        // Integrate torque using midpoint rule
        // Consider the resolved component of normal pressure
        if ( bite.precise_derivs )						计算 cos_phi
        {
            cos_phi = cos(norm_angle);					精确计算
        }
        else
        {
            cos_phi = 1.0 - norm_angle*norm_angle/2.0;			近似计算
        }
        if ( bite.precise_trq )
        {
            if ( bite.precise_derivs )
            {
                sin_phi = sin(norm_angle);
            }
            else
            {
                sin_phi = norm_angle*(1.0 - norm_angle*norm_angle/6.0);
            }
            x1 += r1 * norm * sin_phi * dphi;
        }
        // Consider the resolved component of tangential pressure
        if ( i < bite.idx_neut )
        {
            // Exit side of neutral point
            x2 += tang * (rd -  r1 * cos_phi) * dphi;
        }
        else
        {
            // Entry side of neutral point
            x3 += tang * (rd -  r1 * cos_phi) * dphi;
        }
    }

    // Add the individual components, include both rolls, adjust units
    bite.torque = 2.0 * rd * (x1 + x3 - x2)/ Physcon.mmpm_inpft;

    // Add contribution due to elastic contributions
    bite.torque += bite.e_torque;

    bite.torque_valid = true;
	return true;

} // cRollbite::Calculate_Torque


//--------------------------------------------------------------------------
// This method calculates the deformation, friction, torque and tension
// powers.
//--------------------------------------------------------------------------
bool    cRollbite::Calculate_Power (void)
{
    double   dphi, phi;
    double   pfrict = 0.0;
    double   tang;
    double   thick_fact, speed_fact, speed_fact_1;
    double   cos_phi;

    int     i;

    if ( !bite.inputs_valid )				若数据异常
    {
        Invalidate_All_Calcs();				重置所有计算结果
        return false;
    }

    if ( bite.frict_pwr_valid && bite.deform_pwr_valid &&	避免重复计算检查
         bite.tension_pwr_valid && bite.torque_pwr_valid )
    {
        return true;
    }
    if ( false == ( Calculate_Force() && Calculate_Torque() ) )		确保计算功率所需要的力和扭矩已成功计算
    {
        Invalidate_All_Calcs();
        return false;
    }

    // Calculate friction power
    speed_fact_1 = 1 / (bite.min_thk*bite.slip);
    for ( i=0; i<bite.final.num_slices-1; i++ )
    {
        phi  = (bite.final.phi[i+1] + bite.final.phi[i]) / 2.0;
        if ( bite.precise_derivs )
        {
            cos_phi = cos(phi);
        }
        else
        {
            cos_phi = 1.0 - phi*phi/2.0;
        }

        dphi = (bite.final.phi[i+1] - bite.final.phi[i]);
        tang = (bite.final.tang_pressure[i] + bite.final.tang_pressure[i+1]) / 2.0;
        thick_fact = (bite.final.bite_thick[i] + bite.final.bite_thick[i+1]) / 2.0;
        speed_fact = speed_fact_1 - 1 / ( thick_fact * cos_phi);
        speed_fact = fabs(speed_fact);

        // Integrate friction power using midpoint rule				计算摩擦功率
        pfrict += 0.5 * bite.wrdeform *
                  tang *
                  speed_fact *
                  dphi;
    }

    //-------------------------------------------------------------------------
    // convert the mechanical power to standard power units / volume flow,
    // where volume flow has units of [minor_length**3/minor_time]
    //     engish:  kW/inch**3/s = ((BTU/ft-lb)/((BTU/s)/(kW))) * ft-lb/inch**3
    //     metric:  kW/mm**3/s   = ((kcal/joule)/((kcal/s)/kw)) * joule/mm**3 
    //     SI:      kW/mm**3/s   = ((joule/joule)/(W/kW)) * joule/mm**3  
    //-------------------------------------------------------------------------
    //------------------------------------------------------------------------
    // calculate conversion factor.
    //------------------------------------------------------------------------
    double   temp1 = (Physcon.therm_mech * Physcon.work_force) /
                     (Physcon.heat_power * Physcon.mmpm_inpft);

    //--------------------------------------------
    // calculate the friction power / volume_flow
    //--------------------------------------------
    bite.frict_pwr = 2.0 * pfrict * temp1;
    bite.frict_pwr_valid = true;

    //-------------------------------------------------------------------------
    // calculate deformation power / volume_flow.  Assumes constant flow stress
    // Will need to be replaced with an integrated version when flow stress //??????KGM
    // is calculated within the roll bite.
    //-------------------------------------------------------------------------
    bite.deform_pwr = bite.flows_mean *                    // flow stress [minor_force]/([minor_length]**2)		计算变形功率
                      log(bite.entry_thk/bite.min_thk) *  // compression ratio term [-]
                      temp1;                               // conversion
	if (bite.alternate_power)
	{
#define A0 0.398914
#define A1 0.343784337
#define A2 -0.120634215
#define AMAX 1.30

		float alt_power_factor;
		float alt_power_modifier;

		alt_power_factor = bite.wrdiam/2.0/bite.min_thk;
		if (alt_power_factor < 3.0) alt_power_factor = 3.0;
		alt_power_modifier = (A0 + A1*exp(A2*alt_power_factor))/0.5;

		bite.deform_pwr*=alt_power_modifier;
	}
	bite.deform_pwr_valid = true;

    //------------------------------------------------------------------------
    // The drive in each stand must supply power to pull the strip into 
    // the stand in opposition to the tension on the entry side.  However,
    // the drive receives power from the tension on the exit side pulling
    // the strip through the roll bite. The net tension power is the sum
    // of these two powers, taking the exit side as negative.
    // 
    // tension_pwr = power / volume flow
    //------------------------------------------------------------------------
    bite.tension_pwr = temp1 * ( bite.entry_tension - bite.exit_tension );
    bite.tension_pwr_valid = true;

    //-----------------------------------------------------------------------
    //  Convert torque units times angular velocity to power units:
    // 
    //  1. torque * 1/sec * physWorkForce =
    //     [major_length * force] * 1/sec * [mech_energy/(major_length * force)]
    //                 = [mech_energy/sec].
    // 
    //  2. [mech_energy/sec] * physThermMech =
    //     [mech_energy/sec] * [thermal_energy/mech_energy] 
    //                 = [thermal_energy/sec].
    //     
    //  3. [thermal_energy/sec] / [(thermal_energy/sec) / KW] = [KW]
    //
    //  NOTE:  radians/sec = (distance/sec) * (2*PI) / roll_circumference
    //                     = (distance/sec) / roll_radius
    //
    //-----------------------------------------------------------------------
    // Calculate angular velocity in [radians/sec] from roll peripheral speed 
    // in [minor_length/time] and radius in [minor_length].
    double rad_per_sec = bite.vroll * Physcon.mmpm_inpft / (bite.wrdiam/2.0);

    //----------------------------------------------------
    // calculate power from torque in standard power units
    //----------------------------------------------------
    bite.torque_pwr = bite.torque *
                      rad_per_sec *
                      (Physcon.work_force * Physcon.therm_mech) /
                      Physcon.heat_power;

    bite.torque_pwr_valid = true;

    //-------------------------------------------------------------------------
    // Sum power components to get shaft power and compare with torque power
    // If all is an ideal world, they should be the same if in standard power
    // units.  However, shaft power is in power units / vloume flow and torque
    // power is in power units / width.  Therefore pwr_ratio should be close to 
    // width / volume flow.
    //-------------------------------------------------------------------------
    bite.shaft_pwr = bite.frict_pwr + bite.deform_pwr + bite.tension_pwr;
    bite.shaft_pwr_valid = true;

    if ( bite.torque_pwr != 0.0 )
    {
        bite.pwr_ratio = bite.shaft_pwr / bite.torque_pwr;
    }
    else
    {
        bite.pwr_ratio = 1.0;
    }

    return true;

} // cRollbite::Calculate_Power


//--------------------------------------------------------------------------
// This method calculates all rollbite quantities.
//--------------------------------------------------------------------------
bool    cRollbite::Calculate_All (void)
{

    if ( false == (Calculate_Force() && Calculate_Torque() && Calculate_Power()) )
    {
        return false;
    }

    return true;

} // cRollbite::Calculate_All


//--------------------------------------------------------------------------
// Dump the context of the object
//--------------------------------------------------------------------------
void    cRollbite::Dump ( int level, FILE *fp )
{
    int     i;
    double   tang_pressure;

    fprintf(fp, "\n");
    Dump_Data(fp, "rollbite_type", &bite, 0, "bite");

    fprintf(fp, "\nFinal Pressure Distribution\n");
    fprintf(fp, "Phi\tNormal_Pressure\tTangential Pressure\tBite_Thick\tStrain_Rate\tStrip_Speed\tFlow_Stress\n");
    for ( i=0; i<bite.final.num_slices; i++)
    {
        double   calc_flow_stress = (25.4*25.4*2.2)*0.125*pow(bite.final.strain_rate[i], 0.15)*exp(5500.0/(1250.0+273.0));
        fprintf(fp, "%8.6f\t%6.1f\t%6.1f\t%8.4f\t%7.3f\t%7.3f\t%7.2f\n",
            bite.final.phi[i], bite.final.norm_pressure[i],
            bite.final.tang_pressure[i], bite.final.bite_thick[i],
            bite.final.strain_rate[i], bite.final.strip_speed[i],
            calc_flow_stress);
    }
    if ( level > 0 )
    {
        fprintf(fp, "Phi\tNormal_Pressure\tTangential_Pressure\n");
        for ( i=0; i<bite.entry.num_slices-1; i++)
        {
            bite.entry.tang_pressure[i] = bite.friction_coeff*bite.entry.norm_pressure[i];
            if ( bite.entry.tang_pressure[i] > bite.flows_mean/2.0 )
            {
                bite.entry.tang_pressure[i] = bite.flows_mean/2.0;
            }
            fprintf(fp, "%8.6f\t%6.1f\t%6.1f\n",
                bite.entry.phi[i], bite.entry.norm_pressure[i],
                bite.entry.tang_pressure[i]);
        }
        tang_pressure = bite.friction_coeff*bite.norm_pressure_neut;
        if ( tang_pressure > bite.flows_mean/2.0 )
        {
            tang_pressure = bite.flows_mean/2.0;
        }
        fprintf(fp, "%6.4f\t%6.1f\t%6.1f\n",
            bite.phi_neut, bite.norm_pressure_neut,
            tang_pressure);

        for ( i=bite.exit.num_slices-2; i>=0; i--)
        {
            bite.exit.tang_pressure[i] = bite.friction_coeff*bite.exit.norm_pressure[i];
            if ( bite.exit.tang_pressure[i] > bite.flows_mean/2.0 )
            {
                bite.exit.tang_pressure[i] = bite.flows_mean/2.0;
            }
            fprintf(fp, "%8.6f\t%6.1f\t%6.1f\n",
                bite.exit.phi[i], bite.exit.norm_pressure[i],
                bite.exit.tang_pressure[i]);
        }
    }

    // For testing dynamic arrays
    //Dump_Config(stdout, "slice_type", &bite.final, 0);
    //Dump_Schema(stdout, "slice_type", 0);
    //Dump_Schema_CSV(stdout, "slice_type", true);
    
    return;
}

