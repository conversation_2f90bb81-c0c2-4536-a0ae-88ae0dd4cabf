       //---------------------------------------------------------------
       // Copyright (c) 2006 by
       // Toshiba Mitsubishi-Electric Industrial Systems Corp. 
       // TMGE Automation Systems LLC, U.S.A.
       // Published in only a limited, copyright sense, and all
       // rights, including trade secret rights are reserved.
       //---------------------------------------------------------------
//-----------------------------------------------------------------------------
//
//     TITLE:         Roll Bite Model Class Implementation - Private methods
//
//     FILE NAME:     MDS\SOURCE\ROLLBITE_PRIV.CXX
//
//     PREPARED BY:   TMGE Automation Systems LLC, U.S.A.
//                      1501 Roanoke Blvd., Salem, Virginia, USA
//                    Toshiba Mitsubishi-Electric Industrial Systems Corp.
//                      Mita 43 MT Bldg. Mita 3-13-16, Minato-ku Tokyo, Japan
//
//     CUSTOMER:      STANDARD
//
//     SYSTEM:        Mill Automation System
//
//--------------------------------------------------------------------------------------
//
//     REVISIONS:
//     level  review date  author        change content
//     -----  -----------  ------------  -----------------------------------------------
//     1.0-0  20-Sep-2006  FM Williams   Original
//--------------------------------------------------------------------------------------

//-----------------------------------------------------------------------------
//
//
// ABSTRACT:  This module implements methods private to the rollbite class.
//            
//                              
//     PRIVATE FUNCTION         DESCRIPTION
//     -----------------------  -----------------------------------------------
//     Allocate_Distribution()  Allocate or resize a roll bite slice distribution.
//
//     Delete_Distribution()    Delete a roll bite slice distribution.
//
//     Copy_Distribution()      Copy a roll bite slice distribution.
//
//     Step_Side()              Advance the solution of von Karman's equation by
//                              one step on a particular side of the neutral point.
//
//     Tangent_Pressure()       Calculate the roll tangential pressure observing
//                              the mixed boundary condition for sliding or
//                              sticking.
//
//     Bite_Thickness()         Calculate the roll bite thickness at any angle
//                              within the roll bite.
//
//     Instantaneous_Strain_Rate() Calculate the instantaneous strain rate at any
//                              angle within the roll bite.
//
//     Deformed_Radius()        Calculate the deformed roll radius.
//
//     Elastic_Thickness()      Calculate the strip thickness at the entry and exit
//                              of the plastic zones and the minimum gap thickness.
//
//     Elastic_Force_Tension()  Calculate the force contributions of the entry and
//                              exit elastic zones.
//
//     Calc_G1_Phi()            Calculate the g1(phi) term in von Karman's equation.
//                              Refer to cRollbite documentation.
//
//     Calc_G2_Phi()            Calculate the g2(phi) term in von Karman's equation.
//                              Refer to cRollbite documentation.
//
//     Calc_G3_Phi()            Calculate the g3(phi) term in von Karman's equation.
//                              Refer to cRollbite documentation.
//
//     Calc_Ds_Dphi()           Calculate ds/dphi considering sliding and sticking
//                              in the roll bite.
//
//     Derivs()                 Calculate the RHS derivative in von Karman's eqn.
//                              Marshalls parameters and calls Calc_Ds_Dphi().
//
//     Step_CKRK5_Adaptive()    Advance the solution of a differential equation by
//                              a single step using the C-K RK5 algorithm with
//                              adaptive step size adjustment.
//
//     Step_CKRK5()             Advance the solution of a differential equation by
//                              a single step using the C-K RK5 algorithm with
//                              fixed step size.  Called by Step_CKRK5_Adaptive().
//
//
//
//-----------------------------------------------------------------------------

#define __ROLLBITE_MAINLINE__

//------------------------------
// C++ standard library includes
//------------------------------
#include <math.h>

#include "alarm.hxx"
#include "physcon.hxx"
#include "rbheat.hxx"
#include "flowstress.hxx"
#include "rollbite.hxx"
#include "mathuty.hxx"

#ifdef WIN32
	#ifdef _DEBUG
	#define new DEBUG_NEW
	#endif
#endif

#ifdef WIN32
    #pragma warning(disable: 4244) // double to float conversion (NT thinks constants are doubles)
    #pragma warning(disable: 4305) // truncation from 'const double' to 'const float'
    #pragma warning(disable: 4505) // disable unreferenced inline function warning
#endif


//-----------------------------------------------------------------------------
// Private member functions
//-----------------------------------------------------------------------------

//------------------------------------------------------------------------
// Take one step on a side, solving von karman's equation			解卡尔曼微分方程
//------------------------------------------------------------------------
bool    cRollbite::Step_Side(
                    int         istep,      // the current slice index
                    slice_type  *slice,     // the slice structure
                    double       phi_htry,   // the stepsize to try
                    double&      phi_hdone,  // the actual stepsize accomplished
                    double&      phi_hnext   // the estimated size for next step
                            ) // returns true if step taken OK.
{
    double   dsdphi;
    double   yscal;
    bool    result;

    // Check if we have exceeded maximum allowed slices
    if ( istep > MAX_GAP_SLICES )
    {
        EMSG << "Too many slices required"
             << END_OF_MESSAGE;
        return false;
    }

    if ( phi_htry == 0.0 )
    {
        EMSG << "Step size = 0.0, Step_Side() aborted"
             << END_OF_MESSAGE;
        return false;
    }
    // increment slices used
    slice->num_slices = istep+1;

    // Expand distribution if needed
    if ( istep >= slice->max_slices )
    {
        Allocate_Distribution(slice, slice->max_slices*2);
    }

    // Propagate any required data from the previous slice to this slice
    slice->phi[istep] = slice->phi[istep-1];
    slice->norm_pressure[istep] = slice->norm_pressure[istep-1];

    // Temperature is just propagated here, forcing the use of mean
    // temperature.  We may eventually replace this with a call to
    // some method to calculate temperature.
    // ??????KGM
    slice->temp_mean[istep] = slice->temp_mean[istep-1];

    // Flow stress is just propagated here, forcing the use of mean flow
    // stress.  We may eventually replace this with a call to
    // some method to calculate flow stress.
    // ??????KGM
    slice->flows[istep] = slice->flows[istep-1];

    // Calculate derivative at the starting point.  This is done outside
    // Step_CKRK5_Adaptive() since the step may be retried and we don't
    // want to unnecessarily recalculate the starting point.
    Derivs(
        slice->phi[istep],              // IN  [-] angle phi from exit plane 
        slice->norm_pressure[istep],    // IN  [-] normal pressure at angle phi
        dsdphi);                        // OUT [-] derivative of normal pressure

    // Determine the local scaling factor used to scale the desired global
    // error for this step.
    yscal = fabs(slice->norm_pressure[istep]) + fabs(phi_htry*dsdphi);

    if ( bite.err_bounds )
    {
        // Perform one step in the numerical integration.  Stepsize may be
        // adjusted as a result and an estimate of the stepsize for the next
        // step is given.
        result = Step_CKRK5_Adaptive (
            slice->norm_pressure[istep], // dependant variable
            dsdphi,             // derivative at starting point
            &slice->phi[istep], // starting value for independant variable
            phi_htry,           // stepsize to be attempted
            bite.precision,     // the required accuracy
            yscal,              // scaling value
            &phi_hdone,         // stepsize actually accomplished
            &phi_hnext);        // estimated next stepsize
    }
    else
    {
        // Perform one step in the numerical integration.  Stepsize is fixed
        // and not adjusted by error in result.
        double dummy_err;
        result = Step_CKRK5 (
            slice->norm_pressure[istep], // dependant variable
            dsdphi,             // derivative at starting point
            slice->phi[istep], // starting value for independant variable
            phi_htry,           // stepsize to be attempted
            slice->norm_pressure[istep], // output variable
            dummy_err);        // error term
        phi_hdone = phi_htry;
        phi_hnext = phi_htry;
        slice->phi[istep] += phi_htry;
    }

    if ( false == result )
    {
        // Numerical step has failed.  Dump the context so we can get
        // some idea what is going on.
        EMSG << "Step_Side(): Dumping context"
             << END_OF_MESSAGE;
        printf("istep                       = %d\n", istep);
        printf("slice->norm_pressure[istep] = %g\n", slice->norm_pressure[istep]);
        printf("dsdphi                      = %g\n", dsdphi);
        printf("slice->phi[istep]           = %g\n", slice->phi[istep]);
        printf("phi_htry                    = %g\n", phi_htry);
        printf("bite.precision              = %g\n", bite.precision);
        printf("yscal                       = %g\n", yscal);
        Dump(1);
        return false;
    }

    // Limit the maximum size of the next step
    if ( fabs(phi_hnext) > bite.phi_entry/bite.min_slices )
    {
        phi_hnext = (fabs(phi_hnext)/phi_hnext)*bite.phi_entry/bite.min_slices;
    }

    // Calculate the tangential pressure distribution
    // Observe mixed boundary condition
    slice->tang_pressure[istep] = Tangent_Pressure(
                                        bite.friction_coeff,
                                        slice->norm_pressure[istep],
                                        slice->flows[istep]/2.0);
    
    // Calculate the bite thickness
    slice->bite_thick[istep] = Bite_Thickness(
                                        slice->phi[istep],// exit angle
                                        bite.wrdeform/2.0,     // deformed roll radius
                                        bite.min_thk      // exit thickness
                                        );
    
    // Calculate the instantaneous strain rate
    if ( bite.dbg_strain )
    {
        slice->strain_rate[istep] = Instantaneous_Strain_Rate(
                                        slice->phi[istep],// exit angle
                                        bite.wrdeform/2.0, // deformed roll radius
                                        bite.wrdiam/2.0,   // un-deformed roll radius
                                        bite.min_thk,     // exit thickness
                                        bite.vroll         // roll surface speed
                                        );
    }

    return true;

} // cRollbite::Step_Side


//------------------------------------------------------------------------
// Calculate the tangential pressure observing mixed boundary condition.
//------------------------------------------------------------------------
double   cRollbite::Tangent_Pressure(
                    double       mu,             // coefficient of friction
                    double       s,              // normal pressure
                    double       k               // yield shear stress
                                  )             // returns tangential pressure
{
    double   tang_pressure;

    // Calculate the tangential pressure distribution
    // Observe mixed boundary condition
    tang_pressure = mu*s;
    if ( tang_pressure > k )
    {
        tang_pressure = k;
    }

    return tang_pressure;

} // cRollbite::Tangent_Pressure
    

//------------------------------------------------------------------------
// Calculate the entry angle.
//------------------------------------------------------------------------
double  cRollbite::Entry_Angle(
                        double  diameter,       // deformed roll diameter
                        double  exit_thk,       // exit thickness
                        double  entry_thk       // entry thickness
                              )         // returns the entry angle
{
    return acos(1.0F - (entry_thk - exit_thk)/diameter);
}

//------------------------------------------------------------------------
// Calculate the bite thickness at angle phi.
//------------------------------------------------------------------------
double   cRollbite::Bite_Thickness(
                    double       phi,        // exit angle
                    double       radius,     // deformed roll radius
                    double       exit_thk    // exit thickness
                    ) // returns the bit thickness
{

    return exit_thk + 2.0*radius*(1 - cos(phi));

} // cRollbite::Bite_Thickness


//------------------------------------------------------------------------
// Calculate the instantaneous strain rate at angle phi.
//------------------------------------------------------------------------
double   cRollbite::Instantaneous_Strain_Rate(
                    double       phi,                // exit angle
                    double       deform_radius,      // deformed roll radius [minor_length]
                    double       radius,             // un-deformed roll radius [minor_length]
                    double       exit_thk,           // exit thickness [minor_length]
                    double       vroll               // roll surface speed [major_length/sec]
                    ) // returns the instantaneous strain rate [1/sec]
{
    double   strain_rate;
    float   rollsp (vroll * Physcon.mmpm_inpft);
                               // roll surface speed  [minor_length] / [sec]

    strain_rate = 2.0*deform_radius*rollsp*sin(phi) /
                  ( radius*(exit_thk + 2.0*deform_radius*(1 - cos(phi))) );

    return strain_rate;

} // cRollbite::Instantaneous_Strain_Rate

//-------------------------------------------------------------------
// Calculate the Neutral angle by Sims formula
//-------------------------------------------------------------------
double  cRollbite::Neutral_Sims(
            double   wrdeform,      // IN  deformed roll diameter
            double   exit_thk,      // IN  exit thickness
            double   entry_thk,     // IN  entry thickness
            double   exit_tension,  // IN  exit tension
            double   entry_tension, // IN  entry tension
            double   flows_mean)    // IN  mean flow stress
{
    double  phi_neut;
    double  phi_entry;
    double  term1;
    double  term2;
    double  term3;
    double  term4;
    double  term5;

    phi_entry = Entry_Angle(
                    wrdeform,      // IN  deformed roll diameter
                    exit_thk,      // IN  exit thickness
                    entry_thk);    // IN  entry thickness

    //--------------------------------------------
    // calculate the terms in Sims' expression.
    //--------------------------------------------
    term1 = sqrt((wrdeform/2.0) / exit_thk);
    term2 = (Physcon.pi / 4.0) * log(exit_thk / entry_thk);
    term3 = term1 * atan(term1 * phi_entry);
    if ( flows_mean > 0.0 )
    {
        term4 = (exit_tension - entry_tension) / flows_mean;
    }
    else
    {
        term4 = 0.0;
    }
    term5 = tan((term2 + term3 + term4) / (2.0 * term1));

    phi_neut = (term5 / term1); // [radians]

    return phi_neut;

} // cRollbite::Neutral_Sims


//---------------------------------------------------------------------------
// ELASTIC_THICKNESS ABSTRACT:
//     Elastic_Thickness calculates the entry and exit thickness
//     of the strip under tension,  and the minimum gap thickness.
//     Using the formulas of Bland and Ford.
//---------------------------------------------------------------------------
void cRollbite::Elastic_Thickness (
                double *entry_str,     // OUT entry thickness, stretched 
                                      //        [minor_length]
                double *exit_str,      // OUT exit thickness, stretched
                                      //        [minor_length]
                double *min_thk,       // OUT minimum gap thickness
                                      //        [minor_length]
          const double entry_thk,      // IN entry thickness, no tension
                                      //        [minor_length]
          const double exit_thk,       // IN exit thickness, no tension
                                      //        [minor_length]
          const double entry_tension,  // IN external tension, entry [pressure]
          const double exit_tension,   // IN external tension, exit [pressure]
          const double poisson_ratio,  // IN strip poisson ratio [-]
          const double strip_modulus,  // IN elastic modulus of strip [pressure]
          const double exit_flow )     // IN strip flow stress, exit [pressure]
{
    MDSVERIFYNAME(entry_str,"entry_str")
    MDSVERIFYNAME(exit_str,"exit_str")
    MDSVERIFYNAME(min_thk,"min_thk")

    double he;    // temporary variable, stretched thickness, entry side
    double hx;    // temporary variable, stretched thickness, exit side
    double hm;    // minimum gap thickness
    double temp;  // poisson ratio term

    //-----------------------------------------------------------
    // calculate thickness under tension on exit side of stand.
    //-----------------------------------------------------------
    temp = poisson_ratio * ( 1.0 + poisson_ratio );
    hx   = exit_thk * ( 1.0 - (temp * exit_tension / strip_modulus));

    //-------------------------------------------------------------
    // calculate thickness under tension on entry side of stand.
    //-------------------------------------------------------------
    he = entry_thk * ( 1.0 - (temp * entry_tension / strip_modulus));

    //-----------------------------------
    // calculate minimum gap thickness
    //-----------------------------------
    temp = (1.0 - ( poisson_ratio * poisson_ratio ));
    hm   = hx - ( exit_thk * temp * (exit_flow - exit_tension)/ strip_modulus);

    //----------------------
    // set out parameters
    //----------------------
    *entry_str = he;   // entry thickness under tension  [minor_length]
    *exit_str  = hx;   // exit thickness under tension   [minor_length]
    *min_thk   = hm;   // minimum gap between work rolls [minor_length]
        
    return;
} // end elastic_thickness


//--------------------------------------------------------------------------
// Elastic_Force_Tension Abstract:
//     This procedure can be used for any force model.  It calculates the
//     force contributions of the entry and exit zones,  the effective
//     tension, and the minimum gap, using the Bland and Ford model.
//  
//     This procedure estimates the roll force contributions
//     attributable to the zones of elastic deformation at
//     entry and exit.  The force contributions and the
//     modified tensions applicable to the zone of plastic
//     deformation are calculated according to the equations
//     of Ford, Ellis and Bland.
//  
//     This procedure can be used for any force theory where
//     the tangential force in the elastic area can be
//     assumed to be slipping friction. ( this assumption
//     only applies to the tension modification ).
//  
//     If there is no permanent deformation,  elastic effects
//     cannot be calculated by this method.
//  
//--------------------------------------------------------------------------- 
void cRollbite::Elastic_Force_Tension (
                status_type *status,         // OUT 
                double       *ten1,           // OUT entry tension to plastic
                                             //         zone [pressure]
                double       *ten2,           // OUT exit tension to plastic zone
                                             //         [pressure]
                double       *pe1,            // OUT elastic force/width, entry 
                                             //         [minor_force]/
                                             //         [minor_length]
                double       *pe2,            // OUT elastic force/width, exit 
                                             //         [minor_force]/
                                             //         [minor_length]
          const double       defrad,          // IN deformed roll radius
                                             //         [minor_length]
          const double       entryh,          // IN entry thickness [minor_length]
          const double       exith,           // IN exit thickness [minor_length]
          const double       entry_ten,       // IN external tension - entry
                                             //         [pressure]
          const double       exit_ten,        // IN external tension - exit
                                             //         [pressure]
          const double       entryy,          // IN yield stress at entry
                                             //         [pressure]
          const double       exity,           // IN yield stress at exit
                                             //         [pressure]
          const double       friction_coeff,  // IN coefficient of friction [-]
          const double       poisson_ratio,   // IN strip poisson ratio [-]
          const double       strip_modulus,   // IN strip modulus of elasticity
                                             //         [pressure]
          const double       entryh_e,        // IN elastic entry thickness [minor_length]
          const double       exith_e,         // IN elastic exit thickness [minor_length]
          const double       h_min )          // IN minimum thickness [minor length]
{
    MDSVERIFYNAME(status,"status")
    MDSVERIFYNAME(ten1,"ten1")
    MDSVERIFYNAME(ten2,"ten2")
    MDSVERIFYNAME(pe1,"pe1")
    MDSVERIFYNAME(pe2,"pe2")

          int   j1, j2;     // loop indexes
          double temp1;     // temporary variables
          double temp2;     // temporary variables
          double temp3;     // temporary variables
          double temp4;     // temporary variables
          double y;         // estimated vertical stress at boundary between
                            //     elastic and plastic region, given by
                            //     yield_stress - effective_tension [pressure]
          double eten1;     // effective tension applied to the plastic zone 
                            //     [pressure]
          double eten2 = 0.0;// effective tension applied to the plastic zone 
                            //     [pressure]
          double p1 = 0.0;  // internal variables for elastic force 
                            //     [minor_force]/[minor_length]
          double p2 = 0.0;  // internal variables for elastic force 
                            //     [minor_force]/[minor_length]
          double p11;       // previous estimate for elastic force 
                            //     [minor_force]/[minor_length]
          double p22;       // previous estimate for elastic force 
                            //     [minor_force]/[minor_length]
          double draft (entryh_e - h_min);  // [minor_length]
    const int   loop_count1 (30);   // iteration limit, entry side
    const int   loop_count2 (30);   // iteration limit, exit side
    const double cnvlmt (0.001);    // relative convergence limit for successive
                                    //     force estimates

    //-----------------------------------------------------------
    // If there is no permanent deformation,  elastic effects
    // cannot be calculated by this method.
    //-----------------------------------------------------------
    if (draft <= 0.0) 
    {
        *status = rb_valid;  
        *pe1    = 0.0;       // force contrib. from entry elastic zone 
                             //     [minor_force]/[minor_length]
        *pe2    = 0.0;       // force contrib. from exit elastic zone 
                             //     [minor_force]/[minor_length]
        *ten1   = entry_ten; // effective tension is external tension [pressure]
        *ten2   = exit_ten;  // effective tension is external tension [pressure]
        return;
    }
 
    temp1 = (1.0 - (poisson_ratio * poisson_ratio)); // poisson ratio term [-]		泊松比修正项
    eten1 = entry_ten;                 // first estimate of effective tension			有效入口张力
                                       //     [pressure]
    y     = entryy - eten1;            // first estimate of vertical stress 			弹性区与塑性区分界处的垂直应力（entryy - eten1，entryy为入口流动应力 / 屈服强度）
                                       //     [pressure]
    temp3 = sqrt( defrad / draft );    // precalculate term for equation
    temp4 = (temp1 * entryh * temp3) / ( 4.0 * strip_modulus );
                                       // precalculate term for equation

    //--------------------------------------------
    // estimate entry elastic zone force/width
    //--------------------------------------------
    p11 = temp4 * y * y;               // [minor_force]/[minor_length]

    //----------------------------------------------------------
    // iterate to refine the estimates for force and tension
    //----------------------------------------------------------
    for (j1 = 0; j1 < loop_count1; j1++) 
    {
        //-----------------------------------------------------
        // calculate entry tension adjusted for elastic zone.
        //-----------------------------------------------------
        eten1 = entry_ten - ((2.0 * friction_coeff * p11) / entryh);
                                                               // [pressure]
        if ( eten1 <= 0.0 )
            eten1 = 0.0;

        //--------------------------------------------------
        // calculate force contribution from elastic zone.
        //--------------------------------------------------
        y  = entryy - eten1; // vertical pressure at elastic zone boundary
                             //     [pressure]
        p1 = temp4 * y * y;  // [minor_force]/[minor_length]

        //-----------------------------------------------
        // if force estimates are close, quit the loop
        //-----------------------------------------------
        if (p1 != 0.0) 
        {
            if ((fabs((p1 - p11) / p1)) < cnvlmt) 
            {
                //------------------------------------------------
                // latest estimate differs little from previous.
                //------------------------------------------------
                break; // exit from for loop
            }
            else
            {
                //------------------------------------------------------
                // If this is the last iteration, and not converged,
                // set status and return
                //------------------------------------------------------
                if( j1 == (loop_count1 - 1))
                {
                    *status = bite.status = rb_non_convergent;
                    return;
                }
                else
                {
                    //------------------------------------------------------
                    // set value for next iteration
                    //------------------------------------------------------
                    p11 = p1; 
                }
            } // end if ((fabs((p1 - p11) / p1)) < cnvlmt)
        } 
        else // force estimate was zero
        {
            break; //  exit from for loop
        } // end if (p1 != 0.0) 
    } // end loop j1
     
    //---------------------------------------------------------------------
    // Now calculate the elastic force and effective tension at exit.
    // The next line appears in Ellis and Ford's text and Alexander's
    // program code and is assumed to be the correct version.
    //----------------------------------------------------------------------
    temp2 = (2.0 / 3.0) * sqrt(defrad * exith * temp1 / strip_modulus);

    //----------------------------------------------------------------------
    // The following are modifications made to Ellis and Ford's equation
    // by Yuen.  At light draft (< 4%), the elastic contribution becomes
    // more significant but is virtually unchanged for large drafts.  By
    // setting temp3 = 0.0 and temp4 = 0.0, the result is the same as Ellis 
    // and Ford.
    //----------------------------------------------------------------------
    temp3 = friction_coeff * poisson_ratio * (1.0 + poisson_ratio) * exith *
            defrad / (2.0 * exith_e * strip_modulus);
    temp4 = 4.0 * friction_coeff * friction_coeff * poisson_ratio *
            poisson_ratio * (1.0 + poisson_ratio * poisson_ratio) * defrad *
            exith * sqrt(defrad * exith) /
            (15.0 * exith_e * exith_e * strip_modulus * 
             sqrt(strip_modulus * temp1));

    //----------------------------------------------------------------------
    // Obtain first estimate of force and tension from exit zone.
    //----------------------------------------------------------------------
    if (exity > exit_ten)
    {
        y   = exity - exit_ten;       // vertical pressure at plastic zone
        p22 = temp2 * y * sqrt(y) +
              temp3 * y * y +
              temp4 * y * y * sqrt(y);  // force/unit width
                                        //     [minor_force]/[minor_length]
    }
    else
    {
        //------------------------------------------------
        // error, tension cannot exceed yield stress !!
        //------------------------------------------------
        *status = bite.status = rb_invalid_exit_tension;
        return;

    } // end if
 
    for (j2 = 0; j2 < loop_count2; j2++) 
    {
        //-------------------------------------------------------------------
        // calculate exit tension adjusted for elastic zone.
        // ( effective tension applied to plastic deformation zone )
        //-------------------------------------------------------------------
        eten2 = exit_ten - ((2.0 * friction_coeff * p22) / exith); // [pressure]
        if ( eten2 <= 0.0 )
            eten2 = 0.0;

        //-------------------------------------------------------------------
        // The effective tension applied to the plastic deformation
        // zone will be less than the external tension and
        // may be positive ( a compression ).  In some cases, the
        // compression may become very large and cause the
        // Bland and Ford calculation to overflow.  In these
        // cases, a check on the size of the effective tension
        // should be implemented here.
        //-------------------------------------------------------------------
        // re-calculate force contribution from elastic zone
        //--------------------------------------------------------------------
        y = exity - eten2;  // vertical stress at plastic zone boundary
                            //     [pressure]
        if (y > 0.0 )
        {
            p2 = temp2 * y * sqrt(y) +
                 temp3 * y * y +
                 temp4 * y * y * sqrt(y);   // [minor_force]/[minor_length]
        }
        else
        {
            //---------------------------------------------------
            // error,  exit tension cannot exceed yield stress
            //---------------------------------------------------
            *status = bite.status = rb_invalid_exit_tension;
	        return;

        } // end if

        if (p2 != 0.0 )
        {
            if ((fabs((p2 - p22) / p2)) < cnvlmt) 
            {
              break; // exit from for loop
            }
            else
            {
                //------------------------------------------------------
                // If this is the last iteration, and not converged,
                // set status and return
                //------------------------------------------------------
                if( j2 == (loop_count2 - 1))
                {
                    *status = rb_non_convergent;
                    return;
                }
                else
                {
                    //------------------------------------------------------
                    // set value for next iteration
                    //------------------------------------------------------
                    p22 = p2; 
                }
            } // end if ((fabs((p2 - p22) / p2)) < cnvlmt) 
        }
        else
        {
            break; // exit from for loop zero force estimate
        } // end if (p2 != 0.0 )
    } // end loop j2
     
    //-----------------------
    // set return variables
    //-----------------------
    *status = rb_valid;
    *pe1    = p1;    // force contribution from entry elastic zone/width 
                     //     [minor_force]/[minor_length]
    *pe2    = p2;    // force contribution from exit elastic zone/width
                     //     [minor_force]/[minor_length]
    *ten1   = eten1; // effective tension applied to plastic zone
                     //     [pressure]
    *ten2   = eten2; // effective tension applied to plastic zone
                     //     [pressure]
    return;
        
} // end elastic_force_tension


//--------------------------------------------------------------------------
// Calculate g1(phi)
//--------------------------------------------------------------------------
double   cRollbite::Calc_G1_Phi(
                bool    exit,           // IN  [-] true of on exit side of neutral point
                double   phi,            // IN  [-] angle phi from exit plane 
                double   frict_coeff,    // IN  [-] coefficient of friction
                double   wrdeform,       // IN  [minor_length] deformed roll diameter
                double   bite_thick)     // IN  [minor_length] thickness of element of strip
{
    double   g1;
    double   x1;
    double   x2;
    double   sec_phi = 1.0F/cos(phi);

    x1 = frict_coeff*sec_phi*(wrdeform/bite_thick+sec_phi);
    if ( exit )
    {
        x2 = 1.0F - frict_coeff*tan(phi);
        g1 = x1 / x2;
    }
    else
    {
        x2 = 1 + frict_coeff*tan(phi);
        g1 = (-x1) / x2;
    }

    return g1;

} // cRollbite::Calc_G1_Phi

    
//--------------------------------------------------------------------------
// Calculate g2(phi)
//--------------------------------------------------------------------------
double   cRollbite::Calc_G2_Phi(
                bool    exit,           // IN  [-] true of on exit side of neutral point
                double   phi,            // IN  [-] angle phi from exit plane 
                double   frict_coeff,    // IN  [-] coefficient of friction
                double   wrdeform,       // IN  [minor_length] deformed roll diameter
                double   bite_thick,     // IN  [minor_length] thickness of element of strip
                double   flow_stress,    // IN  [-] flow stress of the element
                double   dflow_stress)   // IN  [-] differential of flow stress of the element
{
    double   g2;
    double   x1;
    double   x2;

    x1 = wrdeform*flow_stress*sin(phi)/bite_thick + dflow_stress;

    if ( exit )
    {
        x2 = 1.0F - frict_coeff*tan(phi);
    }
    else
    {
        x2 = 1.0F + frict_coeff*tan(phi);
    }

    g2 = x1 / x2;

    return g2;

} // cRollbite::Calc_G2_Phi


//--------------------------------------------------------------------------
// Calculate g3(phi)
//--------------------------------------------------------------------------
double   cRollbite::Calc_G3_Phi(
                bool    exit,           // IN  [-] true of on exit side of neutral point
                double   phi,            // IN  [-] angle phi from exit plane 
                double   wrdeform,       // IN  [minor_length] deformed roll diameter
                double   bite_thick,     // IN  [minor_length] thickness of element of strip
                double   flow_stress,    // IN  [-] flow stress of the element
                double   dflow_stress)   // IN  [-] differential of flow stress of the element
{
    double   g3;
    double   x1;
    double   x2;
    double   x3;
    double   cos_phi = cos(phi);

    x1 = 0.5*wrdeform*cos_phi/bite_thick + (0.5F / (cos_phi*cos_phi));
    if ( exit )
    {
        x2 = 1.0F + 0.5F*tan(phi);
    }
    else
    {
        x2 = 1.0F - 0.5F*tan(phi);
        x1 = (-x1);
    }
    x3 = wrdeform*sin(phi)/bite_thick;

    g3 = flow_stress*(x3*x2+x1) + x2*dflow_stress;

    return g3;

} // cRollbite::Calc_G3_Phi


//--------------------------------------------------------------------------
// Calculate ds/dphi considering sliding and sticking in the roll bite
//
// From analysis:
//
// ds/dphi = g1(phi)*s + g2(phi)    for sliding friction
//
// ds/dphi = g3(phi)                for sticking friction
//
//--------------------------------------------------------------------------
double   cRollbite::Calc_Ds_Dphi(
                bool    exit,           // IN  [-] true of on exit side of neutral point
                double   s,              // IN  [-] normal pressure at angle phi
                double   phi,            // IN  [-] angle phi from exit plane 
                double   frict_coeff,    // IN  [-] coefficient of friction
                double   wrdeform,       // IN  [minor_length] deformed roll diameter
                double   bite_thick,     // IN  [minor_length] thickness of element of strip
                double   flow_stress,    // IN  [-] flow stress of the element
                double   dflow_stress)   // IN  [-] differential of flow stress of the element
{
    double   ds;

    if ( (frict_coeff * s) < (flow_stress / 2.0F) )
    {
        // sliding friction in the roll bite
        double   g1;
        double   g2;

        g1 = Calc_G1_Phi(
                    exit,           // IN  [-] true of on exit side of neutral point
                    phi,            // IN  [-] angle phi from exit plane 
                    frict_coeff,    // IN  [-] coefficient of friction
                    wrdeform,       // IN  [minor_length] deformed roll diameter
                    bite_thick);    // IN  [minor_length] thickness of element of strip

        g2 = Calc_G2_Phi(
                    exit,           // IN  [-] true of on exit side of neutral point
                    phi,            // IN  [-] angle phi from exit plane 
                    frict_coeff,    // IN  [-] coefficient of friction
                    wrdeform,       // IN  [minor_length] deformed roll diameter
                    bite_thick,     // IN  [minor_length] thickness of element of strip
                    flow_stress,    // IN  [-] flow stress of the element
                    dflow_stress);  // IN  [-] differential of flow stress of the element

        ds = g1 * s + g2;
    }
    else
    {
        // sticking friction in the roll bite
        double   g3;

        g3 = Calc_G3_Phi(
                    exit,           // IN  [-] true of on exit side of neutral point
                    phi,            // IN  [-] angle phi from exit plane 
                    wrdeform,       // IN  [minor_length] deformed roll diameter
                    bite_thick,     // IN  [minor_length] thickness of element of strip
                    flow_stress,    // IN  [-] flow stress of the element
                    dflow_stress);  // IN  [-] differential of flow stress of the element
    
        ds = g3;
    }

    return ds;

} // cRollbite::Calc_Ds_Dphi


//--------------------------------------------------------------------------
// Calculate the RHS derivative for the numerical integration methods.
//
// Marshalls parameters and calls cRollbite::Calc_Ds_Dphi()
//
//--------------------------------------------------------------------------
/*
void   cRollbite::Derivs(
                double   phi,            // IN  [-] angle phi from exit plane 
                double&  s,              // IN  [-] normal pressure at angle phi
                double&  dsdphi)         // OUT [-] derivative of normal pressure at angle phi
{
    double   bite_thick = bite.min_thk + bite.wrdeform * (1.0F - cos(phi));

    dsdphi = Calc_Ds_Dphi(
                bite.exit_side,      // IN  [-] true on exit side of neutral point, actually the driver
                                    //         routine integrates past the neutral point from each
                                    //         direction, setting exit to true if stepping from exit
                                    //         to entry side, else false.
                s,                  // IN  [-] normal pressure at angle phi
                phi,                // IN  [-] angle phi from exit plane 
                bite.friction_coeff, // IN  [-] coefficient of friction
                bite.wrdeform,       // IN  [minor_length] deformed roll diameter
                bite_thick,         // IN  [minor_length] thickness of element of strip
                bite.flows_mean,     // IN  [-] flow stress of the element
                0.0);               // IN  [-] differential of flow stress of the element //??????KGM

    return;
} // cRollbite::Derivs
*/

//--------------------------------------------------------------------------
// Calculate the RHS derivative for the numerical integration methods.
//
// Marshalls parameters and calls cRollbite::Calc_Ds_Dphi()
//
// This alternate version incoporporates the called functions,
// to eliminate function calls and particularly repreated
// trigonometric function evaulations.
//--------------------------------------------------------------------------
// Calculate ds/dphi considering sliding and sticking in the roll bite
//
// From analysis:
//
// ds/dphi = g1(phi)*s + g2(phi)    for sliding friction
//
// ds/dphi = g3(phi)                for sticking friction
//
//--------------------------------------------------------------------------
void   cRollbite::Derivs(
                double   phi,            // IN  [-] angle phi from exit plane 
                double&  s,              // IN  [-] normal pressure at angle phi
                double&  dsdphi)         // OUT [-] derivative of normal pressure at angle phi
{
        
    double cos_phi;
    double sin_phi;
    double sec_phi;
    double tan_phi;
    if ( bite.precise_derivs )
    {
        cos_phi = cos(phi);
        sin_phi = sin(phi);
    }
    else
    {
        cos_phi = 1.0 - phi*phi/2.0;
        sin_phi = phi*(1 - phi*phi/6.0);
    }
    sec_phi = 1.0/cos_phi;
    tan_phi = sin_phi/cos_phi;
    double   bite_thick = bite.min_thk + bite.wrdeform * (1.0 - cos_phi);
    double   wronh = bite.wrdeform / bite_thick ;

    if ( (bite.friction_coeff * s) < (bite.flows_mean / 2.0) )
    {
        // sliding friction in the roll bite
        double   g1;
        double   g2;

        double x1 = bite.friction_coeff*sec_phi*
                         (wronh+sec_phi);

        double fric_tan = bite.friction_coeff * tan_phi ;

        if ( bite.exit_side )
        {
            g1 = x1 / ( 1.0 - fric_tan ) ;
        }
        else
        {
            g1 = (-x1) / ( 1.0 + fric_tan ) ;
        }

        // Calculate G2

        x1 = (wronh * bite.flows_mean * sin_phi)  ;

        if ( bite.exit_side )
        {
            g2 = x1 / ( 1.0 - fric_tan ) ;
        }
        else
        {
            g2 = x1 / ( 1.0 + fric_tan ) ;
        }

        dsdphi = g1 * s + g2;
    }
    else
    {
        // sticking friction in the roll bite

        double x1 = (0.5 * ((wronh*cos_phi) + (1.0/(cos_phi*cos_phi)) ) );
        double x3 = wronh*sin_phi ;
        
        if ( bite.exit_side )
        {
            dsdphi = bite.flows_mean*((x3*(1.0+(0.5*tan_phi)))+x1) ;
        }
        else
        {
            dsdphi = bite.flows_mean*((x3*(1.0-(0.5*tan_phi)))-x1) ;
        }

    }

    return;
} // cRollbite::Derivs


//----------------------------------------------------------------------------
// Allocate_Distribution() ABSTRACT
//
// Allocate or resize a roll bite distribution.  If more slices are requested
// than currently available, then allocate a new structure to hold the number
// of slices desired, copy the old structure and then delete it.  If there are
// already enough slices defined, then do nothing. 
//
//----------------------------------------------------------------------------
void    cRollbite::Allocate_Distribution(slice_type *bite, int nslices)
{
    bool    allocate_done = (bite->max_slices > 0);

    if ( bite != NULL && nslices > bite->max_slices )
    {
        int         i;
        slice_type  new_bite;

        new_bite.phi           = new double [nslices];
        new_bite.flows         = new double [nslices];
        new_bite.bite_thick    = new double [nslices];
        new_bite.strain_rate   = new double [nslices];
        new_bite.strip_speed   = new double [nslices];
        new_bite.tang_pressure = new double [nslices];
        new_bite.norm_pressure = new double [nslices];
        new_bite.temp_mean     = new double [nslices];
        new_bite.temp_surf     = new double [nslices];
        new_bite.max_slices    = nslices;
        new_bite.num_slices    = bite->num_slices;
        for( i=0; i<nslices; i++ )
        {
            if ( i < new_bite.num_slices )
            {
                // Copy distributions across
                new_bite.phi[i]           = bite->phi[i];
                new_bite.flows[i]         = bite->flows[i];
                new_bite.bite_thick[i]    = bite->bite_thick[i];
                new_bite.strain_rate[i]   = bite->strain_rate[i];
                new_bite.strip_speed[i]   = bite->strip_speed[i];
                new_bite.tang_pressure[i] = bite->tang_pressure[i];
                new_bite.norm_pressure[i] = bite->norm_pressure[i];
                new_bite.temp_mean[i]     = bite->temp_mean[i];
                new_bite.temp_surf[i]     = bite->temp_surf[i];
            }
            else
            {
                // pad out with zeroes
                new_bite.phi[i]           = 0.0;
                new_bite.flows[i]         = 0.0;
                new_bite.bite_thick[i]    = 0.0;
                new_bite.strain_rate[i]   = 0.0;
                new_bite.strip_speed[i]   = 0.0;
                new_bite.tang_pressure[i] = 0.0;
                new_bite.norm_pressure[i] = 0.0;
                new_bite.temp_mean[i]     = 0.0;
                new_bite.temp_surf[i]     = 0.0;
            }
        }
        if ( allocate_done )
        {
            // Now delete the old distributions
            delete bite->phi;
            delete bite->flows;
            delete bite->bite_thick;
            delete bite->strain_rate;
            delete bite->strip_speed;
            delete bite->tang_pressure;
            delete bite->norm_pressure;
            delete bite->temp_mean;
            delete bite->temp_surf;
        }
        // Now assign new bite to original
        *bite = new_bite;
    }
    return;
}


//----------------------------------------------------------------------------
// Delete_Distribution() ABSTRACT
//
// Delete a roll bite distribution. 
//
//----------------------------------------------------------------------------
void    cRollbite::Delete_Distribution(slice_type *bite)
{
    if ( (bite != NULL) && (bite->max_slices > 0) )
    {

        delete [] bite->phi;
        delete [] bite->flows;
        delete [] bite->bite_thick;
        delete [] bite->strain_rate;
        delete [] bite->strip_speed;
        delete [] bite->tang_pressure;
        delete [] bite->norm_pressure;
        delete [] bite->temp_mean;
        delete [] bite->temp_surf;
        bite->num_slices = 0;
        bite->max_slices = 0;
    }
}


//----------------------------------------------------------------------------
// Copy_Distribution() ABSTRACT
//
// Copy a roll bite distribution.  Delete the "to" distribution before
// copying.
//
//----------------------------------------------------------------------------
void    cRollbite::Copy_Distribution(slice_type *to, const slice_type *from)
{
    int     i;

    if ( (NULL == to) || (NULL == from) )
    {
        return;
    }
    Delete_Distribution(to);
    *to = *from;

    if ( to->max_slices > 0 )
    {
        to->phi           = new double [to->max_slices];
        to->flows         = new double [to->max_slices];
        to->bite_thick    = new double [to->max_slices];
        to->strain_rate   = new double [to->max_slices];
        to->strip_speed   = new double [to->max_slices];
        to->tang_pressure = new double [to->max_slices];
        to->norm_pressure = new double [to->max_slices];
        to->temp_mean     = new double [to->max_slices];
        to->temp_surf     = new double [to->max_slices];
        for( i=0; i<to->max_slices; i++ )
        {
            if ( i < to->num_slices )
            {
                // Copy distributions across
                to->phi[i]           = from->phi[i];
                to->flows[i]         = from->flows[i];
                to->bite_thick[i]    = from->bite_thick[i];
                to->strain_rate[i]   = from->strain_rate[i];
                to->strip_speed[i]   = from->strip_speed[i];
                to->tang_pressure[i] = from->tang_pressure[i];
                to->norm_pressure[i] = from->norm_pressure[i];
                to->temp_mean[i]     = from->temp_mean[i];
                to->temp_surf[i]     = from->temp_surf[i];
            }
            else
            {
                // pad out with zeroes
                to->phi[i]            = 0.0;
                to->flows[i]          = 0.0;
                to->bite_thick[i]     = 0.0;
                to->strain_rate[i]    = 0.0;
                to->strip_speed[i]    = 0.0;
                to->tang_pressure[i]  = 0.0;
                to->norm_pressure[i]  = 0.0;
                to->temp_mean[i]      = 0.0;
                to->temp_surf[i]      = 0.0;
            }
        }
    }
    return;
}


//--------------------------------------------------------------------------
// Step_CKRK5_Adaptive() ABSTRACT
//
// Perform a single step using the C-K RK5 algorithm with adaptive step size
// adjustment.  If a step is taken successfully returns TRUE, else false.
//
//--------------------------------------------------------------------------
#define SAFETY_FACTOR   (0.9)       // safety margin for stepsize
#define PGROW           (-0.2)      // exponent for growing the stepsize
#define PSHRNK          (-0.25)     // exponent for shrinking the stepsize
#define ERRCON          (1.89e-4)   // (5/SAFETY_FACTOR raised to power 1/PGROW)
bool    cRollbite::Step_CKRK5_Adaptive (
            double&  y,          // dependant variables y[1..n]
            double&  dydx,       // derivative at starting point
            double   *x,         // starting value for independant variable
            double   htry,       // stepsize to be attempted
            double   eps,        // the required accuracy
            double&  yscal,      // scaling vector
            double   *hdid,      // stepsize actually accomplished
            double   *hnext      // estimated next stepsize
            )
{
    double errmax,h,htemp,xnew,yerr,ytemp;

    h = htry;
    for (;;)
    {
        // Take a step
        Step_CKRK5(y, dydx, *x, h, ytemp, yerr);
        // Evaluate the accuracy
        errmax = fabs(yerr / yscal) / eps;
        if (errmax <= 1.0)
        {
            // Step was OK.  Compute size of next step
            break;
        }
        // Error is too large, reduce the stepsize and try again
        htemp = SAFETY_FACTOR * h * pow(errmax,PSHRNK);
        // Don't change stepsize more than a factor of 10
        h = ( (h >= 0.0) ? cMathUty::Max(htemp,0.1 * h) : cMathUty::Min(htemp,0.1 * h));
        // Guard against round off error
        xnew = (*x)+h;
        if (xnew == *x)
        {
            EMSG << "Step_CKRK5_Adaptive(): Stepsize underflow"
                 << END_OF_MESSAGE;
            return false;
        }
    }
    if (errmax > ERRCON)
    {
        // grow the stepsize for the next step
        *hnext = SAFETY_FACTOR * h * pow(errmax,PGROW);
    }
    else
    {
        // no more than a factor of 5 increase
        *hnext = 5.0 * h;
    }
    // compute the next value of independent variable and next step
    *x += (*hdid = h);
    // assign the array of independant variables
    y = ytemp;

    return true;

}


//--------------------------------------------------------------------------
// Step_CKRK5() ABSTRACT
//
// Perform a single step using the Cash-Karp (C-K) 5th Order Runge Kutta
// algorithm.  Uses fixed step size.  If a step is taken successfully
// returns TRUE, else false.
//
// This method is known as an embedded Runge-Kutta method, since it embeds
// a 4th order Runge-Kutta method within the 5th order method.  In this
// method, 6 derivative evaluations are required, verses 11 to get the
// equivalent 5th order result from a step doubling method based on the
// traditional 4th order Runge Kutta algorithm, in other words it is almost
// twice as efficient as the traditional algorithm.
//
// The embedded 4th order method gives us a way of calculating the error
// bounds.
//
// The general form of a 5th order Runge-Kutta formula is:
// =======================================================
//
// k1 = h*f(xn, yn)
//
// k2 = h*f(xn+a2*h, yn+b21*k1)
//
// k3 = h*f(xn+a3*h, yn+b31*k1+b32*k2)
//
// k4 = h*f(xn+a4*h, yn+b41*k1+b42*k2+b43*k3)
//
// k5 = h*f(xn+a5*h, yn+b51*k1+b52*k2+b53*k3+b54*k4)
//
// k6 = h*f(xn+a6*h, yn+b61*k1+b62*k2+b63*k3+b64*k4+b65*k5)
//
// yn+1 = yn + c1*k1 + c2*k2 + c3*k3 + c4*k4 + c5*k5 + c6*k6 + O(h^6)
//
//
// The embedded 4th order formula is:
// ==================================
//
// y'n+1 = y'n + c1'*k1 + c2'*k2 + c3'*k3 + c4'*k4 + c5'*k5 + c6'*k6 + O(h^5)
//
//
// The error estimate is:
// ======================
//
// yerr = yn+1 - y'n+1
//
//
// For further details refer to the section on "Solving Von Karman's
// Differential Equation" in the "Rollbite Class Functional Design Document".
//
//--------------------------------------------------------------------------
bool    cRollbite::Step_CKRK5 (
            double&  y,          // IN/OUT dependant variable y
            double&  dydx,       // IN/OUT derivative dydx at starting point
            double   x,          // IN starting value for independant variable
            double   h,          // IN stepsize
            double&  yout,       // OUT y after the step
            double&  yerr        // OUT estimate of the truncation error
            )
{
    //----------------------------
    // static Cash-Karp constants
    //----------------------------
    static  double a2  = 0.2, a3  = 0.3,      a4  = 0.6, a5 = 1.0, a6 = 0.875;
    static  double b21 = 0.2, b31 = 3.0/40.0, b32 = 9.0/40.0;
    static  double b41 = 0.3, b42 = -0.9,     b43 = 1.2;
    static  double b51 = -11.0/54.0,       b52 = 2.5,         b53 = -70.0/27.0;
    static  double b54 = 35.0/27.0;
    static  double b61 = 1631.0/55296.0,   b62 = 175.0/512.0, b63 = 575.0/13824.0,
                   b64 = 44275.0/110592.0, b65 = 253.0/4096.0;
    static  double c1  = 37.0/378.0,       c3  = 250.0/621.0, c4  = 125.0/594.0,
                   c6  = 512.0/1771.0;
    static  double dc5 = -277.00/14336.0;

    double  dc1 = c1-2825.0/27648.0,  dc3 = c3-18575.0/48384.0,
            dc4 = c4-13525.0/55296.0, dc6 = c6-0.25;
    double  ak2,ak3,ak4,ak5,ak6,ytemp;

    ytemp = y + b21 * h * dydx;
    this->Derivs(x+a2*h, ytemp, ak2);

    ytemp = y + h * (b31 * dydx + b32 * ak2);
    this->Derivs(x+a3*h, ytemp, ak3);

    ytemp = y + h * (b41 * dydx + b42*ak2 + b43 * ak3);
    this->Derivs(x+a4*h, ytemp, ak4);

    ytemp = y + h * (b51 * dydx + b52 * ak2 + b53 * ak3 + b54 * ak4);
    this->Derivs(x+a5*h, ytemp, ak5);

    ytemp = y + h * (b61 * dydx + b62 * ak2 + b63 * ak3 + b64 * ak4 + b65 * ak5);
    this->Derivs(x+a6*h,ytemp,ak6);

    //--------------------------------------------------------
    // Combine steps with proper weights for 5th order result.
    //--------------------------------------------------------
    yout = y + h * (c1 * dydx + c3 * ak3 + c4 * ak4 + c6 * ak6);

    if ( bite.err_bounds )
    {
        //----------------------------------------------------------------
        // Estimate error as difference between 4th and 5th order methods.
        //----------------------------------------------------------------
        yerr = h * (dc1 * dydx + dc3 * ak3 + dc4 * ak4 + dc5 * ak5 + dc6 * ak6);
    }
    else
    {
        yerr = 0;
    }

    return true;
}
