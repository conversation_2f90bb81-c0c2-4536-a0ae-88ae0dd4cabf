class ESUStatus:
    """ESU状态常量定义"""
    EL_UNDERDRAFT = 'el_underdraft'  # 欠载状态
    EL_OVERDRAFT = 'el_overdraft'    # 过载状态
    EL_NOLIM = 'el_nolim'           # 无限制状态
    EL_DRAFTMAX = 'el_draftmax'      # 最大轧制量限制
    EL_DRAFTMIN = 'el_draftmin'      # 最小轧制量限制
    EL_FRCMAX = 'el_frcmax'         # 最大力限制

class ESUD:
    def __init__(self):
        self.obj_chain = None
        self.pcESU = None
        self.esu_status = ESUStatus.EL_NOLIM

    def set_esu_status(self, sched, wid_error):
        """
        设置ESU状态

        Args:
            sched: 调度对象
            wid_error: 需要消除的宽度误差

        该方法主要完成：
        1. 检查虚拟轧边机状态
        2. 计算总轧制量和限制值
        3. 检查各道次的限制条件
        4. 设置全局ESU状态
        """
        # 获取主体段索引
        iseg = self.obj_chain.body_index()
        
        # 初始化累计值
        draft = 0.0     # 实际轧制量
        draft_hi = 0.0  # 最大轧制量
        draft_lo = 0.0  # 最小轧制量

        # 检查所有轧边机是否虚拟化
        if sched.pcSetupD.edgs_dummied:
            # 根据宽度误差设置状态
            if wid_error <= (-2.0 * self.pcESU.accuracy):
                # ESU欠载
                self.esu_status = ESUStatus.EL_UNDERDRAFT
            elif wid_error >= (2.0 * self.pcESU.accuracy):
                # ESU过载
                self.esu_status = ESUStatus.EL_OVERDRAFT
            else:
                # ESU无限制
                self.esu_status = ESUStatus.EL_NOLIM
            return

        # 遍历所有道次检查限制条件
        for ps in range(sched.pcSetupD.fstpas, sched.pcSetupD.lstpas + 1):
            # 获取轧边机对象
            edg_d = sched.pcSupPassD[ps].drafting_edg_d(iseg)
            if edg_d is not None:
                # 累计轧制量
                draft += edg_d.draft
                draft_hi += edg_d.draft_max
                draft_lo += edg_d.draft_min

                # 检查轧制量限制
                if edg_d.draft > sched.pcSupPassD[ps].pcPass.vdft_max:
                    # 超过最大轧制量限制
                    edg_d.lim = ESUStatus.EL_DRAFTMAX
                elif edg_d.draft < sched.pcSupPassD[ps].pcPass.vdft_min:
                    # 低于最小轧制量限制
                    edg_d.lim = ESUStatus.EL_DRAFTMIN
                else:
                    # 在正常范围内，清除之前的最大/最小限制标记
                    if (edg_d.lim == ESUStatus.EL_DRAFTMAX or 
                        edg_d.lim == ESUStatus.EL_DRAFTMIN):
                        edg_d.lim = ESUStatus.EL_NOLIM

                # 检查力限制
                if edg_d.force_strip >= edg_d.pcEdg.force_max:
                    # 超过最大力限制
                    edg_d.lim = ESUStatus.EL_FRCMAX

        # 设置全局ESU状态
        if draft >= draft_hi:
            # 过载状态：无法达到目标宽度
            # 实际宽度将大于目标宽度
            self.esu_status = ESUStatus.EL_OVERDRAFT
        elif draft <= draft_lo:
            # 欠载状态：无法达到目标宽度
            # 实际宽度将小于目标宽度
            self.esu_status = ESUStatus.EL_UNDERDRAFT
        else:
            # 正常状态：无限制
            self.esu_status = ESUStatus.EL_NOLIM
