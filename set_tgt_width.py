class ESUD:
    def __init__(self):
        self.obj_chain = None
        self.Physcon = None  # 物理常量对象

    def set_tgt_width(self, sched, sensor_cfg_d, fm_spread):
        """
        计算粗轧出口目标宽度。基于精轧出口目标宽度，考虑温度转换和FM展宽量。

        Args:
            sched: 调度对象，包含轧制计划相关信息
            sensor_cfg_d: 动态公共配置指针
            fm_spread: FM展宽量

        Returns:
            bool: 操作是否成功

        计算公式:
        rmx_width = rmx_exp * fxwaim                 # 基础宽度
                 + htwofc * rmx_exp / fmx_exp        # 温度补偿
                 - wcfh                              # 宽度修正
                 - fm_spread                         # FM展宽补偿
                 + F0_egder net effect              # 边部轧机效应
                 + wfvern * rmx_exp / fmx_exp       # 宽度游标补偿
                 + wrvern                           # 宽度基准补偿
        """
        # 检查出口反馈 - FM入口导板的情况
        if sched.wrk.get("wrk_exit_fbk", 0) > 0:
            print(f"{sched.obj_name()} bypass Set_Tgt_Width, call is after RMX for F0 Edger FFWD")
            return True

        # 区分板材和非板材产品的处理
        if sched.pcPDI.state.product != 'prd_plate':
            print(f"pcSensorCfgD->pcRMXWidthD->targ= {sensor_cfg_d.pcRMXWidthD.targ}")

            # 检查是否发生钢种变化
            if sched.pcRAMP.adapted_state.family_prv != sched.pcRAPP.state.family:
                # 计算目标宽度（考虑钢种变化）
                sensor_cfg_d.pcRMXWidthD.targ = (
                    sensor_cfg_d.pcFMXWidthTargtD.pcPce.fx_hot_width *
                    sensor_cfg_d.pcRMXWidthTargtD.expansion() /
                    sensor_cfg_d.pcFMXWidthTargtD.expansion()
                    - fm_spread
                    + (sched.pcRAMP.state.fmx_wid_vern * 
                       (1 - sched.pcRAPP.state.fwid_off_mult) +
                       sched.pcRAPP.state.fmx_wid_off * 
                       sched.pcRAPP.state.fwid_off_mult) *
                    sensor_cfg_d.pcRMXWidthTargtD.expansion() /
                    sensor_cfg_d.pcFMXWidthTargtD.expansion()
                )
            else:
                # 计算目标宽度（钢种未变化）
                sensor_cfg_d.pcRMXWidthD.targ = (
                    sensor_cfg_d.pcFMXWidthTargtD.pcPce.fx_hot_width *
                    sensor_cfg_d.pcRMXWidthTargtD.expansion() /
                    sensor_cfg_d.pcFMXWidthTargtD.expansion()
                    - fm_spread
                    + sched.pcRAMP.state.fmx_wid_vern *
                    sensor_cfg_d.pcRMXWidthTargtD.expansion() /
                    sensor_cfg_d.pcFMXWidthTargtD.expansion()
                )

            # FM入口导板的处理
            if hasattr(self, 'INCLUDE_FME_EDGER') and self.INCLUDE_FME_EDGER:
                iseg = self.obj_chain.body_index()
                lstpas = sched.pcSetupD.lstpas

                # 计算F0导板的影响
                if sched.pcSupPassD[lstpas].pcExPceD[iseg].expansion() > self.Physcon.tol2:
                    rx_fe_exp_ratio = (
                        sensor_cfg_d.pcRMXWidthTargtD.expansion() /
                        sched.pcSupPassD[lstpas].pcExPceD[iseg].expansion()
                    )

                    if sched.pcSupPassD[lstpas].pcEnEdgD[iseg].draft > self.Physcon.tol2:
                        # 计算净边部效应
                        net_edg_effect = (
                            sched.pcSupPassD[lstpas].pcEnEdgD[iseg].draft -
                            sched.pcSupPassD[lstpas].pcEnEdgD[iseg].pcExPceD.recovery
                        )

                        # 更新RMX目标宽度
                        sensor_cfg_d.pcRMXWidthD.targ += net_edg_effect * rx_fe_exp_ratio

                        # 计算FME目标宽度
                        sched.pcSetupD.fme_tgt_wid = (
                            sensor_cfg_d.pcRMXWidthD.targ / rx_fe_exp_ratio -
                            net_edg_effect
                        )
                    else:
                        # 计算FME目标宽度（无边部效应）
                        sched.pcSetupD.fme_tgt_wid = (
                            sensor_cfg_d.pcRMXWidthD.targ / rx_fe_exp_ratio
                        )
        else:
            # 板材产品的处理
            sensor_cfg_d.pcRMXWidthD.targ = (
                sched.pcSuPce.rx_cold_width *
                sensor_cfg_d.pcRMXWidthTargtD.expansion()
            )

        return True
