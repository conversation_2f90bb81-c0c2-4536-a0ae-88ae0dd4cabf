//-----------------------------------------------------------------------------
//
// ABSTRACT:
//     This file defines the static and dynamic stand object methods.
//
//
//     FUNCTION/PROCEDURE/TASK  DESCRIPTION
//     -----------------------  -----------------------------------------------
//   static
//     cStd                     Stand object constructor
//     cStd                     Stand object copy constructor
//     ~cStd                     Stand object destructor
//     operator=        assignment operator
//     operator==               equivalence operator
//     operator<                less than operator
//     linkObj                  Method for linking stand to other objects
//     dump                     dump member data and/or composed objects
//     Create_Dynamic_Object    Create a new dynamic object.
//   dynamic
//     cStdD                    Stand object constructor
//     cStdD                    Stand object copy constructor
//     ~cStdD                   Stand object destructor
//     operator=        assignment operator
//     operator==               equivalence operator
//     operator<                less than operator
//     linkObj                  Method for linking stand to other objects
//     dump                     dump member data and/or composed objects
//     Post_Config              Virtual function to allow the user to
//                              carry out post processing for a dynamic stand.
//     Create_Dynamic_Object    Create a new dynamic object.
//     rbheat                   Method for calculating roll temperature change
//                              from friction power
//     Assign_State             Assigns state data from a source object to a
//                              destination object of the same type.
//     Operate                  This method updates the exit piece state,
//                              given the entry state information for a piece.
//     Speed                    Calculate the roll peripheral speed and motor
//                              rpm.The user may override this on the derived
//                              stand to suite his specific purposes.
//     Volume_Flow              Calculate the volume flow.  The user may
//                              override this on the derived stand to suite
//                              his specific purposes.
//     Max_Gap                  Calculates the maximum gap openning due to
//                              mechanical limitation and to be used
//                              for dummied stands gap openning.
//
//-----------------------------------------------------------------------------


//---------------------
// system include files
//---------------------
#include <stdlib.h>

//-----------------------
// records include files
//-----------------------
#include "rsu_features.hxx"

//---------------------
// mds include files
//---------------------
#include "alarm.hxx"
#include "basepass.hxx"
#include "basesched.hxx"
#include "motion.hxx"
#include "objhash.hxx"
#include "stdrollpr.hxx"
#include "rollbite.hxx"
#if INCLUDE_GAP
    #include "stretch.hxx"
#endif
#ifdef INCLUDE_RM_BENDING
#include "ufd.hxx"
#include "crlc.hxx"
#endif
#include "utility.hxx"

//---------------------
// shared include files
//---------------------
#include "pce.hxx"

//---------------------
// rsu include files
//---------------------
#include "std.hxx"


#ifdef WIN32
    #ifdef _DEBUG
    #define new DEBUG_NEW
    #endif
    #pragma warning(disable: 4244) // double to float conversion (NT thinks constants are doubles)
#endif

// Data schema for the cStd class.
static cSchema::schema_type cStd_schema[]=
{
    //Next  Enum  Schema details                            Fmt  Units        Comment
    //====  ====  ========================================  ==== ===========  ==================================================
    // base stand
    LINK_TO_SCHEMA("cBaseStd","cBaseStd")

    // derived stand
    { NULL, NULL, SCHEMA_T(cStd,float,lo_fs_mult),          "",  "",          "Low pu limit for flow stress recalculation" },
    { NULL, NULL, SCHEMA_T(cStd,float,hi_fs_mult),          "",  "",          "High pu limit for flow stress recalculation" },
    { NULL, NULL, SCHEMA_T(cStd,float,chock_max_lmt),       "",  "mm_in",     "Max chock openning - max center line openning of rolls" },
    { NULL, NULL, SCHEMA_T(cStd,int,max_ps_std),            "",  "",          "Max passes to be taken on this stand" },
#ifdef SKEW_ROLLING
	{ NULL, NULL, SCHEMA_T(cStd,float,skew_width_safety),   "",  "mm_in",   "safety offset of roll width for sideguide for skew rolling" },
#endif
#ifdef INCLUDE_RM_BENDING
	{ NULL, NULL, SCHEMA_T1(cStd,float,force_bnd_lim, 2),   "",  "mton_eton_kn", "roll bending force limits" },
    { NULL, NULL, SCHEMA_T(cStd,float,force_bnd_bal),       "",  "mton_eton_kn", "roll bending balance force" },
    { NULL, NULL, SCHEMA_T(cStd,float,force_bnd_nom),       "",  "mton_eton_kn", "nominal roll bending force" },
#endif
	{ 0 }   // terminate list
};

// Link all the schema's together
cSchema::schema_name_type cStd::sSchema[]=
{
    {
        "cStd",                             // name
        sizeof(cStd),                       // size
        cStd_schema,                        // schema
        false,                              // packed
        true,                               // allow ptr
        false,                              // Read only
        "Static stand configuration",       // comment
        0                                   // offset to config data
    },

    { 0 } // terminate list
};

// Data schema for the cStdD class.
static cSchema::schema_type cStdD_schema[]=
{
    //Next  Enum  Schema details                            Fmt  Units                Comment
    //====  ====  ========================================  ==== ===========          ==================================================
    // Base classes
    LINK_TO_SCHEMA("cBaseStdD","cBaseStdD")

    // Derived class
    { NULL, NULL, SCHEMA_T(cStdD,float,fs_mult),            "",  "",                  "Flow stress multiplier)" },
    { NULL, NULL, SCHEMA_T(cStdD,float,force_ratio),        "",  "",                  "Force ratio (measured/repredicted)" },
    { NULL, NULL, SCHEMA_T(cStdD,float,power_ratio),        "",  "",                  "Power ratio (measured/repredicted)" },
    { NULL, NULL, SCHEMA_T(cStdD,float,rms),                "",  "sec",               "Predicted RMS" },
    { NULL, NULL, SCHEMA_T(cStdD,float,limrat_xlen),        "",  "",                  "exit length limit ratio" },
    { NULL, NULL, SCHEMA_T(cStdD,float,max_gap),            "",  "mm_in",             "maximum gap openning"},
    { NULL, NULL, SCHEMA_T(cStdD,float,power_max_hd),       "",  "kw",                "maximum power limit on the head end - Impact"},

    // added for feedback
    { NULL, NULL, SCHEMA_T(cStdD,float,fs_obs),             "",  "kg/mm2_lb/in2_MPa", "observed mean flow stress" },
    { NULL, NULL, SCHEMA_T(cStdD,float,sigmat_rep),         "",  "kg/mm2_lb/in2_MPa", "repredicted per unit flow stress" },
    { NULL, NULL, SCHEMA_T(cStdD,float,sigmat_obs),         "",  "kg/mm2_lb/in2_MPa", "observed per unit flow stress" },
    { NULL, NULL, SCHEMA_T(cStdD,bool ,sigmat_obs_vld),     "",  "",                  "observed per unit flow stress calcs are valid" },
    { NULL, NULL, SCHEMA_T(cStdD,float,thick_meas),         "",  "mm_in",             "Measured thickness" },
    { NULL, NULL, SCHEMA_T(cStdD,float,thick_obs),          "",  "mm_in",             "Observed thickness" },
    { NULL, NULL, SCHEMA_T(cStdD,float,spos),               "",  "mm_in",             "Screw position" },
    { NULL, NULL, SCHEMA_T(cStdD,float,mtr_volts),          "",  "volts",             "Motor volts" },
    { NULL, NULL, SCHEMA_T(cStdD,float,mtr_current),        "",  "amps",              "Motor current" },
    { NULL, NULL, SCHEMA_T(cStdD,float,power_vhd),          "",  "KW",                "Very head end power measured" },
    { NULL, NULL, SCHEMA_T(cStdD,bool,thick_meas_vld),      "",  "",                  "Measured thickness valid" },
    { NULL, NULL, SCHEMA_T(cStdD,bool,force_meas_vld),      "",  "",                  "Measured force valid" },
    { NULL, NULL, SCHEMA_T(cStdD,bool,power_meas_vld),      "",  "",                  "Measured power valid" },
    { NULL, NULL, SCHEMA_T(cStdD,bool,power_vhd_vld),       "",  "",                  "Measured very head end power valid" },
    { NULL, NULL, SCHEMA_T(cStdD,bool,spos_vld),            "",  "",                  "Measured screw position valid" },
    { NULL, NULL, SCHEMA_T(cStdD,bool,gap_vld),             "",  "",                  "Measured gap valid" },
    { NULL, NULL, SCHEMA_T(cStdD,float,fbgag1),             "",  "mm_in",             "Feedback gage by gap and stretch" },
    { NULL, NULL, SCHEMA_T(cStdD,float,fbgag2),             "",  "mm_in",             "Feedback gage by force ratio" },
    { NULL, NULL, SCHEMA_T(cStdD,float,fbgag),              "",  "mm_in",             "Calculated exit gage by feedback" },
    { NULL, NULL, SCHEMA_T(cStdD,float,weight_fact),        "",  "",                  "Weighting factor" },
    { NULL, NULL, SCHEMA_T(cStdD,int,pm_status),            "",  "",                  "Measured power status" },
    { NULL, NULL, SCHEMA_T(cStdD,int,pm_vhd_status),        "",  "",                  "Measured very head power status" },
    { NULL, NULL, SCHEMA_T(cStdD,float,stack_exp),          "",  "mm_in",             "Stack centerline expansion" },
    { NULL, NULL, SCHEMA_T(cStdD,float,wroll_wear),         "",  "mm_in",             "Work roll wear, top + bottom" },
    { NULL, NULL, SCHEMA_T(cStdD,float,broll_wear),         "",  "mm_in",             "Backup roll wear, top + bottom" },
#if INCLUDE_GAP
    { NULL, NULL, SCHEMA_T(cStdD,float,stretch),            "",  "mm_in",             "Total stretch (housing + stack)" },
    { NULL, NULL, SCHEMA_T(cStdD,float,gap),                "",  "mm_in",             "Screw gap opening" },
    { NULL, NULL, SCHEMA_T(cStdD,float,hmod),               "",  "mm/mton_in/eton",   "Housing modulus" },
    { NULL, NULL, SCHEMA_T(cStdD,float,mmod),               "",  "mm/mton_in/eton",   "Mill modulus" },
    { NULL, NULL, SCHEMA_T(cStdD,float,hs_stretch),         "",  "mm_in",             "Housing stretch" },
    { NULL, NULL, SCHEMA_T(cStdD,float,st_deflection),      "",  "mm_in",             "Stack deflection" },
#endif
    { NULL, NULL, SCHEMA_T( cStdD, float, dia_avg_wr ),                 "",  "mm_in",          "work   roll  average diameter" },
    { NULL, NULL, SCHEMA_T( cStdD, float, dia_avg_br ),                 "",  "mm_in",          "backup roll  average diameter" },
    { NULL, NULL, SCHEMA_T( cStdD, float, mod_eqv_wr ),                 "",  "kg/mm2_psi_mpa", "work roll equivalent Young's modulus" },
    { NULL, NULL, SCHEMA_T( cStdD, float, mod_eqv_br ),                 "",  "kg/mm2_psi_mpa", "backup roll equivalent Young's modulus" },
    { NULL, NULL, SCHEMA_T( cStdD, float, temp_avg_wr ),                "",  "c_f",            "work roll mill centerline volumetric avgerage temperature" },
    { NULL, NULL, SCHEMA_T( cStdD, float, temp_srf_wr ),                "",  "c_f",            "work roll mill centerline surface temperature" },
    { NULL, NULL, SCHEMA_T( cStdD, float, wear_avg_wr ),                "",  "mm_in",          "work roll mill centerline avgerage wear" },
    { NULL, NULL, SCHEMA_T( cStdD, float, wear_avg_br ),                "",  "mm_in",          "backup roll mill centerline avgerage wear" },
    { NULL, NULL, SCHEMA_T1( cStdD, float, crn_wr, 2 ),                 "",  "mm_in",          "work roll ground crown: 0 = top, 1 = bot" },
    { NULL, NULL, SCHEMA_T1( cStdD, float, crn_br, 2 ),                 "",  "mm_in",          "backup roll ground crown: 0 = top, 1 = bot" },
    { NULL, NULL, SCHEMA_T1( cStdD, float, len_wr, 2 ),                 "",  "m_ft",           "work roll piece length rolled: 0 = top, 1 = bot" },
    { NULL, NULL, SCHEMA_T1( cStdD, float, len_br, 2 ),                 "",  "m_ft",           "backup roll piece length rolled: 0 = top, 1 = bot" },
    { NULL, NULL, SCHEMA_T1( cStdD, int, num_wr, 2 ),                   "",  "",               "work roll number pieces rolled: 0 = top, 1 = bot" },
    { NULL, NULL, SCHEMA_T1( cStdD, int, num_br, 2 ),                   "",  "",               "backupu roll number pieces rolled: 0 = top, 1 = bot" },
    { NULL, NULL, SCHEMA_T2( cStdD, char, ser_num_wr, 2, nameSize12 ),  "",  "",               "work roll serial number: 0 = top, 1 = bot" },
    { NULL, NULL, SCHEMA_T2( cStdD, char, ser_num_br, 2, nameSize12 ),  "",  "",               "backup roll serial number: 0 = top, 1 = bot" },
    { NULL, NULL, SCHEMA_T2( cStdD, char, matl_typ_wr, 2, nameSize12 ), "",  "",               "work roll material type: 0 = top, 1 = bot" },
    { NULL, NULL, SCHEMA_T2( cStdD, char, matl_typ_br, 2, nameSize12 ), "",  "",               "backup roll material type: 0 = top, 1 = bot" },
#ifdef SKEW_ROLLING
    { NULL, NULL, SCHEMA_T(cStdD,int,skew_pass),                "",  "",             "skew rolling on this pass" },
    { NULL, NULL, SCHEMA_T(cStdD,float,skew_angle),                "",  "deg",             "skew rolling angle on this pass" },
#endif
    { NULL, NULL, SCHEMA_PO(cStdD,cStd,pcStd),                          "",  "",         "Pointer to static derived stand" },
    { NULL, NULL, SCHEMA_PO(cStdD,cPceD,pcEnPceD),                      "",  "",         "" },
    { NULL, NULL, SCHEMA_PO(cStdD,cPceD,pcExPceD),                      "",  "",         "" },

#if INCLUDE_GAP
    // Include a pointer to the stretch object if gap calculations are required
    { NULL, NULL, SCHEMA_PO(cStdD,cStretch,pcStretch),                  "",  "",         "" },
#endif

#ifdef INCLUDE_RM_BENDING
    { NULL, NULL, SCHEMA_T(cStdD,bool, bnd_enab),             "",  "",  "roll bending system enabled indicator" },
    { NULL, NULL, SCHEMA_T(cStdD,float, op_bnd_off),          "",  "mton_eton_kn", "operator roll bending force offset" },
    { NULL, NULL, SCHEMA_T(cStdD,float, force_bnd),           "",  "mton_eton_kn", "roll bending force" },
    { NULL, NULL, SCHEMA_T(cStdD,float, force_bnd_des),       "",  "mton_eton_kn", "desired roll bending force" },
    { NULL, NULL, SCHEMA_T(cStdD,float, dbnd_dfrc),           "",  "mton/mton_eton/eton_kn/kn", "delta roll bending force / delta rolling force" },
	{ NULL, NULL, SCHEMA_PO( cStdD, cCRLCD, pcCRLCD),                                 "",  "",                        "pointer to dynamic CRLC object" },
    { NULL, NULL, SCHEMA_PO( cStdD, cUFDD, pcUFDD),                                   "",  "",                        "pointer to dynamic UFD object" },
#endif

    { 0 }   // terminate list

};

// Link all the schema's together
cSchema::schema_name_type cStdD::sSchema[]=
{
    {
        "cStdD",                            // name
        sizeof(cStdD),                      // size
        cStdD_schema,                       // schema
        false,                              // packed
        true,                               // allow ptr
        false,                              // Read only
        "Dynamic stand data",               // comment
        0                                   // offset to config data
    },

    { 0 } // terminate list
};



// Diagnostic level specific to this file
//static const cGlobal::DiagnosticCodeEnum diagLvl(cGlobal::Std);
static const int diagLvl = 0;




//-----------------------------------------------------------------------------
// Static Stand Object
//-----------------------------------------------------------------------------
//-----------------------------------------------------------------------------
// cSTD CONSTRUCTOR ABSTRACT:
//   Stand constructor
//-----------------------------------------------------------------------------
int cStd::count   (0); // initialize static members
cStd::cStd()
    : cBaseStd()
{
    Set_Class_Name("cStd");
    Set_Schema("cStd",sSchema);
    // Zero out member data
    Zero_Data(this, sizeof(cStd), Get_Schema("cStd"));
}

cStd::cStd( const MString     &objectName,
            const objTypEnum stdType,
            const objPosEnum position,
                  void       *pHash)
    : cBaseStd( objectName, stdType, position, pHash)
    , num       (++count)
{
    Set_Class_Name("cStd");
    Set_Schema("cStd",sSchema);

    // Zero out member data
    Zero_Data(this, sizeof(cStd), Get_Schema("cStd"));

}

//-------------------------------------------------------------------------
// ~CSTD ABSTRACT:
//   Stand deconstructor
//-------------------------------------------------------------------------
cStd::~cStd()
{
}

//-------------------------------------------------------------------------
// CSTD ABSTRACT:
//   Stand copy constructor
//-------------------------------------------------------------------------
cStd::cStd (const cStd& source)
    : cBaseStd( source )
{

}

//-------------------------------------------------------------------------
// OPERATOR = ABSTRACT:
//   Stand assignment operator
//-------------------------------------------------------------------------
cStd& cStd::operator= (const cStd& source)
{
    if (this != &source)
    {
        cBaseStd::operator=(source);

        num  = source.num;

        Copy_Data(this, (void *)&source,sizeof(cStd),cStd_schema);
    }
    return (*this);
}

//-------------------------------------------------------------------------
// LINKOBJ ABSTRACT
//   Stand link to static sub-objects
//-------------------------------------------------------------------------
bool cStd::linkObj(const void        *pVoid,
                   const objTypEnum  objType,
                   const objPosEnum  objPos )
{
    bool    retValue (TRUE);
    if (!pVoid)
    {
        EMSG << "Passed child pointer is NULL - exiting" << END_OF_MESSAGE;
        return (retValue);
    }

    // perform linkObj on next object up in hierarchy
    return cBaseStd::linkObj(pVoid, objType, objPos);

} // end cStd::linkObj()


//-------------------------------------------------------------------------
// DUMP ABSTRACT:
//   Std dump contents of the struct.
// The boolean composed if true indicates that the
// object should call the dump function for the
// objects that it contains.
//-------------------------------------------------------------------------
void cStd::dump(const bool composed)
{

    // Generically dump the data
    Dump_Data(stdout, "cStd", this, 0, (const char *)objName());
    // let the base stand dump its composed parts
    cBaseStd::dump(composed);
} // end cStd::dump


// Create a new dynamic object.  Intended to be used
// where we have a static/dynamic pair so that the
// static object may create its dynamic equivalent
cBase*  cStd::Create_Dynamic_Object()
{
    cStdD   StdD;

    return StdD.Create_Object();
}

cBase*  cStd::Create_Dynamic_Object(
                    const MString&     objName,
                    const objTypEnum  objType,
                    const objPosEnum  objPosition,
                    void              *pHash,
                    const int         size,
                    void              *pVoid,
                    void              *pFbkVoid,
                    void              *pRaw,
                    void              *pAdapted)
{
    cStdD   StdD;

    return StdD.Create_Object(
                objName,
                objType,
                objPosition,
                pHash, size, pVoid);
}


//-----------------------------------------------------------------------------
// Dynamic Stand Object
//-----------------------------------------------------------------------------
//-----------------------------------------------------------------------------
// Dynamic Stand Object
//-----------------------------------------------------------------------------
#ifdef INCLUDE_RM_BENDING
cUFD*  cStdD::pcUFD  = NULL;
cCRLC* cStdD::pcCRLC = NULL;
#endif
//-----------------------------------------------------------------------------
// CSTDD CONSTRUCTOR ABSTRACT:
//   Stand constructor
//-----------------------------------------------------------------------------
cStdD::cStdD()
    : pcStd (NULL)
    , pcEnPceD (NULL)
    , pcExPceD (NULL)
#if INCLUDE_GAP
    , pcStretch (NULL)
#endif
#ifdef INCLUDE_RM_BENDING
	, pcCRLCD (NULL)
    , pcUFDD (NULL)
#endif
{
    Set_Class_Name("cStdD");
    Set_Schema("cStdD",sSchema);

    // Zero out member data
    Zero_Data(this, sizeof(cStdD), Get_Schema("cStdD"));
}

cStdD::cStdD( const MString     &objectName,
              const objTypEnum stdType,
              const objPosEnum position,
                    void       *pHash)
    : cBaseStdD( objectName, stdType, position, pHash )
    , pcStd         (NULL)
    , pcEnPceD  (NULL)
    , pcExPceD  (NULL)
#if INCLUDE_GAP
    , pcStretch (NULL)
#endif
#ifdef INCLUDE_RM_BENDING
	, pcCRLCD (NULL)
    , pcUFDD (NULL)
#endif
{
    Set_Class_Name("cStdD");
    Set_Schema("cStdD",sSchema);

    int stdNum_save = stdNum;   // Preserve stdNum from Zero_Data.

    // Zero out member data
    Zero_Data(this, sizeof(cStdD), Get_Schema("cStdD"));

    stdNum = stdNum_save;

    // Create the exit piece state associated with this stand and link it
    // up to the stand
    pcExPceD = new cPceD (objectName + (MString)"_pce", ot_undef, op_undef, pHash);
    // Link the exit piece as a child of this dynamic derived stand.
    linkObj(pcExPceD, ot_undef, op_exit);

    next_obj = pcExPceD;
    pcExPceD->previous_obj = this;

    // Find the static stand object
    char    *pName = strchr(objectName, '_') + 1;
    objHashType::ResultT    result;
    result = Objhash.cmnFind(pName);
    if (HashTableHlp::duplicate == result.status)
    {
        linkObj(result.data, ot_undef, op_undef);
    }
    else
    {
        EMSG << "No object named "
             << pName << " exists"
             << END_OF_MESSAGE;
    }

#if INCLUDE_GAP
    // Find the static stretch object
    // Expects name of the format xx_stretch where
    // xx is the name of the static stand object.
    char    buff[64];
    sprintf(buff, "%s_stretch", pName);
    result = Objhash.cmnFind((const char *)(buff));
    if (HashTableHlp::duplicate == result.status)
    {
        pcStretch = (cStretch *)(result.data);
    }
    else
    {
        EMSG << "No stretch object named "
             << buff << " exists"
             << END_OF_MESSAGE;
    }
#endif

#ifdef INCLUDE_RM_BENDING
    // Find the static stand object
    pName = strchr(objectName, '_') + 1;
    result = Objhash.cmnFind(pName);
    if (HashTableHlp::duplicate == result.status)
    {
        linkObj(result.data, ot_undef, op_undef);
    }
    else
    {
        EMSG << "No object named "
             << pName << " exists"
             << END_OF_MESSAGE;
    }

    char str_buf1[ 32 ];                        // [-] string buffer
    char str_buf2[ 32 ];                        // [-] string buffer

    //----------------------------------------------------------------------
    // Copy the name of dynamic STD object passed into the constructor into
    // the string buffer array.
    //----------------------------------------------------------------------
    strcpy( str_buf1, ( const char* )( objectName ) );

    //-------------------------------------------------------------------
    // Find the position of the underscore in the string buffer array and
    // terminate the string after the underscore.  Dynamic names are mm#_SSSS
    // where SSSS is the static objects name as registered inthe hash tables
    // # is the dynamic objects number (pass 0 etc) and mm is some number
    // of characters used to identify the type of this instance such as
    // su for setup, fb feedback hdsu for head setup...
    //-------------------------------------------------------------------
    *( strchr( str_buf1, '_' ) + 1 ) = 0;

    //--------------------------------------------------------------------
    // Create the following SSU model dynamic objects and link them to the
    // dynamic STD object:
    //     Uniform Force Distribution (UFD)
    //     Composite Roll Stack Crown (CRLC)
    //     Lateral Roll Gap (LRG)
    //--------------------------------------------------------------------
    int i = 0;

    //--------------------------------------------
    // Find static UFD class in common hash table.
    //--------------------------------------------
    if (NULL == pcUFD)
    {
        pcUFD = (cUFD *)Objhash.Find_Class_In_Hash( i,
                                                    Objhash.cmnGetPtr(),
                                                    "cUFD" );

        if ( NULL == pcUFD )
        {
            EMSG << "cStdD Constructor: Cannot find static UFD class"
                 << END_OF_MESSAGE;

            return;
        }
    }
    //-------------------------------------------------------------------
    // Concatenate the truncated string buffer array with the name of the
    // dynamic UFD object.
    //-------------------------------------------------------------------
    sprintf( str_buf2, "%s%s", str_buf1, ( const char* )( pcUFD->objName() ) );

    //-----------------------------
    // Create a dynamic UFD object.
    //-----------------------------
    pcUFDD = new cUFDD( str_buf2, ot_undef, op_undef, pHash);

    linkObj(pcUFDD, ot_undef, op_undef);

    //---------------------------------------------
    // Find static CRLC class in common hash table.
    //---------------------------------------------
    if (NULL == pcCRLC)
    {
        i = 0;
        pcCRLC = (cCRLC *)Objhash.Find_Class_In_Hash( i,
                                                      Objhash.cmnGetPtr(),
                                                      "cCRLC" );

        if ( NULL == pcCRLC )
        {
            EMSG << "cStdD Constructor: Cannot find static CRLC class"
                 << END_OF_MESSAGE;

            return;
        }
    }
    //-------------------------------------------------------------------
    // Concatenate the truncated string buffer array with the name of the
    // dynamic CRLC object.
    //-------------------------------------------------------------------
    sprintf( str_buf2, "%s%s", str_buf1, ( const char* )( pcCRLC->objName() ) );

    //------------------------------
    // Create a dynamic CLRC object.
    //------------------------------
    pcCRLCD = new cCRLCD( str_buf2, ot_undef, op_undef, pHash);

    linkObj(pcCRLCD, ot_undef, op_undef);
#endif
} // end cStdD::cStdD

//-------------------------------------------------------------------------
// ~CSTDD ABSTRACT:
//   Stand deconstructor
//-------------------------------------------------------------------------
cStdD::~cStdD()
{

}

void cStdD::zeroData(void)
{
    // Zero out member data

    Zero_Data(this, sizeof(cStdD), Get_Schema("cStdD"));

    cBaseStdD::zeroData();
}

//-------------------------------------------------------------------------
// LINKOBJ ABSTRACT
//   Stand link to dynamic sub-objects
//-------------------------------------------------------------------------
bool cStdD::linkObj( const void       *pVoid,
                     const objTypEnum objType,
                     const objPosEnum objPos )
{
    cBase   *pcBase = (cBase *)(pVoid);
    bool     retValue     (TRUE);
    bool     retValueBase (TRUE);

    if (!pVoid)
    {
        EMSG << "Passed child pointer is NULL - exiting" << END_OF_MESSAGE;
        return (retValue);
    }

    // Perform linkObj on next object up in hierarchy
    retValueBase = cBaseStdD::linkObj(pVoid, objType, objPos);

    // Only the static stand is linked here.
    if ( Is_Base_Class("cStd", pcBase) )
    {
        if (pcStd)
        {
            retValue =FALSE;
        }
        pcStd = (cStd*) pVoid;
    }

    return retValueBase && retValue;

} // end cStdD::linkObj()

//-------------------------------------------------------------------------
// CSTDD ABSTRACT:
//   Stand copy constructor
//-------------------------------------------------------------------------
cStdD::cStdD (const cStdD& source)
    : cBaseStdD( source )
{

}

//-------------------------------------------------------------------------
// OPERATOR = ABSTRACT:
//   Stand assignment operator
//-------------------------------------------------------------------------
cStdD& cStdD::operator= (const cStdD& source)
{
    if (this != &source)
    {
      cBaseStdD::operator=(source);
      Copy_Data(this,(void *)&source,sizeof(cStdD),cStdD_schema);
    }
    return (*this);
}

//---------------------------------------------------------------------------
// ASSIGN_STATE ABSTRACT:
//  Assigns state data from a source object to a destination object of
//  the same type.
//---------------------------------------------------------------------------
bool  cStdD::Assign_State(cBase * pcDest, cBase * pcSource)
{
    //-----------------------------------------------------------------------
    // Check pointers.  Alarm and abort if source or destination pointer is
    //      NULL.
    //-----------------------------------------------------------------------
    if ( pcDest == NULL )
    {
        EMSG << "NULL pointer in pcDest"
             << END_OF_MESSAGE;
        return false;
    }
    if ( pcSource == NULL )
    {
        EMSG << "NULL pointer in pcSource"
             << END_OF_MESSAGE;
        return false;
    }
    //-----------------------------------------------------------------------
    // Check object types.  Alarm and abort if the source and destination
    //      objects are not identical.
    //-----------------------------------------------------------------------
    if ( pcDest->Get_Class_Name() != pcSource->Get_Class_Name() )
    {
        EMSG << "Cannot assign " << (const char *)pcSource->objName()
             << " to " << (const char *)pcDest->objName()
             << ".  Assignment aborted."
             << END_OF_MESSAGE;
        return false;
    }
    //-----------------------------------------------------------------------
    // Assign source to destination object.
    //-----------------------------------------------------------------------
    *((cStdD *)(pcDest)) = *((cStdD *)(pcSource));
    return true;

}   // end cStdD::Assign_State()

//-------------------------------------------------------------------------
//   Std dump contents of the struct.
// The boolean composed if true indicates that the
// object should call the dump function for the
// objects that it contains.
//-------------------------------------------------------------------------
void cStdD::dump(const bool composed)
{

    // Generically dump the data
    Dump_Data(stdout, "cStdD", this, 0, (const char *)objName());

    if (composed)
    {
        ;
    }
    cBaseStdD::dump( composed );

}// end cStdD::dump


//---------------------------------------------------------------------
// Virtual function to allow the user to carry out post processing for
// a dynamic stand.
//---------------------------------------------------------------------
bool cStdD::Post_Config(
                char *name,         // unused
                void *psStruct)     // unused
{
    pcEnPceD = (cPceD *)(previous_obj);

    //  Call Post_Config() for the enclosed cBaseStdD
    //  so that its entry base piece pointer is set.

    cBaseStdD::Post_Config( name, psStruct ) ;

    return true;

}

//-----------------------------------------------------------------
// Operate()
//
// This method updates the exit piece state, given the entry state
// information for a piece.
//
// On the base object this method does nothing.
//-----------------------------------------------------------------
bool cStdD::Operate(void)
{
    bool ret;
	//-------------------------------------------------------------
    // Bulk copy data from entry piece to exit piece
    //-------------------------------------------------------------
    *pcExPceD = *pcEnPceD;

    ret = cBaseStdD::Operate();
#ifdef SKEW_ROLLING
    float dl, dw, theta;
	if (this->skew_pass != 0)
	{

//		Calculate change in length and width for this pass
		dl = pcExPceD->length - pcEnPceD->length;
		dw = pcExPceD->width - pcEnPceD->width;

//		Calculate the skew angle given the sideguide preset width and entry slab width,length
		theta = asin(
			(pcStdRollPrD->getFaceWidth(op_work) - this->pcStd->skew_width_safety)
			/sqrt(pcEnPceD->width*pcEnPceD->width
				+ pcEnPceD->length*pcEnPceD->length*Physcon.mmpm_inpft*Physcon.mmpm_inpft))
			- atan(pcEnPceD->width/(pcEnPceD->length*Physcon.mmpm_inpft));

		this->skew_angle = theta;

//		Recalculate the pass exit length and width
		pcExPceD->length = pcEnPceD->length + dl*cos(theta) + dw*sin(theta)/Physcon.mmpm_inpft;
		pcExPceD->width = pcEnPceD->width + dl*sin(theta)*Physcon.mmpm_inpft + dw*cos(theta);
		this->spread = pcExPceD->width - pcEnPceD->width;
	}
#endif
	return ret;
}

//---------------------------------------------------------------------
// cStdD::Find_Sched() ABSTRACT
//
// Follow up the parent pointers and locate the sched pointer for this
// dynamic stand.
//---------------------------------------------------------------------
cBaseSched  *cStdD::Find_Sched(void)
{
    cBaseSched  *pcBaseSched;                           // Sched pointer
    cBasePassD  *pcBasePassD;                           // Dynamic pass pointer

    //---------------------------------------------------------
    // Get a pointer to the schedule, which is a parent of the
    // pass, which is a parent of the dynamic stand.
    //---------------------------------------------------------
    if ( this->parent_obj == NULL )
    {
        EMSG << "Dynamic stand has no parent pass"
             << END_OF_MESSAGE;
        return NULL;
    }
    pcBasePassD = (cBasePassD *)(this->parent_obj);
    if ( pcBasePassD->parent_obj == NULL )
    {
        EMSG << "Dynamic stand has no schedule parent of its pass parent"
             << END_OF_MESSAGE;
        return NULL;
    }
    if ( pcBasePassD->parent_obj->Get_Class_Name() != "cSched" )
    {
        EMSG << "Top level parent is not of type cSched"
             << END_OF_MESSAGE;
        return NULL;
    }
    pcBaseSched = (cBaseSched *)(pcBasePassD->parent_obj);

    return pcBaseSched;
}


//---------------------------------------------------------------------------
// Length() ABSTRACT:
//
// Calculate the exit piece length.  User may supply a function on the
// derived stand if he chooses to override the calculation on the base stand.
// Note: Volume is calculated based on the cold dimensions and has the unit
//       of [mm2*m_in2*ft]
//---------------------------------------------------------------------------
float cStdD::Length(void)
{
    float   lin_exp = pcEnPceD->Expansion();
    return  (pcEnPceD->pcPce->volume * lin_exp * lin_exp * lin_exp) /
            (pcExPceD->thick * pcExPceD->width);
}

//-------------------------------------------------------------------------
// SPEED ABSTRACT:
// Calculate the roll peripheral speed and motor rpm.  The user may
// override this on the derived stand to suite his specific purposes.
//-------------------------------------------------------------------------
void    cStdD::Speed(void)
{
    float   approx_slip = 0.0;

    // ---------------------------------------------------------------
    // Set the required piece input data for
    // the roll pressure distribution calculations.
    // ---------------------------------------------------------------
    if ( pcRollbite->Force_Valid() && (slip != 0.0) )
    {
        // use last calculated slip
        approx_slip = slip;
    }
    else
    {
        // first time, use approximation for slip
        approx_slip =
            pcRollbite->Approximate_Slip(
            pcStdRollPrD->getAvgDiam(op_work),  // roll diameter
            pcExBasePceD->thick,             // exit thickness
            pcEnBasePceD->thick);            // entry thickness
    }

    // Calculate roll peripheral speed
    // only in setup mode. In feedback mode
    // speed will be initialized to measured
    // roll speed
    if ( !this->feedback )
    {
        speed = pcEnPceD->speed * pcEnPceD->thick /
                ((pcEnPceD->thick - draft) * approx_slip);
    }

    // Calculate motor rpm for power to torque conversions
    rpm = Physcon.mmpm_inpft * speed *
              pcBaseStd->gearat * Physcon.secpmin /
              (Physcon.pi *
               pcStdRollPrD->getAvgDiam(op_work) *
               Physcon.vel_time);

} // End cStdD::Speed()


//-------------------------------------------------------------------------
// VOLUME_FLOW ABSTRACT:
// Calculate the volume flow.  The user may override this on the derived
// stand to suite his specific purposes.
//-------------------------------------------------------------------------
float   cStdD::Volume_Flow()
{
    float volume_flow = pcEnPceD->thick * pcEnPceD->speed *
                        rbite_width *
                        Physcon.mmpm_inpft / Physcon.vel_time;

    return volume_flow;

} // End cStdD::Volume_Flow()


//-------------------------------------------------------------------------
// Entry_Linear_Speed() ABSTRACT:
// Exit_Linear_Speed()  ABSTRACT:
//
// Convert motor rpm to linear strip speed. [m/min_ft/min_m/sec]
//-------------------------------------------------------------------------
float           cStdD::Entry_Linear_Speed(float rpm)
{
    return Motion.Linear_Speed(
                    this->pcStdRollPrD->getAvgDiam(op_work),
                    this->draft_comp * rpm /
                    this->pcStd->gearat);
}

float           cStdD::Exit_Linear_Speed(float rpm)
{
    return Motion.Linear_Speed(
                    this->pcStdRollPrD->getAvgDiam(op_work),
                    this->slip * rpm /
                    this->pcStd->gearat);
}


//-------------------------------------------------------------------------
// Circ() ABSTRACT:
//
// Calculate the horizontal work roll circumference corresponding to one
// revolution of the motor in major length units. [m_ft]
// vel_time is added to ensure correct speed calculations in all unit
// systems - metric, modified metric, english and SI.
//-------------------------------------------------------------------------
float           cStdD::Circ(void)
{
    return Physcon.pi *
           this->pcStdRollPrD->getAvgDiam(op_work) * Physcon.vel_time /
           this->pcStd->gearat / Physcon.secpmin / Physcon.mmpm_inpft;
}

//-------------------------------------------------------------------------
// GapOfs ABSTRACT:
// Calculate the roll thermal and wear and gap offset.
//-------------------------------------------------------------------------
bool   cStdD::GapOfs(void)
{

    //-----------------------------------------------------
    // Check for dummied stand.



    //-----------------------------------------------------
    if( !this->dummied )
    {
        //-------------------------------------------------------------------
        // Get Work Roll stack expansion for 2Hi stand.  Note: the adjustment
        // for "now vs zeroing" has already been done by the RTWM.
        //-------------------------------------------------------------------
        this->stack_exp = this->pcStdRollPrD->getThrmCent();

        //-----------------------------------------------------------------
        // Get Work Roll stack wear for 2Hi stand.
        // Calculate mean wear of top work roll and bottom work roll.
        //   getWearCent() returns wear relative to last zeroed position.
        //   getWearCentTot() returns total accumulated wear
        //-----------------------------------------------------------------
        //this->wroll_wear = ( this->pcWRPairD->getWearCentTot(rpos_top)
        //                    -this->pcWRPairD->getTopRollD()->rollD.wearZero +
        //                     this->pcWRPairD->getWearCentTot(rpos_bottom)
        //                    -this->pcWRPairD->getBotRollD()->rollD.wearZero ) / 2.0F;

        this->wroll_wear = ( this->pcStdRollPrD->getWearCent(rpos_top)
                             + this->pcStdRollPrD->getWearCent(rpos_bottom) )/2.0F ;

        if ( !this->pcStd->two_high )
        {
            this->broll_wear = ( this->pcStdRollPrD->getWearCent(rpos_top, op_backup) +
                                 this->pcStdRollPrD->getWearCent(rpos_bottom, op_backup) ) / 2.0F;

            this->wroll_wear *= 2.0F;   // 4Hi WR wear
            this->stack_exp  *= 2.0F;   // 4Hi WR thermal expansion
        }

        //------------------------------------------------------------
        // Calc. the screw offset for this stand  =
        //                  +   Roll stack expansion
        //                  +   (Work roll wear - zeroing offset)
        //                  +   (BU roll wear - zeroing offset)
        //-------------------------------------------------------------
        this->gapoff = this->stack_exp +
                       this->wroll_wear +
                       this->broll_wear;
    }

    return true;
}   // end  cStdD::GapOfs()

#if INCLUDE_GAP
//-------------------------------------------------------------------------
// GAP ABSTRACT:
// Calculate the roll gap.  The user must supply a function on the derived
// stand to calculate something other than zero.
//-------------------------------------------------------------------------
float   cStdD::Gap(void)
{

    if ( pcStretch == NULL )
    {
        EMSG << "Stretch object is NULL for object " << this->objName()
             << END_OF_MESSAGE;
        return 0.0;
    }

    //-----------------------------------------------------
    // Calculate horizontal stand stretch and screw offsets
    // and position.
    //-----------------------------------------------------
    if( !this->dummied )
    {
        //-----------------------------------------------------
        // Calculate stack expansion, roll wear and gap offset.
        //-----------------------------------------------------
        if ( !this->GapOfs() )
        {
            ;
        }

        //---------------------------------------
        // Calculate the housing and mill stretch
        //---------------------------------------
        this->pcStretch->Calc_Stretch(
                             this->hs_stretch,
                             this->st_deflection,
                             this->hmod,
                             this->mmod,
                             this->force_strip,
                             this->pcEnPceD->width,
                             this->pcStdRollPrD->getSKRM());

        this->stretch = hs_stretch + st_deflection;

        //--------------------------------------------
        // Calc. screw reference = stand_exit_gauge
        //                        - stand stretch
        //                        + stand screw offset
        //--------------------------------------------
        this->gap = this->pcExPceD->thick -
                    this->stretch +
                    this->gapoff;

    }
    else
    {
         // (stand is dummied)
        this->gap = this->pcExPceD->thick +
                    this->pcStd->dmy_ofs;
        this->stretch = 0.0;
    }

    return this->gap;

}   // end cStdD::Gap(void)

//-------------------------------------------------------------------------
// Max_Gap ABSTRACT:
// Calculate the maximum roll gap openning.  The user must supply a function
// on the derivedstand to calculate something other than zero.
//-------------------------------------------------------------------------
float   cStdD::Max_Gap(void)
{

    //-----------------------------------------------------
    // Calculate the maximum roll gap openning.
    //-----------------------------------------------------
#if MAX_GAP_CHOCK_LMT
    this->max_gap = this->pcStd->chock_max_lmt;
#else
    this->max_gap = this->pcStd->chock_max_lmt - this->pcStdRollPrD->getAvgDiam(op_work);
#endif

    if ( this->max_gap < 0.0F )
    {
        this->max_gap = 0.0F;
    }

    return this->max_gap;

}   // end cStdD::Max_Gap(void)
#endif


//-----------------------------------------------------------------------------
// COPY_ROLL_DATA ABSTRACT:
//    Function to copy static / dynamic roll data to dynamic STD object.
//-----------------------------------------------------------------------------
void cStdD::Copy_Roll_Data( void )

{   // Begin of COPY_ROLL_DATA function

// deleted

}   // End of COPY_ROLL_DATA function

