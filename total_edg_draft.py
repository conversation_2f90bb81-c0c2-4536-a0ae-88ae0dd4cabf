class ESUD:
    def __init__(self):
        self.obj_chain = None

    def total_edg_draft(self, sup_pass_d, fst_pas, lst_pas):
        """
        计算粗轧中轧边机的总轧制量

        Args:
            sup_pass_d: 道次数组
            fst_pas: 起始道次
            lst_pas: 结束道次

        Returns:
            tuple: (drafts, drafts_min, drafts_max)
                  - drafts: 总实际轧制量
                  - drafts_min: 总最小轧制量
                  - drafts_max: 总最大轧制量
        
        功能说明：
        遍历指定范围内的轧制道次，累加每个道次的：
        1. 实际轧制量（draft）
        2. 最小轧制量（draft_min）
        3. 最大轧制量（draft_max）
        """
        # 获取主体段索引
        iseg = self.obj_chain.body_index()
        
        # 初始化累计值
        drafts = 0.0      # 总实际轧制量
        drafts_min = 0.0  # 总最小轧制量
        drafts_max = 0.0  # 总最大轧制量

        # 遍历道次累计轧制量
        for ps in range(fst_pas, lst_pas + 1):
            # 获取轧边机对象
            edg_d = sup_pass_d[ps].drafting_edg_d(iseg)
            if edg_d is not None:
                # 累加各类轧制量
                drafts += edg_d.draft           # 累加实际轧制量
                drafts_min += edg_d.draft_min   # 累加最小轧制量
                drafts_max += edg_d.draft_max   # 累加最大轧制量

        # 返回三个累计值
        return drafts, drafts_min, drafts_max
