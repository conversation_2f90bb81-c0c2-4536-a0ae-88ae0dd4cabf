//-----------------------------------------------------------------------------
//
// ABSTRACT:
//      This class contains methods that calculates width-feedback related
//      quantities.
//
//
//     FUNCTION                 DESCRIPTION
//     -----------------------  -----------------------------------------------
//      Rmx_Fbk                 RM width feedback
//      Fmx_Fbk                 FM width feedback
//      Vern_Upd                Returns the updated width vernier
//      Queue_Upd               Updates the width error queue
//      Queue_Init              Initialize the width error queue
//
//-----------------------------------------------------------------------------

#define WFBK_CXX

//---------------------
// system include files
//---------------------
#include "rsu_features.hxx"

//---------------------
// mds include files
//---------------------
#include "alarm.hxx"
#include "mathuty.hxx"
#include "objchain.hxx"
#include "utility.hxx"
#include "width.hxx"

//---------------------
// shared include files
//---------------------
#include "pce.hxx"
#include "widthsensor.hxx"

//---------------------
// rsu include files
//---------------------
#include "sensorcfg.hxx"
#include "edg.hxx"
#include "esu.hxx"
#include "feedback.hxx"
#include "pass.hxx"
#include "pyrosensor.hxx"
#include "sched.hxx"
#include "setup.hxx"
#include "std.hxx"
#include "thicknesssensor.hxx"
#include "wfbk.hxx"

//---------------------
// records include files
//---------------------
#include "pdi.hxx"
#include "esys.hxx"
#include "fxfbk.hxx"
#include "ramp.hxx"
#include "rapp.hxx"    // add by xubin 2012.10.9
#include "rwidfbk.hxx"
#include "rsys.hxx"


#ifdef WIN32
    #pragma warning(disable: 4244)  // double to float conversion (NT thinks constants are doubles)
    #pragma warning(disable: 4800)  // forcing value to bool 'true' or 'false'
#endif

// Diagnostic level specific to this file
static cAlarm::DiagnosticCodeEnum diagLvl(cAlarm::Feedback);

// Data schema for the cWFBK class.
static cSchema::schema_type cWFBK_schema[]=
{
    //Next  Enum  Schema details                            Fmt  Units        Comment
    //====  ====  ========================================  ==== ===========  ==================================================
    { NULL, NULL, SCHEMA_T(cWFBK,float,rm_gain),           "",  "",          "RM width vernier adaptation gain" },
    { NULL, NULL, SCHEMA_T(cWFBK,float,fm_gain),           "",  "",          "FM width vernier adaptation gain" },
    { NULL, NULL, SCHEMA_T(cWFBK,float,rm_vern_low),       "",  "mm_in",     "RM width vernier low limit" },
    { NULL, NULL, SCHEMA_T(cWFBK,float,rm_vern_hi),        "",  "mm_in",     "RM width vernier hi limit" },
    { NULL, NULL, SCHEMA_T(cWFBK,float,fm_vern_low),       "",  "mm_in",     "FM width vernier low limit" },
    { NULL, NULL, SCHEMA_T(cWFBK,float,fm_vern_hi),        "",  "mm_in",     "FM width vernier hi limit" },
    { NULL, NULL, SCHEMA_T(cWFBK,bool, rm_vern_enab),      "",  "",          "RM width vernier adaptation enabled" },
    { NULL, NULL, SCHEMA_T(cWFBK,bool, fm_vern_enab),      "",  "",          "FM width vernier adaptation enabled" },
    { NULL, NULL, SCHEMA_T(cWFBK,float,vld_wid_tol),       "",  "mm_in",     "acceptable width error between meas. and rep. when esu over/under-draft" },
    { NULL, NULL, SCHEMA_T1(cWFBK,float,rm_wgt_queue,num_wid_err),     "",  "mm_in",     "RM width vernier weight queue" },
    { NULL, NULL, SCHEMA_T1(cWFBK,float,fm_wgt_queue,num_wid_err),     "",  "mm_in",     "FM width vernier weight queue" },
    { 0 }   // terminate list
};

// Link all the schema's together
cSchema::schema_name_type cWFBK::sSchema[]=
{
    {
        "cWFBK",                           // name
        sizeof(cWFBK),                     // size
        cWFBK_schema,                      // schema
        false,                              // packed
        false,                              // allow ptr
        false,                              // Read only
        "Width class configuration",        // comment
        0                                   // offset to config data
    },
    { 0 } // terminate list
};

// Use this constructor if no hash table support required
cWFBK::cWFBK()
{
    Set_Class_Name("cWFBK");
    Set_Schema("cWFBK",sSchema);

    // Zero out member data
    Zero_Data(this, sizeof(cWFBK), Get_Schema("cWFBK"));

}

// Use this constructor if hash table support required
cWFBK::cWFBK( const MString        &objName,
              const objTypEnum    objType,
              const objPosEnum    position,
              void                *pHash)

      : cBase( objName, objType, position, pHash, 0, 0 )
{
    Set_Class_Name("cWFBK");
    Set_Schema("cWFBK",sSchema);

    // Zero out member data
    Zero_Data(this, sizeof(cWFBK), Get_Schema("cWFBK"));

}

//---------------------------------------------------------------------
// Virtual function to allow the user to carry out post processing after
// a config file has been read.  In this case set the global pcWFBK
// pointer to point to the object.  NOTE this means that pcWFBK points
// to the last width object configured.
//---------------------------------------------------------------------
bool        cWFBK::Post_Config(
                        char *name,         // name of schema
                        void *psStruct)     // address of binary structure
                                            // holding configured data
{
    pcWFBK = (cWFBK *)(this);
    return true;
}


// --------------------------------------------------------------------------
// This function returns the updated width vernier
//
//   Configured Variables:
//
// --------------------------------------------------------------------------
float   cWFBK::Vern_Upd(
                 float   *err_queue,    // IN [mm_in] error queue array
                 float   *wgt_queue,    // IN [mm_in] weight queue array
                 float   gain,          // IN [-]     adaptation gain
                 float   vern_init      // IN [mm_in] initial vernier
                 )                      // current vernier to be calculated
{
    // Local variables
    float   err_wgt = 0.0F;
    float   wgt     = 0.0F;
    float   vern_cur;

    for ( int i=0; i < num_wid_err; i++)
    {
        err_wgt += err_queue[i] * wgt_queue[i];
        wgt += wgt_queue[i];
    }

    // -------------------------
    // Calculate the new vernier
    // -------------------------
    vern_cur = (err_wgt/wgt) * gain + vern_init * ( 1.0F - gain );

    return vern_cur;

} // cWfbk::Vrn_Upd



// -----------------------------------------------------------------------------
// This function replaces the 1st element of queue with the "error" and pushes
// out the last element out of the queue.
//
//   Configured Variables:
//
// ------------------------------------------------------------------------------
bool    cWFBK::Queue_Upd(
                 float   error,     // IN     [mm_in] width/spread error
                 float   *queue    // IN OUT [mm_in] error queue, RM or FM
                 )
{

    for ( int i = num_wid_err-1; i > 0; i--)
    {
        queue[i] = queue[i-1];
    }
    queue[0] = error;

    return true;

} // cWfbk::Queue_Upd


// -----------------------------------------------------------------------------
// This function initializes all elements of queue with the "error".
//
//   Configured Variables:
//   queue_size             // [-] Array size
// ------------------------------------------------------------------------------
bool   cWFBK::Queue_Init(
                 float   error,     // IN     [mm_in] width/spread error
                 float   *queue     // IN OUT [mm_in] error queue, RM or FM
                 )
{

    for ( int i = 0; i < num_wid_err; i++)
    {
        queue[i] = error;
    }

    return true;

} // cWfbk::Queue_Init

//-------------------------------
// Roughing Mill width feedback
//-------------------------------
bool    cWFBK::Rmx_Fbk(
                cSched  *pcSched    // pointer to Sched
                      )
{
    // local variables
    bool    rx_wdg_vld  = false;
    int     iseg_body   = pcObjChain->Body_Index();
    float   rmx_wid     = 0.0F;
    float   rmerr       = 0.0F;

	//------------------------------------------------
    // Reset RM width feedback adaptation status flag
    //------------------------------------------------
    pcSched->pcFeedbackD->rm_wfbk_vld = false;

    //-------------------------------------------------------------------------
    // Assign measured width and width gauge status at RM exit.
    //-------------------------------------------------------------------------
    rmx_wid    = pcSched->pcFbSensorCfgD[iseg_body]->pcRMXWidthD->bd_width;
    rx_wdg_vld = pcSched->pcFbSensorCfgD[iseg_body]->pcRMXWidthD->bd_status;

    //-------------------------------------
    // Check measured width and status flag
    //-------------------------------------
    if ( 0.0F == rmx_wid || !rx_wdg_vld )
    {
        DMSG(-diagLvl)
            << "  WFBK.Rmx_Fbk: bad RMX width data rx_wdg_vld = false, "
            << "  prod_id= " << pcSched->objName()
            << END_OF_MESSAGE;
    }

    //-----------------------------------------------------------------------------
    // Get repredicted RMX width, via pyro sensor objects.  This width is at the
    // repredicted temperature and should be converted to measured pyro expansion.
    //-----------------------------------------------------------------------------
    float   exp_ratio = pcSched->pcFbSensorCfgD[iseg_body]->pcRMXTempTargtD->Expansion() /   // meas/rep
                        pcSched->pcFbSensorCfgD[iseg_body]->pcRMXPyroD->pcEnPceD->Expansion();

    float   rep_rmx_wid = pcSched->pcFbSensorCfgD[iseg_body]->pcRMXTempTargtD->width * exp_ratio;

    //------------------------
    // Calculate width error
    //------------------------
    rmerr = rep_rmx_wid - rmx_wid;

    //------------------------------------------------
    // Capture RMX width deviation and error
    //------------------------------------------------
    pcSched->pcFeedbackD->rxw_dev = pcSched->pcFbSensorCfgD[iseg_body]->pcRMXWidthD->bd_width
                                  - pcSched->pcSuSensorCfgD[iseg_body]->pcRMXWidthD->targ;
    pcSched->pcFeedbackD->rxw_err = rmerr;

    //------------------------------------------------
    // If there is an UDREDRAFT/OVERDRAFT condition,
    // just alarm and do not update width vernier
    //------------------------------------------------
    if ( rx_wdg_vld )
    {
        if (( el_overdraft == pcSched->pcESUD->esu_status ) &&
            ( rmerr < -this->vld_wid_tol ) )    // less than zero or a small negative value
        {
            //----------------------------------------------
            // Overdraft and meas. width wider than target
            //----------------------------------------------
            DMSG(-diagLvl)
                << "  WFBK.Rmx_Fbk: no RM width vernier update- ESU OVERDRAFT, "
                << "  prod_id= " << pcSched->objName()
                << END_OF_MESSAGE;

            return true;
        }
        else if (( el_underdraft == pcSched->pcESUD->esu_status ) &&
                 ( rmerr > this->vld_wid_tol ) )    // more than zero or a small positive value
        {
            //--------------------------------------------------
            // Underdraft and meas. width narrower than target
            //--------------------------------------------------
            DMSG(-diagLvl)
                << "  WFBK.Rmx_Fbk: no RM width vernier update- ESU UNDERDRAFT, "
                << "  prod_id= " << pcSched->objName()
                << END_OF_MESSAGE;

            return true;
        }
    }

    //--------------------------------------------------------------
    // Update RM width vernier only, if width gauge data is valid,
    // stand/edger screws went to setup position and tracking ok.
    //--------------------------------------------------------------
    if ( rx_wdg_vld )
//    IF s_ptr.input.fbk.lwidrm    AND
//       s_ptr.fbk.rm_eval.rm_trk  AND
//       s_ptr.fbk.rm_eval.rgr_pos AND
//       s_ptr.fbk.rm_eval.edg_pos     THEN
    {
        //--------------------------
        // Limit check width error
        //--------------------------
        rmerr = cMathUty::Clamp( rmerr,
                                 this->rm_vern_low,
                                 this->rm_vern_hi );

        //-----------------------------
        // Update RM width error queue
        //-----------------------------
        Queue_Upd(
            rmerr,                      // IN     [mm_in] width/spread error
            pcSched->pcRAMP->fbk_state.rmx_wid_err
                                        // IN OUT [mm_in] error queue, RM or FM
                 );

        //-------------------------
        // Update RM width vernier
        //-------------------------
        pcSched->pcRAMP->fbk_state.rmx_wid_vern =
            Vern_Upd(
                 pcSched->pcRAMP->fbk_state.rmx_wid_err,    // IN [mm_in] error queue array
                 this->rm_wgt_queue,                        // IN [mm_in] weight queue array
                 this->rm_gain,                             // IN [-]     adaptation gain
                 pcSched->pcRAMP->fbk_state.rmx_wid_vern    // IN [mm_in] initial vernier
                     );                                      // current vernier to be calculated


		
		//--------------------------
        // Limit check width Vernier
        //--------------------------
        pcSched->pcRAMP->fbk_state.rmx_wid_vern =
            cMathUty::Clamp( pcSched->pcRAMP->fbk_state.rmx_wid_vern,
                             this->rm_vern_low,
                             this->rm_vern_hi );

        //------------------------------------------------------------
        // No vernier update if simulation mode, shadow mode,
        // or width adaptation disabled
        //------------------------------------------------------------
        if ( !pcSched->pcRSys->state.sim_mode               &&
             !pcSched->pcFeedbackD->pcFeedback->shadow_mode &&
              this->rm_vern_enab )
        {
            // update width vernier and width error queue
            pcSched->pcRAMP->adapted_state.rmx_wid_vern =
                              pcSched->pcRAMP->fbk_state.rmx_wid_vern;

            for ( int i = 0; i < num_wid_err; i++)
            {
                pcSched->pcRAMP->adapted_state.rmx_wid_err[i] =
                              pcSched->pcRAMP->fbk_state.rmx_wid_err[i];
            }

            pcSched->pcFeedbackD->rm_wfbk_vld = true;

            return true;
        }
        else
        {
            DMSG(-diagLvl)
                << "WFBK.Rmx_Fbk: simulation/shadow mode or width adaptation disabled, "
                << "prod_id= " << pcSched->objName()
                << END_OF_MESSAGE;

            return  true;
        }

    }   //  pcSched->pcMDLFbk->rx_wdg_vld
    else
    {

        DMSG(-diagLvl)
            << "  WFBK.Rmx_Fbk: No RM width vernier adaptation, INVALID wdg data, "
            << "  prod_id= " << pcSched->objName()
            << END_OF_MESSAGE;

        return true;

    }   // end if ( rx_wdg_vld )

//    return false;

} // cWfbk::Rmx_Fbk


//-------------------------------
// Finishing Mill width feedback
//-------------------------------
bool    cWFBK::Fmx_Fbk(
                cSched  *pcSched    // pointer to Sched
                      )
{
    // local variable
    bool    fx_wdg_vld  = false;
    bool    rx_wdg_vld  = false;
    int     iseg_body   = pcObjChain->Body_Index();
    float   spread_err  = 0.0F;
    float   wid_dev     = 0.0F;
    float   rmx_wid     = 0.0F;
    float   fmx_wid     = 0.0F;

    //------------------------------------------------
    // Reset FM width feedback adaptation status flag
    //------------------------------------------------
    pcSched->pcFeedbackD->fm_wfbk_vld = false;

    //-------------------------------------------------------------------------
    // Assign RMX measured or repredicted width as well as width gauge status.
    //-------------------------------------------------------------------------
    rmx_wid = pcSched->pcFbSensorCfgD[iseg_body]->pcRMXWidthTargtD->width;
    rx_wdg_vld = pcSched->pcFbSensorCfgD[iseg_body]->pcRMXWidthD->bd_status;

    //-------------------------------------------------------------------------
    // Assign measured width and width gauge status at FM exit.
    //-------------------------------------------------------------------------
    fmx_wid    = pcSched->pcFbSensorCfgD[iseg_body]->pcFMXWidthD->bd_width;
    fx_wdg_vld = pcSched->pcFbSensorCfgD[iseg_body]->pcFMXWidthD->bd_status;

    //---------------------------------------------------------------
    // Return w/o any updates, if RMX or FMX measured width is zero.
    //---------------------------------------------------------------
    if ( 0.0F == rmx_wid || 0.0F == fmx_wid )
    {

        DMSG(-diagLvl)
            << "  WFBK.Fmx_Fbk: RMX or FMX measured width is ZERO, "
            << "  prod_id= " << pcSched->objName()
            << END_OF_MESSAGE;

        return true;
    }

    //----------------------------------------------------
    // Calculate repredicted fm spread amount.
    // Translate all dimensions to RMX temperature.
    //----------------------------------------------------
    pcSched->pcFeedbackD->fm_spread =
                    pcSched->pcESUD->Get_Fm_Spread( pcSched->pcFbSensorCfgD[iseg_body], pcSched );  //add  pcSched 20150319

    //----------------------------------------------------
    // Calculate measured fm spread amount
    //----------------------------------------------------
    float lin_exp_rat = pcSched->pcFbSensorCfgD[iseg_body]->pcRMXWidthTargtD->Expansion() /
                        pcSched->pcFbSensorCfgD[iseg_body]->pcFMXWidthTargtD->Expansion();

    pcSched->pcFeedbackD->fm_spread_m = fmx_wid * lin_exp_rat - rmx_wid;

#if INCLUDE_FME_EDGER
    int lstpas = pcSched->pcSetupD->lstpas;
    //------------------------------------------------------------
    // Calculate a new measured fm spread, considering a F0 Edger.
    // Convert the (draft - recov) to RMX target temperature.
    // fm_spread  = FXW - RXW + F0_edger net effect
    //------------------------------------------------------------
    if ( (pcSched->pcFbkPassD[lstpas]->pcExPceD[iseg_body]->Expansion() > 1.0F)   &&
         (pcSched->pcFbkPassD[lstpas]->pcEnEdgD[iseg_body]->draft > Physcon.tol2) &&
         (pcSched->pcFbkPassD[lstpas]->pcEnEdgD[iseg_body]->pcExPceD->recovery > Physcon.tol2) )
    {
        pcSched->pcFeedbackD->fm_spread_m +=
                        (  pcSched->pcFbkPassD[lstpas]->pcEnEdgD[iseg_body]->draft
                         - pcSched->pcFbkPassD[lstpas]->pcEnEdgD[iseg_body]->pcExPceD->recovery )
                         * pcSched->pcFbSensorCfgD[iseg_body]->pcRMXWidthTargtD->Expansion()
                         / pcSched->pcFbkPassD[lstpas]->pcExPceD[iseg_body]->Expansion();
    }
#endif

    //----------------------------------------------------------------------------
    // Calculate the actual width deviation
    // Take into account the operator width offset for setting the wdg reference
    //----------------------------------------------------------------------------
    wid_dev = pcSched->pcSuSensorCfgD[iseg_body]->pcFMXWidthD->targ - fmx_wid;

    //--------------------------------------------------
    // update spread vernier, using repredicted spread
    //--------------------------------------------------
    spread_err = pcSched->pcFeedbackD->fm_spread // + wcfh
               - pcSched->pcFeedbackD->fm_spread_m;

    //------------------------------------------------
    // Capture FMX width deviation and spread error
    //------------------------------------------------
    pcSched->pcFeedbackD->fxw_dev = -wid_dev;
    pcSched->pcFeedbackD->fxw_err = spread_err;

    //--------------------------------------------------------
    // Return w/o any updates, if width gauge data is NOT OK
    //--------------------------------------------------------
    if ( !fx_wdg_vld )
    {

        DMSG(-diagLvl)
            << "  WFBK.Fmx_Fbk: bad FMX width data fx_wdg_vld = false, "
            << "  prod_id= " << pcSched->objName()
            << END_OF_MESSAGE;

        return true;
    }
    else if ( !rx_wdg_vld )
    {

        DMSG(-diagLvl)
            << "  WFBK.Fmx_Fbk: bad RMX width data rx_wdg_vld = false, "
            << "  prod_id= " << pcSched->objName()
            << END_OF_MESSAGE;

        return true;
    }

    //------------------------------------------------
    // If there is an UDREDRAFT/OVERDRAFT condition,
    // just alarm and do not update width vernier
    //------------------------------------------------
    if (( el_overdraft == pcSched->pcESUD->esu_status ) &&
        ( wid_dev < -this->vld_wid_tol ) )    // less than zero or a small negative value
    {
        //----------------------------------------------
        // Overdraft and meas. width wider than target
        //----------------------------------------------
        DMSG(-diagLvl)
            << "  WFBK.Fmx_Fbk: no FM width vernier update- ESU OVERDRAFT, "
            << "  prod_id= " << pcSched->objName()
            << END_OF_MESSAGE;

        return true;
    }
    else if (( el_underdraft == pcSched->pcESUD->esu_status ) &&
             ( wid_dev > this->vld_wid_tol ) )   // more than zero or a small positive value
    {
        //--------------------------------------------------
        // Underdraft and meas. width narrower than target
        //--------------------------------------------------
         DMSG(-diagLvl)
            << "  WFBK.Fmx_Fbk: no FM width vernier update- ESU UNDERDRAFT, "
            << "  prod_id= " << pcSched->objName()
            << END_OF_MESSAGE;

         return true;
    }


    //--------------------------------------------------------------
    // Update FM width vernier only, if width gauge data is valid,
    // stand/edger screws went to setup position and tracking ok.
    //--------------------------------------------------------------
    if ( fx_wdg_vld )
//    IF s_ptr.input.fbk.lwidfm    AND
//       s_ptr.fbk.rm_eval.rm_trk  AND
//       s_ptr.fbk.rm_eval.rgr_pos AND
//       s_ptr.fbk.rm_eval.edg_pos     THEN
    {
        //--------------------------
        // Limit check width error
        //--------------------------
        spread_err = cMathUty::Clamp( spread_err,
                                      this->fm_vern_low,
                                      this->fm_vern_hi );

        //--------------------------------------------------------------
        // Note: width_dev = setup predicted target - measured
        // The following condition indicates that error is in the RM,
        // thus, hold the FM vernier
        //--------------------------------------------------------------
        if ( ( spread_err > pcSched->pcRAMP->fbk_state.fmx_wid_vern &&
                                                    wid_dev < 0.0F ) ||
             ( spread_err < pcSched->pcRAMP->fbk_state.fmx_wid_vern &&
                                                    wid_dev > 0.0F ) )
        {
            spread_err = pcSched->pcRAMP->fbk_state.fmx_wid_vern;

             DMSG(-diagLvl)
                << "  WFBK.Fmx_Fbk: no FM width vernier update- Width error is in RM, "
                << "  prod_id= " << pcSched->objName()
                << END_OF_MESSAGE;
        }

        //-----------------------------
        // Update FM width error queue
        //-----------------------------
        Queue_Upd(
            spread_err,                 // IN     [mm_in] width/spread error
            pcSched->pcRAMP->fbk_state.fmx_wid_err
                                        // IN OUT [mm_in] error queue, RM or FM
                 );

        //-------------------------
        // Update FM width vernier
        //-------------------------
        pcSched->pcRAMP->fbk_state.fmx_wid_vern =
            Vern_Upd(
                 pcSched->pcRAMP->fbk_state.fmx_wid_err,    // IN [mm_in] error queue array
                 this->fm_wgt_queue,                        // IN [mm_in] weight queue array
                 this->fm_gain,                             // IN [-]     adaptation gain
                 pcSched->pcRAMP->fbk_state.fmx_wid_vern    // IN [mm_in] initial vernier
                     );                                     // current vernier to be calculated

        //--------------------------
        // Limit check width vernier
        //--------------------------
        pcSched->pcRAMP->fbk_state.fmx_wid_vern =
            cMathUty::Clamp( pcSched->pcRAMP->fbk_state.fmx_wid_vern,
                             this->fm_vern_low,
                             this->fm_vern_hi );

        //------------------------------------------------------------
        // No vernier update if simulation mode, shadow mode,
        // or width adaptation disabled
        //------------------------------------------------------------
        if ( !pcSched->pcRSys->state.sim_mode               &&
             !pcSched->pcFeedbackD->pcFeedback->shadow_mode &&
              this->fm_vern_enab )
        {
            // update width vernier and width error queue
            pcSched->pcRAMP->adapted_state.fmx_wid_vern =
                              pcSched->pcRAMP->fbk_state.fmx_wid_vern;

            for ( int i = 0; i < num_wid_err; i++)
            {
                pcSched->pcRAMP->adapted_state.fmx_wid_err[i] =
                              pcSched->pcRAMP->fbk_state.fmx_wid_err[i];
            }

            pcSched->pcFeedbackD->fm_wfbk_vld = true;

            return true;
        }
        else
        {
            DMSG(-diagLvl)
                << "WFBK.Fmx_Fbk: simulation/shadow mode or width adaptation disabled, "
                << "prod_id= " << pcSched->objName()
                << END_OF_MESSAGE;

            return  true;
        }
    }
    else
    {

        DMSG(-diagLvl)
            << "  WFBK.Fmx_Fbk: No FM width vernier adaptation, INVALID wdg data, "
            << "  prod_id= " << pcSched->objName()
            << END_OF_MESSAGE;

        return true;
    }   // end if ( fx_wdg_vld )

//    return false;

} // cWfbk::Fmx_Fbk
