# 宽度偏差变量分析报告

## 1. 变量位置及使用

主要在以下文件中使用：
1. esud.py
2. Dist_RMWid_Vern.py
3. wfbk.txt（参考源码）
4. esu_calculated.txt（参考源码）

## 2. 变量作用

### 2.1 rmx_wid_vern（粗轧宽度偏差）

1. 存储位置：
   - 存储在sched.ramp.state中
   - 同时在fbk_state和adapted_state中也有记录

2. 使用场景：
   ```python
   width_sensor.wid_offset = sched.ramp.state.rmx_wid_vern
   ```
   主要用于设置宽度传感器的偏移值

3. 工作流程：
   - 在初始化阶段分配到各个轧机道次
   - 影响宽度传感器的测量校正
   - 参与宽度控制的反馈调节

4. 特殊处理：
   ```python
   width_sensor.wid_offset = (
       sched.ramp.state.rmx_wid_vern * 
       (1 - sched.rapp.state.rwid_off_mult) +
       sched.rapp.state.rmx_wid_off * 
       sched.rapp.state.rwid_off_mult
   )
   ```
   在某些情况下会与其他参数组合使用

### 2.2 fm_vernier（精轧宽度偏差）

主要在精轧机组使用，但在当前代码中未直接体现。

## 3. 宽度控制中的作用

### 3.1 宽度偏差的应用流程

1. 初始化阶段：
   - 通过Initialize()函数进行初始分配
   - 设置各个宽度传感器的初始偏移值

2. 运行时调整：
   - 可以手动调整rmx_wid_vern值
   - 系统会自动将调整分配到相关设备

3. 反馈控制：
   - 作为闭环控制的重要参数
   - 参与宽度误差补偿计算

### 3.2 人工干预机制

1. 触发条件：
   - 模型控制效果不理想
   - 实际宽度与目标值偏差过大
   - 需要快速调整宽度

2. 调整方式：
   - 直接修改rmx_wid_vern值
   - 系统会自动将新的偏差值分配到各个控制点

3. 影响范围：
   - 影响所有下游宽度传感器的测量补偿
   - 影响后续道次的宽度控制策略

## 4. 关键代码分析

### 4.1 宽度偏差分配
```python
# 在dist_rm_wid_vern方法中
if entry_side:
    if ps <= 1:
        width_sensor.wid_offset = 0.0
    else:
        if (sched.ramp.adapted_state.family_prv != 
            sched.rapp.state.family):
            width_sensor.wid_offset = (
                sched.ramp.state.rmx_wid_vern * 
                (1 - sched.rapp.state.rwid_off_mult) +
                sched.rapp.state.rmx_wid_off * 
                sched.rapp.state.rwid_off_mult
            )
        else:
            width_sensor.wid_offset = sched.ramp.state.rmx_wid_vern
```

### 4.2 状态管理
```python
# 在状态更新中
pcSched.ramp.fbk_state.rmx_wid_vern = new_value
pcSched.ramp.adapted_state.rmx_wid_vern = pcSched.ramp.fbk_state.rmx_wid_vern
```

## 5. 使用建议

1. 手动调整时机：
   - 当自动控制效果不理想时
   - 出现明显的系统偏差时
   - 需要快速响应时

2. 调整注意事项：
   - 需考虑整体工艺要求
   - 避免过大调整造成波动
   - 关注调整后的效果反馈

3. 监控要点：
   - 关注宽度测量值的变化
   - 观察系统响应情况
   - 记录调整效果

## 6. 总结

rmx_wid_vern和fm_vernier是宽度控制系统中的关键参数，既支持自动控制也允许人工干预。合理使用这两个参数可以提高宽度控制的精确性和灵活性。在进行人工干预时，需要结合实际工况，谨慎调整，避免造成过大波动。
